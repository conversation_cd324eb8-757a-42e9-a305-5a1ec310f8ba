{"version": 3, "file": "retryWhen.js", "sourceRoot": "", "sources": ["../../../../src/internal/operators/retryWhen.ts"], "names": [], "mappings": ";;;AACA,qDAAoD;AACpD,sCAAqC;AAIrC,qCAAuC;AACvC,2DAAgE;AA2DhE,SAAgB,SAAS,CAAI,QAA2D;IACtF,OAAO,cAAO,CAAC,UAAC,MAAM,EAAE,UAAU;QAChC,IAAI,QAA6B,CAAC;QAClC,IAAI,SAAS,GAAG,KAAK,CAAC;QACtB,IAAI,OAAqB,CAAC;QAE1B,IAAM,qBAAqB,GAAG;YAC5B,QAAQ,GAAG,MAAM,CAAC,SAAS,CACzB,6CAAwB,CAAC,UAAU,EAAE,SAAS,EAAE,SAAS,EAAE,UAAC,GAAG;gBAC7D,IAAI,CAAC,OAAO,EAAE;oBACZ,OAAO,GAAG,IAAI,iBAAO,EAAE,CAAC;oBACxB,qBAAS,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,SAAS,CACpC,6CAAwB,CAAC,UAAU,EAAE;wBAMnC,OAAA,QAAQ,CAAC,CAAC,CAAC,qBAAqB,EAAE,CAAC,CAAC,CAAC,CAAC,SAAS,GAAG,IAAI,CAAC;oBAAvD,CAAuD,CACxD,CACF,CAAC;iBACH;gBACD,IAAI,OAAO,EAAE;oBAEX,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;iBACnB;YACH,CAAC,CAAC,CACH,CAAC;YAEF,IAAI,SAAS,EAAE;gBAKb,QAAQ,CAAC,WAAW,EAAE,CAAC;gBACvB,QAAQ,GAAG,IAAI,CAAC;gBAEhB,SAAS,GAAG,KAAK,CAAC;gBAElB,qBAAqB,EAAE,CAAC;aACzB;QACH,CAAC,CAAC;QAGF,qBAAqB,EAAE,CAAC;IAC1B,CAAC,CAAC,CAAC;AACL,CAAC;AA9CD,8BA8CC"}