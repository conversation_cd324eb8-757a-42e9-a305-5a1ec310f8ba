# AP3X Platform Environment Configuration
# Copy this file to .env and update the values

# =============================================================================
# CORE PLATFORM SETTINGS
# =============================================================================

# Server Configuration
PORT=3000
HOST=localhost
NODE_ENV=development

# =============================================================================
# AI MODEL CONFIGURATION
# =============================================================================

# OpenAI Configuration (Required for AIDER)
OPENAI_API_KEY=your-openai-api-key-here

# AIDER Configuration
AIDER_MODEL=gpt-4
PYTHON_PATH=python
AIDER_WORKING_DIR=./workspace

# =============================================================================
# CONTEXT ENGINE CONFIGURATION
# =============================================================================

# Context Engine API
CONTEXT_ENGINE_URL=http://localhost:3001

# Neo4j Database (for advanced context understanding)
NEO4J_URL=bolt://localhost:7687
NEO4J_USER=neo4j
NEO4J_PASSWORD=password

# =============================================================================
# SECURITY CONFIGURATION
# =============================================================================

# JWT Secret (Change this in production!)
JWT_SECRET=ap3x-platform-secret-key-change-in-production

# CORS Origins (comma-separated)
CORS_ORIGINS=http://localhost:3000,http://localhost:3001

# =============================================================================
# FEATURE FLAGS
# =============================================================================

# Enable/Disable Features
ENABLE_REAL_TIME_COLLABORATION=true
ENABLE_VISUAL_WORKFLOWS=true
ENABLE_USER_MANAGEMENT=true
ENABLE_ADVANCED_ANALYTICS=true
ENABLE_AUDIT_LOGGING=true

# =============================================================================
# ENTERPRISE FEATURES
# =============================================================================

# SSO Configuration (Optional)
ENABLE_SSO=false
SSO_PROVIDER=google
SSO_CLIENT_ID=your-sso-client-id
SSO_CLIENT_SECRET=your-sso-client-secret

# =============================================================================
# MONITORING & LOGGING
# =============================================================================

# Log Level (error, warn, info, debug)
LOG_LEVEL=info

# Log File Path
LOG_FILE=./logs/ap3x.log

# =============================================================================
# PERFORMANCE TUNING
# =============================================================================

# Maximum concurrent agents
MAX_CONCURRENT_AGENTS=10

# Session timeout (in minutes)
SESSION_TIMEOUT=480

# Rate limiting
ENABLE_RATE_LIMIT=true
RATE_LIMIT_WINDOW=15
RATE_LIMIT_MAX_REQUESTS=100

# =============================================================================
# DEVELOPMENT SETTINGS
# =============================================================================

# Debug Mode
DEBUG=false

# Hot Reload
HOT_RELOAD=true

# Mock External Services (for development)
MOCK_EXTERNAL_SERVICES=false

# =============================================================================
# PRODUCTION SETTINGS
# =============================================================================

# Database URLs (for production)
DATABASE_URL=postgresql://user:password@localhost:5432/ap3x
REDIS_URL=redis://localhost:6379

# External Service URLs
WEBHOOK_URL=https://your-domain.com/webhooks
NOTIFICATION_SERVICE_URL=https://notifications.your-domain.com

# SSL Configuration
SSL_CERT_PATH=./certs/cert.pem
SSL_KEY_PATH=./certs/key.pem

# =============================================================================
# CLOUD DEPLOYMENT
# =============================================================================

# AWS Configuration (if deploying to AWS)
AWS_REGION=us-east-1
AWS_ACCESS_KEY_ID=your-aws-access-key
AWS_SECRET_ACCESS_KEY=your-aws-secret-key
S3_BUCKET=ap3x-storage

# Docker Configuration
DOCKER_REGISTRY=your-registry.com
DOCKER_IMAGE_TAG=latest

# =============================================================================
# MONITORING & OBSERVABILITY
# =============================================================================

# Application Performance Monitoring
APM_SERVICE_NAME=ap3x-platform
APM_SERVER_URL=https://apm.your-domain.com

# Metrics Collection
METRICS_ENABLED=true
METRICS_PORT=9090

# Health Check Configuration
HEALTH_CHECK_INTERVAL=30000
HEALTH_CHECK_TIMEOUT=5000
