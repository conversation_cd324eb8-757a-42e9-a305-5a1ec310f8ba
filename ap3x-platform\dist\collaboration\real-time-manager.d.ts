/**
 * AP3X Platform - Real-time Collaboration Manager
 *
 * Manages real-time collaboration features including live sessions,
 * agent status updates, collaborative editing, and conflict resolution.
 *
 * Features:
 * - Live session management
 * - Real-time agent status tracking
 * - Collaborative editing with conflict resolution
 * - Live cursor tracking
 * - Voice/video integration ready
 * - Presence awareness
 */
import { EventEmitter } from 'events';
import { Server as SocketIOServer, Socket } from 'socket.io';
export interface CollaborationSession {
    sessionId: string;
    projectId: string;
    participants: Participant[];
    activeFiles: ActiveFile[];
    agents: ActiveAgent[];
    settings: SessionSettings;
    createdAt: Date;
    lastActivity: Date;
}
export interface Participant {
    userId: string;
    username: string;
    role: 'owner' | 'collaborator' | 'viewer';
    status: 'online' | 'away' | 'offline';
    cursor?: CursorPosition;
    permissions: Permission[];
    joinedAt: Date;
    lastSeen: Date;
}
export interface ActiveFile {
    path: string;
    content: string;
    version: number;
    locks: FileLock[];
    cursors: Map<string, CursorPosition>;
    lastModified: Date;
    modifiedBy: string;
}
export interface ActiveAgent {
    agentId: string;
    agentType: string;
    status: 'idle' | 'working' | 'waiting' | 'error';
    currentTask?: string;
    progress: number;
    assignedFiles: string[];
    lastUpdate: Date;
}
export interface CursorPosition {
    file: string;
    line: number;
    column: number;
    selection?: {
        startLine: number;
        startColumn: number;
        endLine: number;
        endColumn: number;
    };
}
export interface FileLock {
    userId: string;
    type: 'read' | 'write' | 'exclusive';
    startLine?: number;
    endLine?: number;
    acquiredAt: Date;
    expiresAt: Date;
}
export interface Permission {
    action: 'read' | 'write' | 'execute' | 'admin';
    resource: 'files' | 'agents' | 'session' | 'settings';
    scope?: string[];
}
export interface SessionSettings {
    allowConcurrentEditing: boolean;
    autoSave: boolean;
    autoSaveInterval: number;
    conflictResolution: 'manual' | 'auto' | 'agent';
    maxParticipants: number;
    requireApproval: boolean;
    enableVoiceChat: boolean;
    enableVideoChat: boolean;
}
export interface EditOperation {
    id: string;
    userId: string;
    file: string;
    type: 'insert' | 'delete' | 'replace';
    position: {
        line: number;
        column: number;
    };
    content?: string;
    length?: number;
    timestamp: Date;
    version: number;
}
export interface Conflict {
    id: string;
    file: string;
    operations: EditOperation[];
    participants: string[];
    type: 'concurrent_edit' | 'agent_conflict' | 'version_mismatch';
    status: 'pending' | 'resolved' | 'escalated';
    createdAt: Date;
    resolvedAt?: Date;
    resolution?: ConflictResolution;
}
export interface ConflictResolution {
    strategy: 'accept_all' | 'accept_user' | 'accept_agent' | 'merge' | 'manual';
    chosenUserId?: string;
    mergedContent?: string;
    notes?: string;
}
/**
 * Real-time Collaboration Manager
 */
export declare class RealTimeManager extends EventEmitter {
    private io;
    private sessions;
    private userSockets;
    private fileVersions;
    private pendingOperations;
    private conflicts;
    private isInitialized;
    constructor(io: SocketIOServer);
    /**
     * Initialize real-time collaboration
     */
    initialize(): Promise<void>;
    /**
     * Setup socket event handlers
     */
    private setupSocketHandlers;
    /**
     * Create collaboration session
     */
    createCollaborationSession(sessionId: string, projectId: string, ownerId: string, settings?: Partial<SessionSettings>): Promise<CollaborationSession>;
    /**
     * Add participant to session
     */
    addParticipantToSession(sessionId: string, userId: string, socket: Socket): Promise<void>;
    /**
     * Remove participant from session
     */
    removeParticipantFromSession(sessionId: string, userId: string): Promise<void>;
    /**
     * Handle cursor updates
     */
    private handleCursorUpdate;
    /**
     * Handle edit operations
     */
    private handleEditOperation;
    /**
     * Process edit operations and detect conflicts
     */
    private processEditOperations;
    /**
     * Detect edit conflicts
     */
    private detectEditConflicts;
    /**
     * Handle edit conflicts
     */
    private handleEditConflicts;
    /**
     * Apply edit operations
     */
    private applyEditOperations;
    /**
     * Apply single operation to content
     */
    private applyOperation;
    /**
     * Request file lock
     */
    private requestFileLock;
    /**
     * Release file lock
     */
    private releaseFileLock;
    /**
     * Handle agent status updates
     */
    private handleAgentStatusUpdate;
    /**
     * Handle user disconnect
     */
    private handleUserDisconnect;
    /**
     * Utility methods
     */
    private validateUserToken;
    private getDefaultPermissions;
    private hasPermission;
    private canEditFile;
    /**
     * Start cleanup intervals
     */
    private startCleanupIntervals;
    /**
     * Get session
     */
    getSession(sessionId: string): CollaborationSession | undefined;
    /**
     * Get all sessions
     */
    getAllSessions(): CollaborationSession[];
    /**
     * Shutdown manager
     */
    shutdown(): Promise<void>;
}
declare module 'socket.io' {
    interface Socket {
        userId?: string;
    }
}
export default RealTimeManager;
//# sourceMappingURL=real-time-manager.d.ts.map