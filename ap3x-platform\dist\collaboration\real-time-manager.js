"use strict";
/**
 * AP3X Platform - Real-time Collaboration Manager
 *
 * Manages real-time collaboration features including live sessions,
 * agent status updates, collaborative editing, and conflict resolution.
 *
 * Features:
 * - Live session management
 * - Real-time agent status tracking
 * - Collaborative editing with conflict resolution
 * - Live cursor tracking
 * - Voice/video integration ready
 * - Presence awareness
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.RealTimeManager = void 0;
const events_1 = require("events");
/**
 * Real-time Collaboration Manager
 */
class RealTimeManager extends events_1.EventEmitter {
    constructor(io) {
        super();
        this.sessions = new Map();
        this.userSockets = new Map();
        this.fileVersions = new Map();
        this.pendingOperations = new Map();
        this.conflicts = new Map();
        this.isInitialized = false;
        this.io = io;
    }
    /**
     * Initialize real-time collaboration
     */
    async initialize() {
        if (this.isInitialized)
            return;
        console.log('🔄 Initializing Real-time Collaboration Manager...');
        try {
            // Setup socket event handlers
            this.setupSocketHandlers();
            // Start cleanup intervals
            this.startCleanupIntervals();
            this.isInitialized = true;
            this.emit('initialized');
            console.log('✅ Real-time Collaboration Manager initialized successfully');
        }
        catch (error) {
            console.error('❌ Failed to initialize Real-time Collaboration Manager:', error);
            throw error;
        }
    }
    /**
     * Setup socket event handlers
     */
    setupSocketHandlers() {
        this.io.on('connection', (socket) => {
            console.log(`🔌 User connected: ${socket.id}`);
            // Handle user authentication
            socket.on('authenticate', async (data) => {
                try {
                    // Validate user token (implement your auth logic)
                    const isValid = await this.validateUserToken(data.userId, data.token);
                    if (isValid) {
                        this.userSockets.set(data.userId, socket);
                        socket.userId = data.userId;
                        socket.emit('authenticated', { success: true });
                        console.log(`✅ User authenticated: ${data.userId}`);
                    }
                    else {
                        socket.emit('authenticated', { success: false, error: 'Invalid token' });
                    }
                }
                catch (error) {
                    socket.emit('authenticated', { success: false, error: 'Authentication failed' });
                }
            });
            // Handle session joining
            socket.on('join_session', async (data) => {
                if (!socket.userId) {
                    socket.emit('error', { message: 'Not authenticated' });
                    return;
                }
                try {
                    await this.addParticipantToSession(data.sessionId, socket.userId, socket);
                    socket.emit('session_joined', { sessionId: data.sessionId });
                }
                catch (error) {
                    socket.emit('error', { message: error.message });
                }
            });
            // Handle session leaving
            socket.on('leave_session', async (data) => {
                if (!socket.userId)
                    return;
                try {
                    await this.removeParticipantFromSession(data.sessionId, socket.userId);
                    socket.emit('session_left', { sessionId: data.sessionId });
                }
                catch (error) {
                    socket.emit('error', { message: error.message });
                }
            });
            // Handle cursor updates
            socket.on('cursor_update', (data) => {
                if (!socket.userId)
                    return;
                this.handleCursorUpdate(data.sessionId, socket.userId, data.cursor);
            });
            // Handle file editing
            socket.on('edit_operation', async (data) => {
                if (!socket.userId)
                    return;
                try {
                    await this.handleEditOperation(data.sessionId, socket.userId, data.operation);
                }
                catch (error) {
                    socket.emit('error', { message: error.message });
                }
            });
            // Handle file locking
            socket.on('request_lock', async (data) => {
                if (!socket.userId)
                    return;
                try {
                    const lock = await this.requestFileLock(data.sessionId, socket.userId, data.file, data.type);
                    socket.emit('lock_granted', { file: data.file, lock });
                }
                catch (error) {
                    socket.emit('lock_denied', { file: data.file, error: error.message });
                }
            });
            // Handle lock release
            socket.on('release_lock', async (data) => {
                if (!socket.userId)
                    return;
                await this.releaseFileLock(data.sessionId, socket.userId, data.file);
            });
            // Handle agent status updates
            socket.on('agent_status_update', (data) => {
                this.handleAgentStatusUpdate(data.sessionId, data.agentId, data.status);
            });
            // Handle disconnection
            socket.on('disconnect', () => {
                console.log(`🔌 User disconnected: ${socket.id}`);
                if (socket.userId) {
                    this.handleUserDisconnect(socket.userId);
                    this.userSockets.delete(socket.userId);
                }
            });
        });
    }
    /**
     * Create collaboration session
     */
    async createCollaborationSession(sessionId, projectId, ownerId, settings) {
        const defaultSettings = {
            allowConcurrentEditing: true,
            autoSave: true,
            autoSaveInterval: 30000, // 30 seconds
            conflictResolution: 'manual',
            maxParticipants: 10,
            requireApproval: false,
            enableVoiceChat: false,
            enableVideoChat: false,
            ...settings
        };
        const session = {
            sessionId,
            projectId,
            participants: [],
            activeFiles: [],
            agents: [],
            settings: defaultSettings,
            createdAt: new Date(),
            lastActivity: new Date()
        };
        this.sessions.set(sessionId, session);
        this.emit('session_created', { sessionId, session });
        console.log(`🔄 Created collaboration session: ${sessionId}`);
        return session;
    }
    /**
     * Add participant to session
     */
    async addParticipantToSession(sessionId, userId, socket) {
        const session = this.sessions.get(sessionId);
        if (!session) {
            throw new Error(`Session ${sessionId} not found`);
        }
        // Check if user is already in session
        const existingParticipant = session.participants.find(p => p.userId === userId);
        if (existingParticipant) {
            existingParticipant.status = 'online';
            existingParticipant.lastSeen = new Date();
        }
        else {
            // Add new participant
            const participant = {
                userId,
                username: `User-${userId}`, // Get from user service
                role: session.participants.length === 0 ? 'owner' : 'collaborator',
                status: 'online',
                permissions: this.getDefaultPermissions('collaborator'),
                joinedAt: new Date(),
                lastSeen: new Date()
            };
            session.participants.push(participant);
        }
        // Join socket room
        socket.join(sessionId);
        // Send session state to new participant
        socket.emit('session_state', {
            session,
            files: session.activeFiles,
            agents: session.agents
        });
        // Notify other participants
        socket.to(sessionId).emit('participant_joined', {
            userId,
            participant: session.participants.find(p => p.userId === userId)
        });
        session.lastActivity = new Date();
        this.emit('participant_joined', { sessionId, userId });
    }
    /**
     * Remove participant from session
     */
    async removeParticipantFromSession(sessionId, userId) {
        const session = this.sessions.get(sessionId);
        if (!session)
            return;
        // Update participant status
        const participant = session.participants.find(p => p.userId === userId);
        if (participant) {
            participant.status = 'offline';
            participant.lastSeen = new Date();
        }
        // Release all locks held by user
        for (const file of session.activeFiles) {
            file.locks = file.locks.filter(lock => lock.userId !== userId);
        }
        // Notify other participants
        this.io.to(sessionId).emit('participant_left', { userId });
        session.lastActivity = new Date();
        this.emit('participant_left', { sessionId, userId });
    }
    /**
     * Handle cursor updates
     */
    handleCursorUpdate(sessionId, userId, cursor) {
        const session = this.sessions.get(sessionId);
        if (!session)
            return;
        // Update participant cursor
        const participant = session.participants.find(p => p.userId === userId);
        if (participant) {
            participant.cursor = cursor;
        }
        // Update file cursor tracking
        const file = session.activeFiles.find(f => f.path === cursor.file);
        if (file) {
            file.cursors.set(userId, cursor);
        }
        // Broadcast cursor update to other participants
        this.io.to(sessionId).emit('cursor_update', {
            userId,
            cursor
        });
        session.lastActivity = new Date();
    }
    /**
     * Handle edit operations
     */
    async handleEditOperation(sessionId, userId, operation) {
        const session = this.sessions.get(sessionId);
        if (!session) {
            throw new Error(`Session ${sessionId} not found`);
        }
        // Check permissions
        if (!this.hasPermission(session, userId, 'write', 'files')) {
            throw new Error('Insufficient permissions');
        }
        // Check file locks
        const file = session.activeFiles.find(f => f.path === operation.file);
        if (file && !this.canEditFile(file, userId)) {
            throw new Error('File is locked by another user');
        }
        // Add operation to pending queue
        const fileKey = `${sessionId}:${operation.file}`;
        if (!this.pendingOperations.has(fileKey)) {
            this.pendingOperations.set(fileKey, []);
        }
        this.pendingOperations.get(fileKey).push(operation);
        // Process operations
        await this.processEditOperations(sessionId, operation.file);
        session.lastActivity = new Date();
    }
    /**
     * Process edit operations and detect conflicts
     */
    async processEditOperations(sessionId, filePath) {
        const fileKey = `${sessionId}:${filePath}`;
        const operations = this.pendingOperations.get(fileKey) || [];
        if (operations.length === 0)
            return;
        // Sort operations by timestamp
        operations.sort((a, b) => a.timestamp.getTime() - b.timestamp.getTime());
        // Detect conflicts
        const conflicts = this.detectEditConflicts(operations);
        if (conflicts.length > 0) {
            // Handle conflicts
            await this.handleEditConflicts(sessionId, filePath, conflicts);
        }
        else {
            // Apply operations
            await this.applyEditOperations(sessionId, filePath, operations);
        }
        // Clear processed operations
        this.pendingOperations.set(fileKey, []);
    }
    /**
     * Detect edit conflicts
     */
    detectEditConflicts(operations) {
        const conflicts = [];
        // Simple conflict detection - operations on same line within short time window
        for (let i = 0; i < operations.length - 1; i++) {
            for (let j = i + 1; j < operations.length; j++) {
                const op1 = operations[i];
                const op2 = operations[j];
                // Check if operations overlap
                if (op1.file === op2.file &&
                    op1.position.line === op2.position.line &&
                    Math.abs(op1.timestamp.getTime() - op2.timestamp.getTime()) < 5000) { // 5 second window
                    const conflictId = `conflict-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
                    conflicts.push({
                        id: conflictId,
                        file: op1.file,
                        operations: [op1, op2],
                        participants: [op1.userId, op2.userId],
                        type: 'concurrent_edit',
                        status: 'pending',
                        createdAt: new Date()
                    });
                }
            }
        }
        return conflicts;
    }
    /**
     * Handle edit conflicts
     */
    async handleEditConflicts(sessionId, filePath, conflicts) {
        for (const conflict of conflicts) {
            this.conflicts.set(conflict.id, conflict);
            // Notify participants about conflict
            this.io.to(sessionId).emit('edit_conflict', {
                conflict,
                file: filePath
            });
            this.emit('conflict_detected', { sessionId, conflict });
        }
    }
    /**
     * Apply edit operations
     */
    async applyEditOperations(sessionId, filePath, operations) {
        const session = this.sessions.get(sessionId);
        if (!session)
            return;
        // Find or create file
        let file = session.activeFiles.find(f => f.path === filePath);
        if (!file) {
            file = {
                path: filePath,
                content: '',
                version: 1,
                locks: [],
                cursors: new Map(),
                lastModified: new Date(),
                modifiedBy: operations[0]?.userId || 'system'
            };
            session.activeFiles.push(file);
        }
        // Apply operations in order
        for (const operation of operations) {
            // Apply operation to file content
            file.content = this.applyOperation(file.content, operation);
            file.version++;
            file.lastModified = new Date();
            file.modifiedBy = operation.userId;
        }
        // Broadcast file update to all participants
        this.io.to(sessionId).emit('file_updated', {
            file: filePath,
            content: file.content,
            version: file.version,
            operations
        });
        this.emit('file_updated', { sessionId, filePath, operations });
    }
    /**
     * Apply single operation to content
     */
    applyOperation(content, operation) {
        const lines = content.split('\n');
        const { line, column } = operation.position;
        switch (operation.type) {
            case 'insert':
                if (operation.content) {
                    const targetLine = lines[line] || '';
                    lines[line] = targetLine.slice(0, column) + operation.content + targetLine.slice(column);
                }
                break;
            case 'delete':
                if (operation.length) {
                    const targetLine = lines[line] || '';
                    lines[line] = targetLine.slice(0, column) + targetLine.slice(column + operation.length);
                }
                break;
            case 'replace':
                if (operation.content && operation.length) {
                    const targetLine = lines[line] || '';
                    lines[line] = targetLine.slice(0, column) + operation.content + targetLine.slice(column + operation.length);
                }
                break;
        }
        return lines.join('\n');
    }
    /**
     * Request file lock
     */
    async requestFileLock(sessionId, userId, filePath, lockType) {
        const session = this.sessions.get(sessionId);
        if (!session) {
            throw new Error(`Session ${sessionId} not found`);
        }
        let file = session.activeFiles.find(f => f.path === filePath);
        if (!file) {
            file = {
                path: filePath,
                content: '',
                version: 1,
                locks: [],
                cursors: new Map(),
                lastModified: new Date(),
                modifiedBy: userId
            };
            session.activeFiles.push(file);
        }
        // Check for conflicting locks
        const conflictingLocks = file.locks.filter(lock => {
            if (lockType === 'exclusive' || lock.type === 'exclusive') {
                return true;
            }
            if (lockType === 'write' && lock.type === 'write') {
                return true;
            }
            return false;
        });
        if (conflictingLocks.length > 0) {
            throw new Error('File is locked by another user');
        }
        // Create lock
        const lock = {
            userId,
            type: lockType,
            acquiredAt: new Date(),
            expiresAt: new Date(Date.now() + 300000) // 5 minutes
        };
        file.locks.push(lock);
        // Notify other participants
        this.io.to(sessionId).emit('file_locked', {
            file: filePath,
            userId,
            lockType
        });
        return lock;
    }
    /**
     * Release file lock
     */
    async releaseFileLock(sessionId, userId, filePath) {
        const session = this.sessions.get(sessionId);
        if (!session)
            return;
        const file = session.activeFiles.find(f => f.path === filePath);
        if (!file)
            return;
        // Remove user's locks
        file.locks = file.locks.filter(lock => lock.userId !== userId);
        // Notify other participants
        this.io.to(sessionId).emit('file_unlocked', {
            file: filePath,
            userId
        });
    }
    /**
     * Handle agent status updates
     */
    handleAgentStatusUpdate(sessionId, agentId, status) {
        const session = this.sessions.get(sessionId);
        if (!session)
            return;
        let agent = session.agents.find(a => a.agentId === agentId);
        if (!agent) {
            agent = {
                agentId,
                agentType: status.type || 'unknown',
                status: 'idle',
                progress: 0,
                assignedFiles: [],
                lastUpdate: new Date()
            };
            session.agents.push(agent);
        }
        // Update agent status
        Object.assign(agent, status, { lastUpdate: new Date() });
        // Broadcast agent update
        this.io.to(sessionId).emit('agent_status_update', {
            agentId,
            status: agent
        });
        session.lastActivity = new Date();
        this.emit('agent_status_updated', { sessionId, agentId, status: agent });
    }
    /**
     * Handle user disconnect
     */
    handleUserDisconnect(userId) {
        // Update user status in all sessions
        for (const session of this.sessions.values()) {
            const participant = session.participants.find(p => p.userId === userId);
            if (participant) {
                participant.status = 'offline';
                participant.lastSeen = new Date();
                // Release all locks
                for (const file of session.activeFiles) {
                    file.locks = file.locks.filter(lock => lock.userId !== userId);
                }
                // Notify other participants
                this.io.to(session.sessionId).emit('participant_status_changed', {
                    userId,
                    status: 'offline'
                });
            }
        }
    }
    /**
     * Utility methods
     */
    async validateUserToken(userId, token) {
        // Implement your token validation logic
        return true; // Placeholder
    }
    getDefaultPermissions(role) {
        const permissions = [
            { action: 'read', resource: 'files' },
            { action: 'read', resource: 'session' }
        ];
        if (role === 'collaborator' || role === 'owner') {
            permissions.push({ action: 'write', resource: 'files' }, { action: 'execute', resource: 'agents' });
        }
        if (role === 'owner') {
            permissions.push({ action: 'admin', resource: 'session' }, { action: 'admin', resource: 'settings' });
        }
        return permissions;
    }
    hasPermission(session, userId, action, resource) {
        const participant = session.participants.find(p => p.userId === userId);
        if (!participant)
            return false;
        return participant.permissions.some(p => p.action === action && p.resource === resource);
    }
    canEditFile(file, userId) {
        // Check if file has exclusive locks from other users
        const exclusiveLocks = file.locks.filter(lock => lock.type === 'exclusive' && lock.userId !== userId);
        return exclusiveLocks.length === 0;
    }
    /**
     * Start cleanup intervals
     */
    startCleanupIntervals() {
        // Clean up expired locks every minute
        setInterval(() => {
            const now = new Date();
            for (const session of this.sessions.values()) {
                for (const file of session.activeFiles) {
                    file.locks = file.locks.filter(lock => lock.expiresAt > now);
                }
            }
        }, 60000);
        // Clean up inactive sessions every hour
        setInterval(() => {
            const now = new Date();
            const inactiveThreshold = 24 * 60 * 60 * 1000; // 24 hours
            for (const [sessionId, session] of this.sessions) {
                if (now.getTime() - session.lastActivity.getTime() > inactiveThreshold) {
                    this.sessions.delete(sessionId);
                    console.log(`🧹 Cleaned up inactive session: ${sessionId}`);
                }
            }
        }, 60 * 60 * 1000);
    }
    /**
     * Get session
     */
    getSession(sessionId) {
        return this.sessions.get(sessionId);
    }
    /**
     * Get all sessions
     */
    getAllSessions() {
        return Array.from(this.sessions.values());
    }
    /**
     * Shutdown manager
     */
    async shutdown() {
        console.log('🔄 Shutting down Real-time Collaboration Manager...');
        // Disconnect all users
        for (const socket of this.userSockets.values()) {
            socket.disconnect();
        }
        // Clear all data
        this.sessions.clear();
        this.userSockets.clear();
        this.fileVersions.clear();
        this.pendingOperations.clear();
        this.conflicts.clear();
        this.isInitialized = false;
        this.removeAllListeners();
        console.log('✅ Real-time Collaboration Manager shutdown complete');
    }
}
exports.RealTimeManager = RealTimeManager;
exports.default = RealTimeManager;
//# sourceMappingURL=real-time-manager.js.map