{"version": 3, "file": "real-time-manager.js", "sourceRoot": "", "sources": ["../../src/collaboration/real-time-manager.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;GAaG;;;AAEH,mCAAqC;AAmHrC;;GAEG;AACH,MAAa,eAAgB,SAAQ,qBAAY;IAS/C,YAAY,EAAkB;QAC5B,KAAK,EAAE,CAAA;QARD,aAAQ,GAAsC,IAAI,GAAG,EAAE,CAAA;QACvD,gBAAW,GAAwB,IAAI,GAAG,EAAE,CAAA;QAC5C,iBAAY,GAAwB,IAAI,GAAG,EAAE,CAAA;QAC7C,sBAAiB,GAAiC,IAAI,GAAG,EAAE,CAAA;QAC3D,cAAS,GAA0B,IAAI,GAAG,EAAE,CAAA;QAC5C,kBAAa,GAAG,KAAK,CAAA;QAI3B,IAAI,CAAC,EAAE,GAAG,EAAE,CAAA;IACd,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,UAAU;QACd,IAAI,IAAI,CAAC,aAAa;YAAE,OAAM;QAE9B,OAAO,CAAC,GAAG,CAAC,oDAAoD,CAAC,CAAA;QAEjE,IAAI,CAAC;YACH,8BAA8B;YAC9B,IAAI,CAAC,mBAAmB,EAAE,CAAA;YAE1B,0BAA0B;YAC1B,IAAI,CAAC,qBAAqB,EAAE,CAAA;YAE5B,IAAI,CAAC,aAAa,GAAG,IAAI,CAAA;YACzB,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,CAAA;YACxB,OAAO,CAAC,GAAG,CAAC,4DAA4D,CAAC,CAAA;QAE3E,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,yDAAyD,EAAE,KAAK,CAAC,CAAA;YAC/E,MAAM,KAAK,CAAA;QACb,CAAC;IACH,CAAC;IAED;;OAEG;IACK,mBAAmB;QACzB,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,YAAY,EAAE,CAAC,MAAc,EAAE,EAAE;YAC1C,OAAO,CAAC,GAAG,CAAC,sBAAsB,MAAM,CAAC,EAAE,EAAE,CAAC,CAAA;YAE9C,6BAA6B;YAC7B,MAAM,CAAC,EAAE,CAAC,cAAc,EAAE,KAAK,EAAE,IAAuC,EAAE,EAAE;gBAC1E,IAAI,CAAC;oBACH,kDAAkD;oBAClD,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,KAAK,CAAC,CAAA;oBAErE,IAAI,OAAO,EAAE,CAAC;wBACZ,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,EAAE,MAAM,CAAC,CAAA;wBACzC,MAAM,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAA;wBAC3B,MAAM,CAAC,IAAI,CAAC,eAAe,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC,CAAA;wBAC/C,OAAO,CAAC,GAAG,CAAC,yBAAyB,IAAI,CAAC,MAAM,EAAE,CAAC,CAAA;oBACrD,CAAC;yBAAM,CAAC;wBACN,MAAM,CAAC,IAAI,CAAC,eAAe,EAAE,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,eAAe,EAAE,CAAC,CAAA;oBAC1E,CAAC;gBACH,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACf,MAAM,CAAC,IAAI,CAAC,eAAe,EAAE,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,uBAAuB,EAAE,CAAC,CAAA;gBAClF,CAAC;YACH,CAAC,CAAC,CAAA;YAEF,yBAAyB;YACzB,MAAM,CAAC,EAAE,CAAC,cAAc,EAAE,KAAK,EAAE,IAA2B,EAAE,EAAE;gBAC9D,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC;oBACnB,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,EAAE,OAAO,EAAE,mBAAmB,EAAE,CAAC,CAAA;oBACtD,OAAM;gBACR,CAAC;gBAED,IAAI,CAAC;oBACH,MAAM,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,SAAS,EAAE,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,CAAA;oBACzE,MAAM,CAAC,IAAI,CAAC,gBAAgB,EAAE,EAAE,SAAS,EAAE,IAAI,CAAC,SAAS,EAAE,CAAC,CAAA;gBAC9D,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACf,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,EAAE,OAAO,EAAE,KAAK,CAAC,OAAO,EAAE,CAAC,CAAA;gBAClD,CAAC;YACH,CAAC,CAAC,CAAA;YAEF,yBAAyB;YACzB,MAAM,CAAC,EAAE,CAAC,eAAe,EAAE,KAAK,EAAE,IAA2B,EAAE,EAAE;gBAC/D,IAAI,CAAC,MAAM,CAAC,MAAM;oBAAE,OAAM;gBAE1B,IAAI,CAAC;oBACH,MAAM,IAAI,CAAC,4BAA4B,CAAC,IAAI,CAAC,SAAS,EAAE,MAAM,CAAC,MAAM,CAAC,CAAA;oBACtE,MAAM,CAAC,IAAI,CAAC,cAAc,EAAE,EAAE,SAAS,EAAE,IAAI,CAAC,SAAS,EAAE,CAAC,CAAA;gBAC5D,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACf,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,EAAE,OAAO,EAAE,KAAK,CAAC,OAAO,EAAE,CAAC,CAAA;gBAClD,CAAC;YACH,CAAC,CAAC,CAAA;YAEF,wBAAwB;YACxB,MAAM,CAAC,EAAE,CAAC,eAAe,EAAE,CAAC,IAAmD,EAAE,EAAE;gBACjF,IAAI,CAAC,MAAM,CAAC,MAAM;oBAAE,OAAM;gBAC1B,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,SAAS,EAAE,MAAM,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC,CAAA;YACrE,CAAC,CAAC,CAAA;YAEF,sBAAsB;YACtB,MAAM,CAAC,EAAE,CAAC,gBAAgB,EAAE,KAAK,EAAE,IAAqD,EAAE,EAAE;gBAC1F,IAAI,CAAC,MAAM,CAAC,MAAM;oBAAE,OAAM;gBAE1B,IAAI,CAAC;oBACH,MAAM,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,SAAS,EAAE,MAAM,CAAC,MAAM,EAAE,IAAI,CAAC,SAAS,CAAC,CAAA;gBAC/E,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACf,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,EAAE,OAAO,EAAE,KAAK,CAAC,OAAO,EAAE,CAAC,CAAA;gBAClD,CAAC;YACH,CAAC,CAAC,CAAA;YAEF,sBAAsB;YACtB,MAAM,CAAC,EAAE,CAAC,cAAc,EAAE,KAAK,EAAE,IAA+E,EAAE,EAAE;gBAClH,IAAI,CAAC,MAAM,CAAC,MAAM;oBAAE,OAAM;gBAE1B,IAAI,CAAC;oBACH,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,SAAS,EAAE,MAAM,CAAC,MAAM,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,CAAA;oBAC5F,MAAM,CAAC,IAAI,CAAC,cAAc,EAAE,EAAE,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE,CAAC,CAAA;gBACxD,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACf,MAAM,CAAC,IAAI,CAAC,aAAa,EAAE,EAAE,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE,KAAK,EAAE,KAAK,CAAC,OAAO,EAAE,CAAC,CAAA;gBACvE,CAAC;YACH,CAAC,CAAC,CAAA;YAEF,sBAAsB;YACtB,MAAM,CAAC,EAAE,CAAC,cAAc,EAAE,KAAK,EAAE,IAAyC,EAAE,EAAE;gBAC5E,IAAI,CAAC,MAAM,CAAC,MAAM;oBAAE,OAAM;gBAC1B,MAAM,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,SAAS,EAAE,MAAM,CAAC,MAAM,EAAE,IAAI,CAAC,IAAI,CAAC,CAAA;YACtE,CAAC,CAAC,CAAA;YAEF,8BAA8B;YAC9B,MAAM,CAAC,EAAE,CAAC,qBAAqB,EAAE,CAAC,IAAyD,EAAE,EAAE;gBAC7F,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,MAAM,CAAC,CAAA;YACzE,CAAC,CAAC,CAAA;YAEF,uBAAuB;YACvB,MAAM,CAAC,EAAE,CAAC,YAAY,EAAE,GAAG,EAAE;gBAC3B,OAAO,CAAC,GAAG,CAAC,yBAAyB,MAAM,CAAC,EAAE,EAAE,CAAC,CAAA;gBACjD,IAAI,MAAM,CAAC,MAAM,EAAE,CAAC;oBAClB,IAAI,CAAC,oBAAoB,CAAC,MAAM,CAAC,MAAM,CAAC,CAAA;oBACxC,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAA;gBACxC,CAAC;YACH,CAAC,CAAC,CAAA;QACJ,CAAC,CAAC,CAAA;IACJ,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,0BAA0B,CAC9B,SAAiB,EACjB,SAAiB,EACjB,OAAe,EACf,QAAmC;QAEnC,MAAM,eAAe,GAAoB;YACvC,sBAAsB,EAAE,IAAI;YAC5B,QAAQ,EAAE,IAAI;YACd,gBAAgB,EAAE,KAAK,EAAE,aAAa;YACtC,kBAAkB,EAAE,QAAQ;YAC5B,eAAe,EAAE,EAAE;YACnB,eAAe,EAAE,KAAK;YACtB,eAAe,EAAE,KAAK;YACtB,eAAe,EAAE,KAAK;YACtB,GAAG,QAAQ;SACZ,CAAA;QAED,MAAM,OAAO,GAAyB;YACpC,SAAS;YACT,SAAS;YACT,YAAY,EAAE,EAAE;YAChB,WAAW,EAAE,EAAE;YACf,MAAM,EAAE,EAAE;YACV,QAAQ,EAAE,eAAe;YACzB,SAAS,EAAE,IAAI,IAAI,EAAE;YACrB,YAAY,EAAE,IAAI,IAAI,EAAE;SACzB,CAAA;QAED,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,SAAS,EAAE,OAAO,CAAC,CAAA;QACrC,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE,EAAE,SAAS,EAAE,OAAO,EAAE,CAAC,CAAA;QAEpD,OAAO,CAAC,GAAG,CAAC,qCAAqC,SAAS,EAAE,CAAC,CAAA;QAC7D,OAAO,OAAO,CAAA;IAChB,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,uBAAuB,CAC3B,SAAiB,EACjB,MAAc,EACd,MAAc;QAEd,MAAM,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,SAAS,CAAC,CAAA;QAC5C,IAAI,CAAC,OAAO,EAAE,CAAC;YACb,MAAM,IAAI,KAAK,CAAC,WAAW,SAAS,YAAY,CAAC,CAAA;QACnD,CAAC;QAED,sCAAsC;QACtC,MAAM,mBAAmB,GAAG,OAAO,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,KAAK,MAAM,CAAC,CAAA;QAC/E,IAAI,mBAAmB,EAAE,CAAC;YACxB,mBAAmB,CAAC,MAAM,GAAG,QAAQ,CAAA;YACrC,mBAAmB,CAAC,QAAQ,GAAG,IAAI,IAAI,EAAE,CAAA;QAC3C,CAAC;aAAM,CAAC;YACN,sBAAsB;YACtB,MAAM,WAAW,GAAgB;gBAC/B,MAAM;gBACN,QAAQ,EAAE,QAAQ,MAAM,EAAE,EAAE,wBAAwB;gBACpD,IAAI,EAAE,OAAO,CAAC,YAAY,CAAC,MAAM,KAAK,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,cAAc;gBAClE,MAAM,EAAE,QAAQ;gBAChB,WAAW,EAAE,IAAI,CAAC,qBAAqB,CAAC,cAAc,CAAC;gBACvD,QAAQ,EAAE,IAAI,IAAI,EAAE;gBACpB,QAAQ,EAAE,IAAI,IAAI,EAAE;aACrB,CAAA;YAED,OAAO,CAAC,YAAY,CAAC,IAAI,CAAC,WAAW,CAAC,CAAA;QACxC,CAAC;QAED,mBAAmB;QACnB,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAA;QAEtB,wCAAwC;QACxC,MAAM,CAAC,IAAI,CAAC,eAAe,EAAE;YAC3B,OAAO;YACP,KAAK,EAAE,OAAO,CAAC,WAAW;YAC1B,MAAM,EAAE,OAAO,CAAC,MAAM;SACvB,CAAC,CAAA;QAEF,4BAA4B;QAC5B,MAAM,CAAC,EAAE,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,oBAAoB,EAAE;YAC9C,MAAM;YACN,WAAW,EAAE,OAAO,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,KAAK,MAAM,CAAC;SACjE,CAAC,CAAA;QAEF,OAAO,CAAC,YAAY,GAAG,IAAI,IAAI,EAAE,CAAA;QACjC,IAAI,CAAC,IAAI,CAAC,oBAAoB,EAAE,EAAE,SAAS,EAAE,MAAM,EAAE,CAAC,CAAA;IACxD,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,4BAA4B,CAAC,SAAiB,EAAE,MAAc;QAClE,MAAM,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,SAAS,CAAC,CAAA;QAC5C,IAAI,CAAC,OAAO;YAAE,OAAM;QAEpB,4BAA4B;QAC5B,MAAM,WAAW,GAAG,OAAO,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,KAAK,MAAM,CAAC,CAAA;QACvE,IAAI,WAAW,EAAE,CAAC;YAChB,WAAW,CAAC,MAAM,GAAG,SAAS,CAAA;YAC9B,WAAW,CAAC,QAAQ,GAAG,IAAI,IAAI,EAAE,CAAA;QACnC,CAAC;QAED,iCAAiC;QACjC,KAAK,MAAM,IAAI,IAAI,OAAO,CAAC,WAAW,EAAE,CAAC;YACvC,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,KAAK,MAAM,CAAC,CAAA;QAChE,CAAC;QAED,4BAA4B;QAC5B,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,kBAAkB,EAAE,EAAE,MAAM,EAAE,CAAC,CAAA;QAE1D,OAAO,CAAC,YAAY,GAAG,IAAI,IAAI,EAAE,CAAA;QACjC,IAAI,CAAC,IAAI,CAAC,kBAAkB,EAAE,EAAE,SAAS,EAAE,MAAM,EAAE,CAAC,CAAA;IACtD,CAAC;IAED;;OAEG;IACK,kBAAkB,CAAC,SAAiB,EAAE,MAAc,EAAE,MAAsB;QAClF,MAAM,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,SAAS,CAAC,CAAA;QAC5C,IAAI,CAAC,OAAO;YAAE,OAAM;QAEpB,4BAA4B;QAC5B,MAAM,WAAW,GAAG,OAAO,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,KAAK,MAAM,CAAC,CAAA;QACvE,IAAI,WAAW,EAAE,CAAC;YAChB,WAAW,CAAC,MAAM,GAAG,MAAM,CAAA;QAC7B,CAAC;QAED,8BAA8B;QAC9B,MAAM,IAAI,GAAG,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,MAAM,CAAC,IAAI,CAAC,CAAA;QAClE,IAAI,IAAI,EAAE,CAAC;YACT,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM,EAAE,MAAM,CAAC,CAAA;QAClC,CAAC;QAED,gDAAgD;QAChD,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,eAAe,EAAE;YAC1C,MAAM;YACN,MAAM;SACP,CAAC,CAAA;QAEF,OAAO,CAAC,YAAY,GAAG,IAAI,IAAI,EAAE,CAAA;IACnC,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,mBAAmB,CAC/B,SAAiB,EACjB,MAAc,EACd,SAAwB;QAExB,MAAM,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,SAAS,CAAC,CAAA;QAC5C,IAAI,CAAC,OAAO,EAAE,CAAC;YACb,MAAM,IAAI,KAAK,CAAC,WAAW,SAAS,YAAY,CAAC,CAAA;QACnD,CAAC;QAED,oBAAoB;QACpB,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,CAAC,EAAE,CAAC;YAC3D,MAAM,IAAI,KAAK,CAAC,0BAA0B,CAAC,CAAA;QAC7C,CAAC;QAED,mBAAmB;QACnB,MAAM,IAAI,GAAG,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,SAAS,CAAC,IAAI,CAAC,CAAA;QACrE,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE,MAAM,CAAC,EAAE,CAAC;YAC5C,MAAM,IAAI,KAAK,CAAC,gCAAgC,CAAC,CAAA;QACnD,CAAC;QAED,iCAAiC;QACjC,MAAM,OAAO,GAAG,GAAG,SAAS,IAAI,SAAS,CAAC,IAAI,EAAE,CAAA;QAChD,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC;YACzC,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,OAAO,EAAE,EAAE,CAAC,CAAA;QACzC,CAAC;QACD,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,OAAO,CAAE,CAAC,IAAI,CAAC,SAAS,CAAC,CAAA;QAEpD,qBAAqB;QACrB,MAAM,IAAI,CAAC,qBAAqB,CAAC,SAAS,EAAE,SAAS,CAAC,IAAI,CAAC,CAAA;QAE3D,OAAO,CAAC,YAAY,GAAG,IAAI,IAAI,EAAE,CAAA;IACnC,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,qBAAqB,CAAC,SAAiB,EAAE,QAAgB;QACrE,MAAM,OAAO,GAAG,GAAG,SAAS,IAAI,QAAQ,EAAE,CAAA;QAC1C,MAAM,UAAU,GAAG,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,EAAE,CAAA;QAE5D,IAAI,UAAU,CAAC,MAAM,KAAK,CAAC;YAAE,OAAM;QAEnC,+BAA+B;QAC/B,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,SAAS,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC,CAAA;QAExE,mBAAmB;QACnB,MAAM,SAAS,GAAG,IAAI,CAAC,mBAAmB,CAAC,UAAU,CAAC,CAAA;QAEtD,IAAI,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACzB,mBAAmB;YACnB,MAAM,IAAI,CAAC,mBAAmB,CAAC,SAAS,EAAE,QAAQ,EAAE,SAAS,CAAC,CAAA;QAChE,CAAC;aAAM,CAAC;YACN,mBAAmB;YACnB,MAAM,IAAI,CAAC,mBAAmB,CAAC,SAAS,EAAE,QAAQ,EAAE,UAAU,CAAC,CAAA;QACjE,CAAC;QAED,6BAA6B;QAC7B,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,OAAO,EAAE,EAAE,CAAC,CAAA;IACzC,CAAC;IAED;;OAEG;IACK,mBAAmB,CAAC,UAA2B;QACrD,MAAM,SAAS,GAAe,EAAE,CAAA;QAEhC,+EAA+E;QAC/E,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;YAC/C,KAAK,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,UAAU,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;gBAC/C,MAAM,GAAG,GAAG,UAAU,CAAC,CAAC,CAAC,CAAA;gBACzB,MAAM,GAAG,GAAG,UAAU,CAAC,CAAC,CAAC,CAAA;gBAEzB,8BAA8B;gBAC9B,IAAI,GAAG,CAAC,IAAI,KAAK,GAAG,CAAC,IAAI;oBACrB,GAAG,CAAC,QAAQ,CAAC,IAAI,KAAK,GAAG,CAAC,QAAQ,CAAC,IAAI;oBACvC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,SAAS,CAAC,OAAO,EAAE,GAAG,GAAG,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC,GAAG,IAAI,EAAE,CAAC,CAAC,kBAAkB;oBAE1F,MAAM,UAAU,GAAG,YAAY,IAAI,CAAC,GAAG,EAAE,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAA;oBACtF,SAAS,CAAC,IAAI,CAAC;wBACb,EAAE,EAAE,UAAU;wBACd,IAAI,EAAE,GAAG,CAAC,IAAI;wBACd,UAAU,EAAE,CAAC,GAAG,EAAE,GAAG,CAAC;wBACtB,YAAY,EAAE,CAAC,GAAG,CAAC,MAAM,EAAE,GAAG,CAAC,MAAM,CAAC;wBACtC,IAAI,EAAE,iBAAiB;wBACvB,MAAM,EAAE,SAAS;wBACjB,SAAS,EAAE,IAAI,IAAI,EAAE;qBACtB,CAAC,CAAA;gBACJ,CAAC;YACH,CAAC;QACH,CAAC;QAED,OAAO,SAAS,CAAA;IAClB,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,mBAAmB,CAC/B,SAAiB,EACjB,QAAgB,EAChB,SAAqB;QAErB,KAAK,MAAM,QAAQ,IAAI,SAAS,EAAE,CAAC;YACjC,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,EAAE,QAAQ,CAAC,CAAA;YAEzC,qCAAqC;YACrC,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,eAAe,EAAE;gBAC1C,QAAQ;gBACR,IAAI,EAAE,QAAQ;aACf,CAAC,CAAA;YAEF,IAAI,CAAC,IAAI,CAAC,mBAAmB,EAAE,EAAE,SAAS,EAAE,QAAQ,EAAE,CAAC,CAAA;QACzD,CAAC;IACH,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,mBAAmB,CAC/B,SAAiB,EACjB,QAAgB,EAChB,UAA2B;QAE3B,MAAM,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,SAAS,CAAC,CAAA;QAC5C,IAAI,CAAC,OAAO;YAAE,OAAM;QAEpB,sBAAsB;QACtB,IAAI,IAAI,GAAG,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,QAAQ,CAAC,CAAA;QAC7D,IAAI,CAAC,IAAI,EAAE,CAAC;YACV,IAAI,GAAG;gBACL,IAAI,EAAE,QAAQ;gBACd,OAAO,EAAE,EAAE;gBACX,OAAO,EAAE,CAAC;gBACV,KAAK,EAAE,EAAE;gBACT,OAAO,EAAE,IAAI,GAAG,EAAE;gBAClB,YAAY,EAAE,IAAI,IAAI,EAAE;gBACxB,UAAU,EAAE,UAAU,CAAC,CAAC,CAAC,EAAE,MAAM,IAAI,QAAQ;aAC9C,CAAA;YACD,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;QAChC,CAAC;QAED,4BAA4B;QAC5B,KAAK,MAAM,SAAS,IAAI,UAAU,EAAE,CAAC;YACnC,kCAAkC;YAClC,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,OAAO,EAAE,SAAS,CAAC,CAAA;YAC3D,IAAI,CAAC,OAAO,EAAE,CAAA;YACd,IAAI,CAAC,YAAY,GAAG,IAAI,IAAI,EAAE,CAAA;YAC9B,IAAI,CAAC,UAAU,GAAG,SAAS,CAAC,MAAM,CAAA;QACpC,CAAC;QAED,4CAA4C;QAC5C,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,cAAc,EAAE;YACzC,IAAI,EAAE,QAAQ;YACd,OAAO,EAAE,IAAI,CAAC,OAAO;YACrB,OAAO,EAAE,IAAI,CAAC,OAAO;YACrB,UAAU;SACX,CAAC,CAAA;QAEF,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,EAAE,SAAS,EAAE,QAAQ,EAAE,UAAU,EAAE,CAAC,CAAA;IAChE,CAAC;IAED;;OAEG;IACK,cAAc,CAAC,OAAe,EAAE,SAAwB;QAC9D,MAAM,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,CAAA;QACjC,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,SAAS,CAAC,QAAQ,CAAA;QAE3C,QAAQ,SAAS,CAAC,IAAI,EAAE,CAAC;YACvB,KAAK,QAAQ;gBACX,IAAI,SAAS,CAAC,OAAO,EAAE,CAAC;oBACtB,MAAM,UAAU,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,EAAE,CAAA;oBACpC,KAAK,CAAC,IAAI,CAAC,GAAG,UAAU,CAAC,KAAK,CAAC,CAAC,EAAE,MAAM,CAAC,GAAG,SAAS,CAAC,OAAO,GAAG,UAAU,CAAC,KAAK,CAAC,MAAM,CAAC,CAAA;gBAC1F,CAAC;gBACD,MAAK;YAEP,KAAK,QAAQ;gBACX,IAAI,SAAS,CAAC,MAAM,EAAE,CAAC;oBACrB,MAAM,UAAU,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,EAAE,CAAA;oBACpC,KAAK,CAAC,IAAI,CAAC,GAAG,UAAU,CAAC,KAAK,CAAC,CAAC,EAAE,MAAM,CAAC,GAAG,UAAU,CAAC,KAAK,CAAC,MAAM,GAAG,SAAS,CAAC,MAAM,CAAC,CAAA;gBACzF,CAAC;gBACD,MAAK;YAEP,KAAK,SAAS;gBACZ,IAAI,SAAS,CAAC,OAAO,IAAI,SAAS,CAAC,MAAM,EAAE,CAAC;oBAC1C,MAAM,UAAU,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,EAAE,CAAA;oBACpC,KAAK,CAAC,IAAI,CAAC,GAAG,UAAU,CAAC,KAAK,CAAC,CAAC,EAAE,MAAM,CAAC,GAAG,SAAS,CAAC,OAAO,GAAG,UAAU,CAAC,KAAK,CAAC,MAAM,GAAG,SAAS,CAAC,MAAM,CAAC,CAAA;gBAC7G,CAAC;gBACD,MAAK;QACT,CAAC;QAED,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;IACzB,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,eAAe,CAC3B,SAAiB,EACjB,MAAc,EACd,QAAgB,EAChB,QAAwC;QAExC,MAAM,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,SAAS,CAAC,CAAA;QAC5C,IAAI,CAAC,OAAO,EAAE,CAAC;YACb,MAAM,IAAI,KAAK,CAAC,WAAW,SAAS,YAAY,CAAC,CAAA;QACnD,CAAC;QAED,IAAI,IAAI,GAAG,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,QAAQ,CAAC,CAAA;QAC7D,IAAI,CAAC,IAAI,EAAE,CAAC;YACV,IAAI,GAAG;gBACL,IAAI,EAAE,QAAQ;gBACd,OAAO,EAAE,EAAE;gBACX,OAAO,EAAE,CAAC;gBACV,KAAK,EAAE,EAAE;gBACT,OAAO,EAAE,IAAI,GAAG,EAAE;gBAClB,YAAY,EAAE,IAAI,IAAI,EAAE;gBACxB,UAAU,EAAE,MAAM;aACnB,CAAA;YACD,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;QAChC,CAAC;QAED,8BAA8B;QAC9B,MAAM,gBAAgB,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE;YAChD,IAAI,QAAQ,KAAK,WAAW,IAAI,IAAI,CAAC,IAAI,KAAK,WAAW,EAAE,CAAC;gBAC1D,OAAO,IAAI,CAAA;YACb,CAAC;YACD,IAAI,QAAQ,KAAK,OAAO,IAAI,IAAI,CAAC,IAAI,KAAK,OAAO,EAAE,CAAC;gBAClD,OAAO,IAAI,CAAA;YACb,CAAC;YACD,OAAO,KAAK,CAAA;QACd,CAAC,CAAC,CAAA;QAEF,IAAI,gBAAgB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAChC,MAAM,IAAI,KAAK,CAAC,gCAAgC,CAAC,CAAA;QACnD,CAAC;QAED,cAAc;QACd,MAAM,IAAI,GAAa;YACrB,MAAM;YACN,IAAI,EAAE,QAAQ;YACd,UAAU,EAAE,IAAI,IAAI,EAAE;YACtB,SAAS,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,MAAM,CAAC,CAAC,YAAY;SACtD,CAAA;QAED,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;QAErB,4BAA4B;QAC5B,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,aAAa,EAAE;YACxC,IAAI,EAAE,QAAQ;YACd,MAAM;YACN,QAAQ;SACT,CAAC,CAAA;QAEF,OAAO,IAAI,CAAA;IACb,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,eAAe,CAAC,SAAiB,EAAE,MAAc,EAAE,QAAgB;QAC/E,MAAM,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,SAAS,CAAC,CAAA;QAC5C,IAAI,CAAC,OAAO;YAAE,OAAM;QAEpB,MAAM,IAAI,GAAG,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,QAAQ,CAAC,CAAA;QAC/D,IAAI,CAAC,IAAI;YAAE,OAAM;QAEjB,sBAAsB;QACtB,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,KAAK,MAAM,CAAC,CAAA;QAE9D,4BAA4B;QAC5B,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,eAAe,EAAE;YAC1C,IAAI,EAAE,QAAQ;YACd,MAAM;SACP,CAAC,CAAA;IACJ,CAAC;IAED;;OAEG;IACK,uBAAuB,CAAC,SAAiB,EAAE,OAAe,EAAE,MAAW;QAC7E,MAAM,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,SAAS,CAAC,CAAA;QAC5C,IAAI,CAAC,OAAO;YAAE,OAAM;QAEpB,IAAI,KAAK,GAAG,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,KAAK,OAAO,CAAC,CAAA;QAC3D,IAAI,CAAC,KAAK,EAAE,CAAC;YACX,KAAK,GAAG;gBACN,OAAO;gBACP,SAAS,EAAE,MAAM,CAAC,IAAI,IAAI,SAAS;gBACnC,MAAM,EAAE,MAAM;gBACd,QAAQ,EAAE,CAAC;gBACX,aAAa,EAAE,EAAE;gBACjB,UAAU,EAAE,IAAI,IAAI,EAAE;aACvB,CAAA;YACD,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;QAC5B,CAAC;QAED,sBAAsB;QACtB,MAAM,CAAC,MAAM,CAAC,KAAK,EAAE,MAAM,EAAE,EAAE,UAAU,EAAE,IAAI,IAAI,EAAE,EAAE,CAAC,CAAA;QAExD,yBAAyB;QACzB,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,qBAAqB,EAAE;YAChD,OAAO;YACP,MAAM,EAAE,KAAK;SACd,CAAC,CAAA;QAEF,OAAO,CAAC,YAAY,GAAG,IAAI,IAAI,EAAE,CAAA;QACjC,IAAI,CAAC,IAAI,CAAC,sBAAsB,EAAE,EAAE,SAAS,EAAE,OAAO,EAAE,MAAM,EAAE,KAAK,EAAE,CAAC,CAAA;IAC1E,CAAC;IAED;;OAEG;IACK,oBAAoB,CAAC,MAAc;QACzC,qCAAqC;QACrC,KAAK,MAAM,OAAO,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,EAAE,CAAC;YAC7C,MAAM,WAAW,GAAG,OAAO,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,KAAK,MAAM,CAAC,CAAA;YACvE,IAAI,WAAW,EAAE,CAAC;gBAChB,WAAW,CAAC,MAAM,GAAG,SAAS,CAAA;gBAC9B,WAAW,CAAC,QAAQ,GAAG,IAAI,IAAI,EAAE,CAAA;gBAEjC,oBAAoB;gBACpB,KAAK,MAAM,IAAI,IAAI,OAAO,CAAC,WAAW,EAAE,CAAC;oBACvC,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,KAAK,MAAM,CAAC,CAAA;gBAChE,CAAC;gBAED,4BAA4B;gBAC5B,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,4BAA4B,EAAE;oBAC/D,MAAM;oBACN,MAAM,EAAE,SAAS;iBAClB,CAAC,CAAA;YACJ,CAAC;QACH,CAAC;IACH,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,iBAAiB,CAAC,MAAc,EAAE,KAAa;QAC3D,wCAAwC;QACxC,OAAO,IAAI,CAAA,CAAC,cAAc;IAC5B,CAAC;IAEO,qBAAqB,CAAC,IAAY;QACxC,MAAM,WAAW,GAAiB;YAChC,EAAE,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,OAAO,EAAE;YACrC,EAAE,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,SAAS,EAAE;SACxC,CAAA;QAED,IAAI,IAAI,KAAK,cAAc,IAAI,IAAI,KAAK,OAAO,EAAE,CAAC;YAChD,WAAW,CAAC,IAAI,CACd,EAAE,MAAM,EAAE,OAAO,EAAE,QAAQ,EAAE,OAAO,EAAE,EACtC,EAAE,MAAM,EAAE,SAAS,EAAE,QAAQ,EAAE,QAAQ,EAAE,CAC1C,CAAA;QACH,CAAC;QAED,IAAI,IAAI,KAAK,OAAO,EAAE,CAAC;YACrB,WAAW,CAAC,IAAI,CACd,EAAE,MAAM,EAAE,OAAO,EAAE,QAAQ,EAAE,SAAS,EAAE,EACxC,EAAE,MAAM,EAAE,OAAO,EAAE,QAAQ,EAAE,UAAU,EAAE,CAC1C,CAAA;QACH,CAAC;QAED,OAAO,WAAW,CAAA;IACpB,CAAC;IAEO,aAAa,CACnB,OAA6B,EAC7B,MAAc,EACd,MAAc,EACd,QAAgB;QAEhB,MAAM,WAAW,GAAG,OAAO,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,KAAK,MAAM,CAAC,CAAA;QACvE,IAAI,CAAC,WAAW;YAAE,OAAO,KAAK,CAAA;QAE9B,OAAO,WAAW,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CACtC,CAAC,CAAC,MAAM,KAAK,MAAM,IAAI,CAAC,CAAC,QAAQ,KAAK,QAAQ,CAC/C,CAAA;IACH,CAAC;IAEO,WAAW,CAAC,IAAgB,EAAE,MAAc;QAClD,qDAAqD;QACrD,MAAM,cAAc,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAC9C,IAAI,CAAC,IAAI,KAAK,WAAW,IAAI,IAAI,CAAC,MAAM,KAAK,MAAM,CACpD,CAAA;QAED,OAAO,cAAc,CAAC,MAAM,KAAK,CAAC,CAAA;IACpC,CAAC;IAED;;OAEG;IACK,qBAAqB;QAC3B,sCAAsC;QACtC,WAAW,CAAC,GAAG,EAAE;YACf,MAAM,GAAG,GAAG,IAAI,IAAI,EAAE,CAAA;YACtB,KAAK,MAAM,OAAO,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,EAAE,CAAC;gBAC7C,KAAK,MAAM,IAAI,IAAI,OAAO,CAAC,WAAW,EAAE,CAAC;oBACvC,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,SAAS,GAAG,GAAG,CAAC,CAAA;gBAC9D,CAAC;YACH,CAAC;QACH,CAAC,EAAE,KAAK,CAAC,CAAA;QAET,wCAAwC;QACxC,WAAW,CAAC,GAAG,EAAE;YACf,MAAM,GAAG,GAAG,IAAI,IAAI,EAAE,CAAA;YACtB,MAAM,iBAAiB,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAA,CAAC,WAAW;YAEzD,KAAK,MAAM,CAAC,SAAS,EAAE,OAAO,CAAC,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;gBACjD,IAAI,GAAG,CAAC,OAAO,EAAE,GAAG,OAAO,CAAC,YAAY,CAAC,OAAO,EAAE,GAAG,iBAAiB,EAAE,CAAC;oBACvE,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,SAAS,CAAC,CAAA;oBAC/B,OAAO,CAAC,GAAG,CAAC,mCAAmC,SAAS,EAAE,CAAC,CAAA;gBAC7D,CAAC;YACH,CAAC;QACH,CAAC,EAAE,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAA;IACpB,CAAC;IAED;;OAEG;IACH,UAAU,CAAC,SAAiB;QAC1B,OAAO,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,SAAS,CAAC,CAAA;IACrC,CAAC;IAED;;OAEG;IACH,cAAc;QACZ,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC,CAAA;IAC3C,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,QAAQ;QACZ,OAAO,CAAC,GAAG,CAAC,qDAAqD,CAAC,CAAA;QAElE,uBAAuB;QACvB,KAAK,MAAM,MAAM,IAAI,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,EAAE,CAAC;YAC/C,MAAM,CAAC,UAAU,EAAE,CAAA;QACrB,CAAC;QAED,iBAAiB;QACjB,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAA;QACrB,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,CAAA;QACxB,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,CAAA;QACzB,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,CAAA;QAC9B,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,CAAA;QAEtB,IAAI,CAAC,aAAa,GAAG,KAAK,CAAA;QAC1B,IAAI,CAAC,kBAAkB,EAAE,CAAA;QACzB,OAAO,CAAC,GAAG,CAAC,qDAAqD,CAAC,CAAA;IACpE,CAAC;CACF;AA1uBD,0CA0uBC;AASD,kBAAe,eAAe,CAAA"}