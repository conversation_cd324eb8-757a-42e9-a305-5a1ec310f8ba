/**
 * AP3X Platform - Complete Integration
 *
 * The ultimate integration that brings together all components:
 * - AIDER's proven code editing
 * - AG3NT's multi-agent coordination
 * - Advanced Context Engine
 * - Real-time collaboration
 * - Visual workflow designer
 * - Enterprise features
 *
 * This is the main orchestrator that creates the most powerful
 * agentic coding platform ever built.
 */
import { EventEmitter } from 'events';
export interface AP3XPlatformConfig {
    port: number;
    host: string;
    environment: 'development' | 'staging' | 'production';
    aider: {
        pythonPath: string;
        modelSettings: {
            model: string;
            apiKey?: string;
        };
        workingDirectory: string;
    };
    ag3nt: {
        enableMCP: boolean;
        enableSequentialThinking: boolean;
        enableRAG: boolean;
        maxConcurrentAgents: number;
    };
    contextEngine: {
        apiUrl: string;
        neo4jUrl?: string;
        neo4jUser?: string;
        neo4jPassword?: string;
        enableRealTimeIndexing: boolean;
    };
    enterprise: {
        enableUserManagement: boolean;
        enableTeams: boolean;
        enableSSO: boolean;
        jwtSecret: string;
    };
    features: {
        enableRealTimeCollaboration: boolean;
        enableVisualWorkflows: boolean;
        enableAdvancedAnalytics: boolean;
        enableAuditLogging: boolean;
    };
    security: {
        enableRateLimit: boolean;
        enableCORS: boolean;
        corsOrigins: string[];
        enableHelmet: boolean;
    };
}
export interface AP3XPlatformStatus {
    status: 'starting' | 'running' | 'stopping' | 'stopped' | 'error';
    components: {
        bridge: 'initializing' | 'ready' | 'error';
        gateway: 'initializing' | 'ready' | 'error';
        aider: 'initializing' | 'ready' | 'error';
        ag3nt: 'initializing' | 'ready' | 'error';
        contextEngine: 'initializing' | 'ready' | 'error';
        realTime: 'initializing' | 'ready' | 'error';
        userManagement: 'initializing' | 'ready' | 'error';
    };
    metrics: {
        uptime: number;
        activeSessions: number;
        activeAgents: number;
        totalRequests: number;
        errorRate: number;
    };
    version: string;
    startTime: Date;
}
/**
 * AP3X Platform - Complete Integration
 */
export declare class AP3XPlatform extends EventEmitter {
    private config;
    private status;
    private bridge;
    private gateway;
    private server;
    private io;
    private aiderIntegration;
    private ag3ntFramework;
    private multiAgentCoordinator;
    private contextEngineIntegration;
    private realTimeManager;
    private userManagement;
    private isRunning;
    constructor(config: AP3XPlatformConfig);
    /**
     * Initialize all components
     */
    private initializeComponents;
    /**
     * Setup event listeners for cross-component communication
     */
    private setupEventListeners;
    /**
     * Start the complete AP3X Platform
     */
    start(): Promise<void>;
    /**
     * Stop the AP3X Platform
     */
    stop(): Promise<void>;
    /**
     * Get platform status
     */
    getStatus(): AP3XPlatformStatus;
    /**
     * Get platform health
     */
    getHealth(): Promise<{
        status: 'healthy' | 'degraded' | 'unhealthy';
        components: {
            [key: string]: boolean;
        };
        timestamp: Date;
    }>;
    /**
     * Restart the platform
     */
    restart(): Promise<void>;
}
export declare const defaultConfig: AP3XPlatformConfig;
export default AP3XPlatform;
//# sourceMappingURL=ap3x-platform.d.ts.map