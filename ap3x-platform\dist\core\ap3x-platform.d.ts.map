{"version": 3, "file": "ap3x-platform.d.ts", "sourceRoot": "", "sources": ["../../src/core/ap3x-platform.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;GAaG;AAEH,OAAO,EAAE,YAAY,EAAE,MAAM,QAAQ,CAAA;AAarC,MAAM,WAAW,kBAAkB;IAEjC,IAAI,EAAE,MAAM,CAAA;IACZ,IAAI,EAAE,MAAM,CAAA;IACZ,WAAW,EAAE,aAAa,GAAG,SAAS,GAAG,YAAY,CAAA;IAGrD,KAAK,EAAE;QACL,UAAU,EAAE,MAAM,CAAA;QAClB,aAAa,EAAE;YACb,KAAK,EAAE,MAAM,CAAA;YACb,MAAM,CAAC,EAAE,MAAM,CAAA;SAChB,CAAA;QACD,gBAAgB,EAAE,MAAM,CAAA;KACzB,CAAA;IAED,KAAK,EAAE;QACL,SAAS,EAAE,OAAO,CAAA;QAClB,wBAAwB,EAAE,OAAO,CAAA;QACjC,SAAS,EAAE,OAAO,CAAA;QAClB,mBAAmB,EAAE,MAAM,CAAA;KAC5B,CAAA;IAED,aAAa,EAAE;QACb,MAAM,EAAE,MAAM,CAAA;QACd,QAAQ,CAAC,EAAE,MAAM,CAAA;QACjB,SAAS,CAAC,EAAE,MAAM,CAAA;QAClB,aAAa,CAAC,EAAE,MAAM,CAAA;QACtB,sBAAsB,EAAE,OAAO,CAAA;KAChC,CAAA;IAGD,UAAU,EAAE;QACV,oBAAoB,EAAE,OAAO,CAAA;QAC7B,WAAW,EAAE,OAAO,CAAA;QACpB,SAAS,EAAE,OAAO,CAAA;QAClB,SAAS,EAAE,MAAM,CAAA;KAClB,CAAA;IAGD,QAAQ,EAAE;QACR,2BAA2B,EAAE,OAAO,CAAA;QACpC,qBAAqB,EAAE,OAAO,CAAA;QAC9B,uBAAuB,EAAE,OAAO,CAAA;QAChC,kBAAkB,EAAE,OAAO,CAAA;KAC5B,CAAA;IAGD,QAAQ,EAAE;QACR,eAAe,EAAE,OAAO,CAAA;QACxB,UAAU,EAAE,OAAO,CAAA;QACnB,WAAW,EAAE,MAAM,EAAE,CAAA;QACrB,YAAY,EAAE,OAAO,CAAA;KACtB,CAAA;CACF;AAED,MAAM,WAAW,kBAAkB;IACjC,MAAM,EAAE,UAAU,GAAG,SAAS,GAAG,UAAU,GAAG,SAAS,GAAG,OAAO,CAAA;IACjE,UAAU,EAAE;QACV,MAAM,EAAE,cAAc,GAAG,OAAO,GAAG,OAAO,CAAA;QAC1C,OAAO,EAAE,cAAc,GAAG,OAAO,GAAG,OAAO,CAAA;QAC3C,KAAK,EAAE,cAAc,GAAG,OAAO,GAAG,OAAO,CAAA;QACzC,KAAK,EAAE,cAAc,GAAG,OAAO,GAAG,OAAO,CAAA;QACzC,aAAa,EAAE,cAAc,GAAG,OAAO,GAAG,OAAO,CAAA;QACjD,QAAQ,EAAE,cAAc,GAAG,OAAO,GAAG,OAAO,CAAA;QAC5C,cAAc,EAAE,cAAc,GAAG,OAAO,GAAG,OAAO,CAAA;KACnD,CAAA;IACD,OAAO,EAAE;QACP,MAAM,EAAE,MAAM,CAAA;QACd,cAAc,EAAE,MAAM,CAAA;QACtB,YAAY,EAAE,MAAM,CAAA;QACpB,aAAa,EAAE,MAAM,CAAA;QACrB,SAAS,EAAE,MAAM,CAAA;KAClB,CAAA;IACD,OAAO,EAAE,MAAM,CAAA;IACf,SAAS,EAAE,IAAI,CAAA;CAChB;AAED;;GAEG;AACH,qBAAa,YAAa,SAAQ,YAAY;IAC5C,OAAO,CAAC,MAAM,CAAoB;IAClC,OAAO,CAAC,MAAM,CAAoB;IAGlC,OAAO,CAAC,MAAM,CAAY;IAC1B,OAAO,CAAC,OAAO,CAAa;IAC5B,OAAO,CAAC,MAAM,CAAK;IACnB,OAAO,CAAC,EAAE,CAAgB;IAG1B,OAAO,CAAC,gBAAgB,CAAkB;IAC1C,OAAO,CAAC,cAAc,CAAgB;IACtC,OAAO,CAAC,qBAAqB,CAAuB;IACpD,OAAO,CAAC,wBAAwB,CAA0B;IAC1D,OAAO,CAAC,eAAe,CAAiB;IACxC,OAAO,CAAC,cAAc,CAAgB;IAEtC,OAAO,CAAC,SAAS,CAAQ;gBAEb,MAAM,EAAE,kBAAkB;IA8BtC;;OAEG;IACH,OAAO,CAAC,oBAAoB;IAoH5B;;OAEG;IACH,OAAO,CAAC,mBAAmB;IA+C3B;;OAEG;IACG,KAAK,IAAI,OAAO,CAAC,IAAI,CAAC;IAmF5B;;OAEG;IACG,IAAI,IAAI,OAAO,CAAC,IAAI,CAAC;IAyD3B;;OAEG;IACH,SAAS,IAAI,kBAAkB;IAW/B;;OAEG;IACG,SAAS,IAAI,OAAO,CAAC;QACzB,MAAM,EAAE,SAAS,GAAG,UAAU,GAAG,WAAW,CAAA;QAC5C,UAAU,EAAE;YAAE,CAAC,GAAG,EAAE,MAAM,GAAG,OAAO,CAAA;SAAE,CAAA;QACtC,SAAS,EAAE,IAAI,CAAA;KAChB,CAAC;IA4CF;;OAEG;IACG,OAAO,IAAI,OAAO,CAAC,IAAI,CAAC;CAM/B;AAGD,eAAO,MAAM,aAAa,EAAE,kBAiD3B,CAAA;AAED,eAAe,YAAY,CAAA"}