/**
 * AP3X Platform - Enterprise User Management
 *
 * Comprehensive user management system for enterprise deployments.
 *
 * Features:
 * - User authentication and authorization
 * - Role-based access control (RBAC)
 * - Team and organization management
 * - Single Sign-On (SSO) integration
 * - Audit logging
 * - User activity tracking
 */
import { EventEmitter } from 'events';
export interface User {
    id: string;
    email: string;
    username: string;
    firstName: string;
    lastName: string;
    avatar?: string;
    status: 'active' | 'inactive' | 'suspended' | 'pending';
    roles: Role[];
    teams: string[];
    organizationId: string;
    preferences: UserPreferences;
    metadata: {
        createdAt: Date;
        updatedAt: Date;
        lastLoginAt?: Date;
        loginCount: number;
        emailVerified: boolean;
        twoFactorEnabled: boolean;
    };
}
export interface Role {
    id: string;
    name: string;
    description: string;
    permissions: Permission[];
    isSystemRole: boolean;
    organizationId?: string;
}
export interface Permission {
    id: string;
    resource: string;
    action: string;
    conditions?: PermissionCondition[];
}
export interface PermissionCondition {
    field: string;
    operator: 'equals' | 'not_equals' | 'in' | 'not_in' | 'contains' | 'starts_with';
    value: any;
}
export interface UserPreferences {
    theme: 'light' | 'dark' | 'auto';
    language: string;
    timezone: string;
    notifications: {
        email: boolean;
        push: boolean;
        desktop: boolean;
        frequency: 'immediate' | 'hourly' | 'daily' | 'weekly';
    };
    editor: {
        fontSize: number;
        tabSize: number;
        wordWrap: boolean;
        minimap: boolean;
        theme: string;
    };
}
export interface Organization {
    id: string;
    name: string;
    slug: string;
    description?: string;
    logo?: string;
    settings: OrganizationSettings;
    subscription: {
        plan: 'free' | 'pro' | 'enterprise';
        status: 'active' | 'cancelled' | 'past_due';
        limits: {
            users: number;
            projects: number;
            agents: number;
            storage: number;
        };
    };
    metadata: {
        createdAt: Date;
        updatedAt: Date;
        ownerId: string;
    };
}
export interface OrganizationSettings {
    allowUserRegistration: boolean;
    requireEmailVerification: boolean;
    enforceStrongPasswords: boolean;
    sessionTimeout: number;
    maxLoginAttempts: number;
    sso: {
        enabled: boolean;
        provider?: 'google' | 'microsoft' | 'okta' | 'auth0' | 'saml';
        config?: any;
    };
    security: {
        requireTwoFactor: boolean;
        allowedDomains: string[];
        ipWhitelist: string[];
    };
}
export interface Team {
    id: string;
    name: string;
    description?: string;
    organizationId: string;
    members: TeamMember[];
    projects: string[];
    settings: TeamSettings;
    metadata: {
        createdAt: Date;
        updatedAt: Date;
        createdBy: string;
    };
}
export interface TeamMember {
    userId: string;
    role: 'owner' | 'admin' | 'member' | 'viewer';
    joinedAt: Date;
    invitedBy: string;
}
export interface TeamSettings {
    visibility: 'public' | 'private';
    allowMemberInvites: boolean;
    defaultProjectRole: string;
}
export interface AuthToken {
    accessToken: string;
    refreshToken: string;
    expiresAt: Date;
    tokenType: 'Bearer';
    scope: string[];
}
export interface LoginAttempt {
    id: string;
    userId?: string;
    email: string;
    ipAddress: string;
    userAgent: string;
    success: boolean;
    failureReason?: string;
    timestamp: Date;
}
/**
 * Enterprise User Management System
 */
export declare class UserManagement extends EventEmitter {
    private users;
    private organizations;
    private teams;
    private roles;
    private sessions;
    private loginAttempts;
    private jwtSecret;
    private isInitialized;
    constructor(jwtSecret: string);
    /**
     * Initialize user management system
     */
    initialize(): Promise<void>;
    /**
     * Create system roles
     */
    private createSystemRoles;
    /**
     * Create default organization
     */
    private createDefaultOrganization;
    /**
     * Register new user
     */
    registerUser(userData: {
        email: string;
        username: string;
        password: string;
        firstName: string;
        lastName: string;
        organizationId?: string;
    }): Promise<User>;
    /**
     * Authenticate user
     */
    authenticateUser(email: string, password: string, ipAddress: string, userAgent: string): Promise<{
        user: User;
        tokens: AuthToken;
    }>;
    /**
     * Generate JWT tokens
     */
    private generateTokens;
    /**
     * Verify JWT token
     */
    verifyToken(token: string): Promise<User>;
    /**
     * Check user permissions
     */
    hasPermission(user: User, resource: string, action: string): boolean;
    /**
     * Match permission against resource and action
     */
    private matchesPermission;
    /**
     * Create team
     */
    createTeam(teamData: {
        name: string;
        description?: string;
        organizationId: string;
        createdBy: string;
    }): Promise<Team>;
    /**
     * Add user to team
     */
    addUserToTeam(teamId: string, userId: string, role: TeamMember['role'], invitedBy: string): Promise<void>;
    /**
     * Get default user preferences
     */
    private getDefaultPreferences;
    /**
     * Record login attempt
     */
    private recordLoginAttempt;
    /**
     * Get recent login attempts
     */
    private getRecentLoginAttempts;
    /**
     * Start cleanup intervals
     */
    private startCleanupIntervals;
    /**
     * Get user by ID
     */
    getUser(userId: string): User | undefined;
    /**
     * Get organization by ID
     */
    getOrganization(orgId: string): Organization | undefined;
    /**
     * Get team by ID
     */
    getTeam(teamId: string): Team | undefined;
    /**
     * Get users by organization
     */
    getUsersByOrganization(orgId: string): User[];
    /**
     * Get teams by organization
     */
    getTeamsByOrganization(orgId: string): Team[];
    /**
     * Shutdown user management
     */
    shutdown(): Promise<void>;
}
export declare const userManagement: UserManagement;
export default UserManagement;
//# sourceMappingURL=user-management.d.ts.map