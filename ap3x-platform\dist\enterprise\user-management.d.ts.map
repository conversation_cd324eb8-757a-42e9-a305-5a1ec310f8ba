{"version": 3, "file": "user-management.d.ts", "sourceRoot": "", "sources": ["../../src/enterprise/user-management.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;GAYG;AAEH,OAAO,EAAE,YAAY,EAAE,MAAM,QAAQ,CAAA;AAKrC,MAAM,WAAW,IAAI;IACnB,EAAE,EAAE,MAAM,CAAA;IACV,KAAK,EAAE,MAAM,CAAA;IACb,QAAQ,EAAE,MAAM,CAAA;IAChB,SAAS,EAAE,MAAM,CAAA;IACjB,QAAQ,EAAE,MAAM,CAAA;IAChB,MAAM,CAAC,EAAE,MAAM,CAAA;IACf,MAAM,EAAE,QAAQ,GAAG,UAAU,GAAG,WAAW,GAAG,SAAS,CAAA;IACvD,KAAK,EAAE,IAAI,EAAE,CAAA;IACb,KAAK,EAAE,MAAM,EAAE,CAAA;IACf,cAAc,EAAE,MAAM,CAAA;IACtB,WAAW,EAAE,eAAe,CAAA;IAC5B,QAAQ,EAAE;QACR,SAAS,EAAE,IAAI,CAAA;QACf,SAAS,EAAE,IAAI,CAAA;QACf,WAAW,CAAC,EAAE,IAAI,CAAA;QAClB,UAAU,EAAE,MAAM,CAAA;QAClB,aAAa,EAAE,OAAO,CAAA;QACtB,gBAAgB,EAAE,OAAO,CAAA;KAC1B,CAAA;CACF;AAED,MAAM,WAAW,IAAI;IACnB,EAAE,EAAE,MAAM,CAAA;IACV,IAAI,EAAE,MAAM,CAAA;IACZ,WAAW,EAAE,MAAM,CAAA;IACnB,WAAW,EAAE,UAAU,EAAE,CAAA;IACzB,YAAY,EAAE,OAAO,CAAA;IACrB,cAAc,CAAC,EAAE,MAAM,CAAA;CACxB;AAED,MAAM,WAAW,UAAU;IACzB,EAAE,EAAE,MAAM,CAAA;IACV,QAAQ,EAAE,MAAM,CAAA;IAChB,MAAM,EAAE,MAAM,CAAA;IACd,UAAU,CAAC,EAAE,mBAAmB,EAAE,CAAA;CACnC;AAED,MAAM,WAAW,mBAAmB;IAClC,KAAK,EAAE,MAAM,CAAA;IACb,QAAQ,EAAE,QAAQ,GAAG,YAAY,GAAG,IAAI,GAAG,QAAQ,GAAG,UAAU,GAAG,aAAa,CAAA;IAChF,KAAK,EAAE,GAAG,CAAA;CACX;AAED,MAAM,WAAW,eAAe;IAC9B,KAAK,EAAE,OAAO,GAAG,MAAM,GAAG,MAAM,CAAA;IAChC,QAAQ,EAAE,MAAM,CAAA;IAChB,QAAQ,EAAE,MAAM,CAAA;IAChB,aAAa,EAAE;QACb,KAAK,EAAE,OAAO,CAAA;QACd,IAAI,EAAE,OAAO,CAAA;QACb,OAAO,EAAE,OAAO,CAAA;QAChB,SAAS,EAAE,WAAW,GAAG,QAAQ,GAAG,OAAO,GAAG,QAAQ,CAAA;KACvD,CAAA;IACD,MAAM,EAAE;QACN,QAAQ,EAAE,MAAM,CAAA;QAChB,OAAO,EAAE,MAAM,CAAA;QACf,QAAQ,EAAE,OAAO,CAAA;QACjB,OAAO,EAAE,OAAO,CAAA;QAChB,KAAK,EAAE,MAAM,CAAA;KACd,CAAA;CACF;AAED,MAAM,WAAW,YAAY;IAC3B,EAAE,EAAE,MAAM,CAAA;IACV,IAAI,EAAE,MAAM,CAAA;IACZ,IAAI,EAAE,MAAM,CAAA;IACZ,WAAW,CAAC,EAAE,MAAM,CAAA;IACpB,IAAI,CAAC,EAAE,MAAM,CAAA;IACb,QAAQ,EAAE,oBAAoB,CAAA;IAC9B,YAAY,EAAE;QACZ,IAAI,EAAE,MAAM,GAAG,KAAK,GAAG,YAAY,CAAA;QACnC,MAAM,EAAE,QAAQ,GAAG,WAAW,GAAG,UAAU,CAAA;QAC3C,MAAM,EAAE;YACN,KAAK,EAAE,MAAM,CAAA;YACb,QAAQ,EAAE,MAAM,CAAA;YAChB,MAAM,EAAE,MAAM,CAAA;YACd,OAAO,EAAE,MAAM,CAAA;SAChB,CAAA;KACF,CAAA;IACD,QAAQ,EAAE;QACR,SAAS,EAAE,IAAI,CAAA;QACf,SAAS,EAAE,IAAI,CAAA;QACf,OAAO,EAAE,MAAM,CAAA;KAChB,CAAA;CACF;AAED,MAAM,WAAW,oBAAoB;IACnC,qBAAqB,EAAE,OAAO,CAAA;IAC9B,wBAAwB,EAAE,OAAO,CAAA;IACjC,sBAAsB,EAAE,OAAO,CAAA;IAC/B,cAAc,EAAE,MAAM,CAAA;IACtB,gBAAgB,EAAE,MAAM,CAAA;IACxB,GAAG,EAAE;QACH,OAAO,EAAE,OAAO,CAAA;QAChB,QAAQ,CAAC,EAAE,QAAQ,GAAG,WAAW,GAAG,MAAM,GAAG,OAAO,GAAG,MAAM,CAAA;QAC7D,MAAM,CAAC,EAAE,GAAG,CAAA;KACb,CAAA;IACD,QAAQ,EAAE;QACR,gBAAgB,EAAE,OAAO,CAAA;QACzB,cAAc,EAAE,MAAM,EAAE,CAAA;QACxB,WAAW,EAAE,MAAM,EAAE,CAAA;KACtB,CAAA;CACF;AAED,MAAM,WAAW,IAAI;IACnB,EAAE,EAAE,MAAM,CAAA;IACV,IAAI,EAAE,MAAM,CAAA;IACZ,WAAW,CAAC,EAAE,MAAM,CAAA;IACpB,cAAc,EAAE,MAAM,CAAA;IACtB,OAAO,EAAE,UAAU,EAAE,CAAA;IACrB,QAAQ,EAAE,MAAM,EAAE,CAAA;IAClB,QAAQ,EAAE,YAAY,CAAA;IACtB,QAAQ,EAAE;QACR,SAAS,EAAE,IAAI,CAAA;QACf,SAAS,EAAE,IAAI,CAAA;QACf,SAAS,EAAE,MAAM,CAAA;KAClB,CAAA;CACF;AAED,MAAM,WAAW,UAAU;IACzB,MAAM,EAAE,MAAM,CAAA;IACd,IAAI,EAAE,OAAO,GAAG,OAAO,GAAG,QAAQ,GAAG,QAAQ,CAAA;IAC7C,QAAQ,EAAE,IAAI,CAAA;IACd,SAAS,EAAE,MAAM,CAAA;CAClB;AAED,MAAM,WAAW,YAAY;IAC3B,UAAU,EAAE,QAAQ,GAAG,SAAS,CAAA;IAChC,kBAAkB,EAAE,OAAO,CAAA;IAC3B,kBAAkB,EAAE,MAAM,CAAA;CAC3B;AAED,MAAM,WAAW,SAAS;IACxB,WAAW,EAAE,MAAM,CAAA;IACnB,YAAY,EAAE,MAAM,CAAA;IACpB,SAAS,EAAE,IAAI,CAAA;IACf,SAAS,EAAE,QAAQ,CAAA;IACnB,KAAK,EAAE,MAAM,EAAE,CAAA;CAChB;AAED,MAAM,WAAW,YAAY;IAC3B,EAAE,EAAE,MAAM,CAAA;IACV,MAAM,CAAC,EAAE,MAAM,CAAA;IACf,KAAK,EAAE,MAAM,CAAA;IACb,SAAS,EAAE,MAAM,CAAA;IACjB,SAAS,EAAE,MAAM,CAAA;IACjB,OAAO,EAAE,OAAO,CAAA;IAChB,aAAa,CAAC,EAAE,MAAM,CAAA;IACtB,SAAS,EAAE,IAAI,CAAA;CAChB;AAED;;GAEG;AACH,qBAAa,cAAe,SAAQ,YAAY;IAC9C,OAAO,CAAC,KAAK,CAA+B;IAC5C,OAAO,CAAC,aAAa,CAAuC;IAC5D,OAAO,CAAC,KAAK,CAA+B;IAC5C,OAAO,CAAC,KAAK,CAA+B;IAC5C,OAAO,CAAC,QAAQ,CAA8D;IAC9E,OAAO,CAAC,aAAa,CAAyC;IAC9D,OAAO,CAAC,SAAS,CAAQ;IACzB,OAAO,CAAC,aAAa,CAAQ;gBAEjB,SAAS,EAAE,MAAM;IAK7B;;OAEG;IACG,UAAU,IAAI,OAAO,CAAC,IAAI,CAAC;IA2BjC;;OAEG;YACW,iBAAiB;IAiE/B;;OAEG;YACW,yBAAyB;IAwCvC;;OAEG;IACG,YAAY,CAAC,QAAQ,EAAE;QAC3B,KAAK,EAAE,MAAM,CAAA;QACb,QAAQ,EAAE,MAAM,CAAA;QAChB,QAAQ,EAAE,MAAM,CAAA;QAChB,SAAS,EAAE,MAAM,CAAA;QACjB,QAAQ,EAAE,MAAM,CAAA;QAChB,cAAc,CAAC,EAAE,MAAM,CAAA;KACxB,GAAG,OAAO,CAAC,IAAI,CAAC;IAkDjB;;OAEG;IACG,gBAAgB,CAAC,KAAK,EAAE,MAAM,EAAE,QAAQ,EAAE,MAAM,EAAE,SAAS,EAAE,MAAM,EAAE,SAAS,EAAE,MAAM,GAAG,OAAO,CAAC;QACrG,IAAI,EAAE,IAAI,CAAA;QACV,MAAM,EAAE,SAAS,CAAA;KAClB,CAAC;IAwEF;;OAEG;IACH,OAAO,CAAC,cAAc;IA6BtB;;OAEG;IACG,WAAW,CAAC,KAAK,EAAE,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC;IAe/C;;OAEG;IACH,aAAa,CAAC,IAAI,EAAE,IAAI,EAAE,QAAQ,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,GAAG,OAAO;IAWpE;;OAEG;IACH,OAAO,CAAC,iBAAiB;IAgBzB;;OAEG;IACG,UAAU,CAAC,QAAQ,EAAE;QACzB,IAAI,EAAE,MAAM,CAAA;QACZ,WAAW,CAAC,EAAE,MAAM,CAAA;QACpB,cAAc,EAAE,MAAM,CAAA;QACtB,SAAS,EAAE,MAAM,CAAA;KAClB,GAAG,OAAO,CAAC,IAAI,CAAC;IAiCjB;;OAEG;IACG,aAAa,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,UAAU,CAAC,MAAM,CAAC,EAAE,SAAS,EAAE,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC;IAoC/G;;OAEG;IACH,OAAO,CAAC,qBAAqB;IAqB7B;;OAEG;IACH,OAAO,CAAC,kBAAkB;IAO1B;;OAEG;IACH,OAAO,CAAC,sBAAsB;IAS9B;;OAEG;IACH,OAAO,CAAC,qBAAqB;IA0B7B;;OAEG;IACH,OAAO,CAAC,MAAM,EAAE,MAAM,GAAG,IAAI,GAAG,SAAS;IAIzC;;OAEG;IACH,eAAe,CAAC,KAAK,EAAE,MAAM,GAAG,YAAY,GAAG,SAAS;IAIxD;;OAEG;IACH,OAAO,CAAC,MAAM,EAAE,MAAM,GAAG,IAAI,GAAG,SAAS;IAIzC;;OAEG;IACH,sBAAsB,CAAC,KAAK,EAAE,MAAM,GAAG,IAAI,EAAE;IAI7C;;OAEG;IACH,sBAAsB,CAAC,KAAK,EAAE,MAAM,GAAG,IAAI,EAAE;IAI7C;;OAEG;IACG,QAAQ,IAAI,OAAO,CAAC,IAAI,CAAC;CAehC;AAGD,eAAO,MAAM,cAAc,gBAE1B,CAAA;AAED,eAAe,cAAc,CAAA"}