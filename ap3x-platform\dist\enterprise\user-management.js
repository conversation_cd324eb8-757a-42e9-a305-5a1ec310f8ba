"use strict";
/**
 * AP3X Platform - Enterprise User Management
 *
 * Comprehensive user management system for enterprise deployments.
 *
 * Features:
 * - User authentication and authorization
 * - Role-based access control (RBAC)
 * - Team and organization management
 * - Single Sign-On (SSO) integration
 * - Audit logging
 * - User activity tracking
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.userManagement = exports.UserManagement = void 0;
const events_1 = require("events");
const bcrypt_1 = __importDefault(require("bcrypt"));
const jsonwebtoken_1 = __importDefault(require("jsonwebtoken"));
const uuid_1 = require("uuid");
/**
 * Enterprise User Management System
 */
class UserManagement extends events_1.EventEmitter {
    constructor(jwtSecret) {
        super();
        this.users = new Map();
        this.organizations = new Map();
        this.teams = new Map();
        this.roles = new Map();
        this.sessions = new Map();
        this.loginAttempts = new Map();
        this.isInitialized = false;
        this.jwtSecret = jwtSecret;
    }
    /**
     * Initialize user management system
     */
    async initialize() {
        if (this.isInitialized)
            return;
        console.log('👥 Initializing User Management System...');
        try {
            // Create default system roles
            await this.createSystemRoles();
            // Create default organization if none exists
            if (this.organizations.size === 0) {
                await this.createDefaultOrganization();
            }
            // Start cleanup intervals
            this.startCleanupIntervals();
            this.isInitialized = true;
            this.emit('initialized');
            console.log('✅ User Management System initialized successfully');
        }
        catch (error) {
            console.error('❌ Failed to initialize User Management System:', error);
            throw error;
        }
    }
    /**
     * Create system roles
     */
    async createSystemRoles() {
        const systemRoles = [
            {
                id: 'system-admin',
                name: 'System Administrator',
                description: 'Full system access',
                permissions: [
                    { id: 'all', resource: '*', action: '*' }
                ],
                isSystemRole: true,
            },
            {
                id: 'org-admin',
                name: 'Organization Administrator',
                description: 'Organization management access',
                permissions: [
                    { id: 'org-manage', resource: 'organization', action: '*' },
                    { id: 'user-manage', resource: 'user', action: '*' },
                    { id: 'team-manage', resource: 'team', action: '*' },
                    { id: 'project-manage', resource: 'project', action: '*' },
                ],
                isSystemRole: true,
            },
            {
                id: 'team-lead',
                name: 'Team Lead',
                description: 'Team and project management',
                permissions: [
                    { id: 'team-manage', resource: 'team', action: 'read,write' },
                    { id: 'project-manage', resource: 'project', action: '*' },
                    { id: 'agent-manage', resource: 'agent', action: '*' },
                ],
                isSystemRole: true,
            },
            {
                id: 'developer',
                name: 'Developer',
                description: 'Development access',
                permissions: [
                    { id: 'project-access', resource: 'project', action: 'read,write' },
                    { id: 'agent-use', resource: 'agent', action: 'read,execute' },
                    { id: 'workflow-manage', resource: 'workflow', action: '*' },
                ],
                isSystemRole: true,
            },
            {
                id: 'viewer',
                name: 'Viewer',
                description: 'Read-only access',
                permissions: [
                    { id: 'project-view', resource: 'project', action: 'read' },
                    { id: 'agent-view', resource: 'agent', action: 'read' },
                    { id: 'workflow-view', resource: 'workflow', action: 'read' },
                ],
                isSystemRole: true,
            },
        ];
        for (const roleData of systemRoles) {
            this.roles.set(roleData.id, roleData);
        }
        console.log('✅ System roles created');
    }
    /**
     * Create default organization
     */
    async createDefaultOrganization() {
        const defaultOrg = {
            id: 'default-org',
            name: 'Default Organization',
            slug: 'default',
            description: 'Default organization for AP3X Platform',
            settings: {
                allowUserRegistration: true,
                requireEmailVerification: false,
                enforceStrongPasswords: true,
                sessionTimeout: 480, // 8 hours
                maxLoginAttempts: 5,
                sso: { enabled: false },
                security: {
                    requireTwoFactor: false,
                    allowedDomains: [],
                    ipWhitelist: [],
                },
            },
            subscription: {
                plan: 'enterprise',
                status: 'active',
                limits: {
                    users: 1000,
                    projects: 100,
                    agents: 50,
                    storage: 1000,
                },
            },
            metadata: {
                createdAt: new Date(),
                updatedAt: new Date(),
                ownerId: 'system',
            },
        };
        this.organizations.set(defaultOrg.id, defaultOrg);
        console.log('✅ Default organization created');
    }
    /**
     * Register new user
     */
    async registerUser(userData) {
        // Validate email uniqueness
        const existingUser = Array.from(this.users.values()).find(u => u.email === userData.email);
        if (existingUser) {
            throw new Error('Email already registered');
        }
        // Validate username uniqueness
        const existingUsername = Array.from(this.users.values()).find(u => u.username === userData.username);
        if (existingUsername) {
            throw new Error('Username already taken');
        }
        // Hash password
        const hashedPassword = await bcrypt_1.default.hash(userData.password, 12);
        // Create user
        const user = {
            id: (0, uuid_1.v4)(),
            email: userData.email,
            username: userData.username,
            firstName: userData.firstName,
            lastName: userData.lastName,
            status: 'active',
            roles: [this.roles.get('developer')], // Default role
            teams: [],
            organizationId: userData.organizationId || 'default-org',
            preferences: this.getDefaultPreferences(),
            metadata: {
                createdAt: new Date(),
                updatedAt: new Date(),
                loginCount: 0,
                emailVerified: false,
                twoFactorEnabled: false,
            },
        };
        // Store user (in production, this would be in a database)
        this.users.set(user.id, user)(user).hashedPassword = hashedPassword;
        this.emit('user_registered', { user });
        console.log(`👥 User registered: ${user.email}`);
        return user;
    }
    /**
     * Authenticate user
     */
    async authenticateUser(email, password, ipAddress, userAgent) {
        const loginAttempt = {
            id: (0, uuid_1.v4)(),
            email,
            ipAddress,
            userAgent,
            success: false,
            timestamp: new Date(),
        };
        try {
            // Find user
            const user = Array.from(this.users.values()).find(u => u.email === email);
            if (!user) {
                loginAttempt.failureReason = 'User not found';
                this.recordLoginAttempt(loginAttempt);
                throw new Error('Invalid credentials');
            }
            loginAttempt.userId = user.id;
            // Check if user is active
            if (user.status !== 'active') {
                loginAttempt.failureReason = 'Account inactive';
                this.recordLoginAttempt(loginAttempt);
                throw new Error('Account is not active');
            }
            // Check login attempts
            const recentAttempts = this.getRecentLoginAttempts(email);
            const org = this.organizations.get(user.organizationId);
            const maxAttempts = org?.settings.maxLoginAttempts || 5;
            if (recentAttempts.length >= maxAttempts) {
                loginAttempt.failureReason = 'Too many attempts';
                this.recordLoginAttempt(loginAttempt);
                throw new Error('Too many login attempts. Please try again later.');
            }
            // Verify password
            const hashedPassword = user.hashedPassword;
            const isValidPassword = await bcrypt_1.default.compare(password, hashedPassword);
            if (!isValidPassword) {
                loginAttempt.failureReason = 'Invalid password';
                this.recordLoginAttempt(loginAttempt);
                throw new Error('Invalid credentials');
            }
            // Generate tokens
            const tokens = this.generateTokens(user);
            // Update user metadata
            user.metadata.lastLoginAt = new Date();
            user.metadata.loginCount++;
            user.metadata.updatedAt = new Date();
            // Record successful login
            loginAttempt.success = true;
            this.recordLoginAttempt(loginAttempt);
            this.emit('user_authenticated', { user, ipAddress, userAgent });
            console.log(`👥 User authenticated: ${user.email}`);
            return { user, tokens };
        }
        catch (error) {
            this.recordLoginAttempt(loginAttempt);
            throw error;
        }
    }
    /**
     * Generate JWT tokens
     */
    generateTokens(user) {
        const payload = {
            userId: user.id,
            email: user.email,
            organizationId: user.organizationId,
            roles: user.roles.map(r => r.id),
        };
        const accessToken = jsonwebtoken_1.default.sign(payload, this.jwtSecret, {
            expiresIn: '1h',
            issuer: 'ap3x-platform',
            audience: 'ap3x-users',
        });
        const refreshToken = jsonwebtoken_1.default.sign({ userId: user.id, type: 'refresh' }, this.jwtSecret, { expiresIn: '7d' });
        return {
            accessToken,
            refreshToken,
            expiresAt: new Date(Date.now() + 60 * 60 * 1000), // 1 hour
            tokenType: 'Bearer',
            scope: user.roles.flatMap(r => r.permissions.map(p => `${p.resource}:${p.action}`)),
        };
    }
    /**
     * Verify JWT token
     */
    async verifyToken(token) {
        try {
            const payload = jsonwebtoken_1.default.verify(token, this.jwtSecret);
            const user = this.users.get(payload.userId);
            if (!user || user.status !== 'active') {
                throw new Error('Invalid token');
            }
            return user;
        }
        catch (error) {
            throw new Error('Invalid token');
        }
    }
    /**
     * Check user permissions
     */
    hasPermission(user, resource, action) {
        for (const role of user.roles) {
            for (const permission of role.permissions) {
                if (this.matchesPermission(permission, resource, action)) {
                    return true;
                }
            }
        }
        return false;
    }
    /**
     * Match permission against resource and action
     */
    matchesPermission(permission, resource, action) {
        // Check wildcard permissions
        if (permission.resource === '*' && permission.action === '*') {
            return true;
        }
        // Check resource match
        const resourceMatch = permission.resource === '*' || permission.resource === resource;
        // Check action match
        const actions = permission.action.split(',').map(a => a.trim());
        const actionMatch = actions.includes('*') || actions.includes(action);
        return resourceMatch && actionMatch;
    }
    /**
     * Create team
     */
    async createTeam(teamData) {
        const team = {
            id: (0, uuid_1.v4)(),
            name: teamData.name,
            description: teamData.description,
            organizationId: teamData.organizationId,
            members: [
                {
                    userId: teamData.createdBy,
                    role: 'owner',
                    joinedAt: new Date(),
                    invitedBy: teamData.createdBy,
                },
            ],
            projects: [],
            settings: {
                visibility: 'private',
                allowMemberInvites: true,
                defaultProjectRole: 'developer',
            },
            metadata: {
                createdAt: new Date(),
                updatedAt: new Date(),
                createdBy: teamData.createdBy,
            },
        };
        this.teams.set(team.id, team);
        this.emit('team_created', { team });
        return team;
    }
    /**
     * Add user to team
     */
    async addUserToTeam(teamId, userId, role, invitedBy) {
        const team = this.teams.get(teamId);
        if (!team) {
            throw new Error('Team not found');
        }
        const user = this.users.get(userId);
        if (!user) {
            throw new Error('User not found');
        }
        // Check if user is already a member
        const existingMember = team.members.find(m => m.userId === userId);
        if (existingMember) {
            throw new Error('User is already a team member');
        }
        // Add member
        team.members.push({
            userId,
            role,
            joinedAt: new Date(),
            invitedBy,
        });
        // Add team to user
        if (!user.teams.includes(teamId)) {
            user.teams.push(teamId);
        }
        team.metadata.updatedAt = new Date();
        user.metadata.updatedAt = new Date();
        this.emit('user_added_to_team', { teamId, userId, role });
    }
    /**
     * Get default user preferences
     */
    getDefaultPreferences() {
        return {
            theme: 'dark',
            language: 'en',
            timezone: 'UTC',
            notifications: {
                email: true,
                push: true,
                desktop: true,
                frequency: 'immediate',
            },
            editor: {
                fontSize: 14,
                tabSize: 2,
                wordWrap: true,
                minimap: true,
                theme: 'vs-dark',
            },
        };
    }
    /**
     * Record login attempt
     */
    recordLoginAttempt(attempt) {
        if (!this.loginAttempts.has(attempt.email)) {
            this.loginAttempts.set(attempt.email, []);
        }
        this.loginAttempts.get(attempt.email).push(attempt);
    }
    /**
     * Get recent login attempts
     */
    getRecentLoginAttempts(email) {
        const attempts = this.loginAttempts.get(email) || [];
        const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000);
        return attempts.filter(attempt => attempt.timestamp > oneHourAgo && !attempt.success);
    }
    /**
     * Start cleanup intervals
     */
    startCleanupIntervals() {
        // Clean up old login attempts every hour
        setInterval(() => {
            const oneDayAgo = new Date(Date.now() - 24 * 60 * 60 * 1000);
            for (const [email, attempts] of this.loginAttempts) {
                const recentAttempts = attempts.filter(attempt => attempt.timestamp > oneDayAgo);
                if (recentAttempts.length === 0) {
                    this.loginAttempts.delete(email);
                }
                else {
                    this.loginAttempts.set(email, recentAttempts);
                }
            }
        }, 60 * 60 * 1000);
        // Clean up expired sessions every 15 minutes
        setInterval(() => {
            const now = new Date();
            for (const [sessionId, session] of this.sessions) {
                if (session.expiresAt < now) {
                    this.sessions.delete(sessionId);
                }
            }
        }, 15 * 60 * 1000);
    }
    /**
     * Get user by ID
     */
    getUser(userId) {
        return this.users.get(userId);
    }
    /**
     * Get organization by ID
     */
    getOrganization(orgId) {
        return this.organizations.get(orgId);
    }
    /**
     * Get team by ID
     */
    getTeam(teamId) {
        return this.teams.get(teamId);
    }
    /**
     * Get users by organization
     */
    getUsersByOrganization(orgId) {
        return Array.from(this.users.values()).filter(user => user.organizationId === orgId);
    }
    /**
     * Get teams by organization
     */
    getTeamsByOrganization(orgId) {
        return Array.from(this.teams.values()).filter(team => team.organizationId === orgId);
    }
    /**
     * Shutdown user management
     */
    async shutdown() {
        console.log('🔄 Shutting down User Management System...');
        // Clear all data
        this.users.clear();
        this.organizations.clear();
        this.teams.clear();
        this.roles.clear();
        this.sessions.clear();
        this.loginAttempts.clear();
        this.isInitialized = false;
        this.removeAllListeners();
        console.log('✅ User Management System shutdown complete');
    }
}
exports.UserManagement = UserManagement;
// Export default instance
exports.userManagement = new UserManagement(process.env.JWT_SECRET || 'ap3x-platform-secret-key-change-in-production');
exports.default = UserManagement;
//# sourceMappingURL=user-management.js.map