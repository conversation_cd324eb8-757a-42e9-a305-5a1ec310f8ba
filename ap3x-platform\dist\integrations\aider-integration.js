"use strict";
/**
 * AP3X Platform - AIDER Integration
 *
 * Integrates AIDER's proven code editing capabilities with the AP3X platform.
 * Provides a bridge between AIDER's Python-based coder and our TypeScript platform.
 *
 * Features:
 * - AIDER process management
 * - Code editing request handling
 * - Git integration
 * - File watching and synchronization
 * - Error handling and recovery
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.aiderIntegration = exports.AiderIntegration = void 0;
const child_process_1 = require("child_process");
const events_1 = require("events");
const path_1 = __importDefault(require("path"));
const promises_1 = __importDefault(require("fs/promises"));
/**
 * AIDER Integration Manager
 */
class AiderIntegration extends events_1.EventEmitter {
    constructor(config) {
        super();
        this.processes = new Map();
        this.isInitialized = false;
        this.config = config;
    }
    /**
     * Initialize AIDER integration
     */
    async initialize() {
        if (this.isInitialized)
            return;
        console.log('🔧 Initializing AIDER Integration...');
        try {
            // Verify AIDER installation
            await this.verifyAiderInstallation();
            // Setup working directory
            await this.setupWorkingDirectory();
            // Test AIDER functionality
            await this.testAiderFunctionality();
            this.isInitialized = true;
            this.emit('initialized');
            console.log('✅ AIDER Integration initialized successfully');
        }
        catch (error) {
            console.error('❌ Failed to initialize AIDER Integration:', error);
            throw error;
        }
    }
    /**
     * Execute code editing with AIDER
     */
    async executeCodeEdit(sessionId, request) {
        if (!this.isInitialized) {
            throw new Error('AIDER Integration not initialized');
        }
        const startTime = Date.now();
        console.log(`🔧 Executing AIDER code edit for session ${sessionId}`);
        try {
            // Prepare AIDER command
            const command = this.buildAiderCommand(request);
            // Execute AIDER process
            const result = await this.runAiderProcess(sessionId, command, request.prompt);
            // Parse results
            const response = await this.parseAiderOutput(result, startTime);
            this.emit('code_edit_completed', { sessionId, request, response });
            return response;
        }
        catch (error) {
            console.error(`❌ AIDER code edit failed for session ${sessionId}:`, error);
            this.emit('code_edit_failed', { sessionId, request, error });
            throw error;
        }
    }
    /**
     * Verify AIDER installation
     */
    async verifyAiderInstallation() {
        return new Promise((resolve, reject) => {
            const process = (0, child_process_1.spawn)(this.config.pythonPath, ['-c', 'import aider; print(aider.__version__)']);
            let output = '';
            let error = '';
            process.stdout.on('data', (data) => {
                output += data.toString();
            });
            process.stderr.on('data', (data) => {
                error += data.toString();
            });
            process.on('close', (code) => {
                if (code === 0) {
                    console.log(`✅ AIDER version: ${output.trim()}`);
                    resolve();
                }
                else {
                    reject(new Error(`AIDER not found or not working: ${error}`));
                }
            });
            process.on('error', (err) => {
                reject(new Error(`Failed to check AIDER installation: ${err.message}`));
            });
        });
    }
    /**
     * Setup working directory
     */
    async setupWorkingDirectory() {
        try {
            await promises_1.default.access(this.config.workingDirectory);
        }
        catch {
            await promises_1.default.mkdir(this.config.workingDirectory, { recursive: true });
        }
        // Initialize git repository if needed
        if (this.config.gitIntegration) {
            await this.initializeGitRepo();
        }
    }
    /**
     * Initialize git repository
     */
    async initializeGitRepo() {
        return new Promise((resolve, reject) => {
            const process = (0, child_process_1.spawn)('git', ['init'], {
                cwd: this.config.workingDirectory
            });
            process.on('close', (code) => {
                if (code === 0) {
                    console.log('✅ Git repository initialized');
                    resolve();
                }
                else {
                    // Git repo might already exist, that's okay
                    resolve();
                }
            });
            process.on('error', (err) => {
                console.warn('⚠️ Git initialization failed:', err.message);
                resolve(); // Don't fail the entire initialization
            });
        });
    }
    /**
     * Test AIDER functionality
     */
    async testAiderFunctionality() {
        // Create a simple test file
        const testFile = path_1.default.join(this.config.workingDirectory, 'test.py');
        await promises_1.default.writeFile(testFile, '# Test file for AIDER\nprint("Hello, World!")\n');
        try {
            // Test AIDER with a simple request
            const testRequest = {
                prompt: 'Add a comment explaining what this code does',
                files: [testFile],
                dryRun: true
            };
            await this.executeCodeEdit('test', testRequest);
            console.log('✅ AIDER functionality test passed');
        }
        finally {
            // Clean up test file
            try {
                await promises_1.default.unlink(testFile);
            }
            catch {
                // Ignore cleanup errors
            }
        }
    }
    /**
     * Build AIDER command
     */
    buildAiderCommand(request) {
        const command = [
            this.config.pythonPath,
            '-m', 'aider'
        ];
        // Add model settings
        if (request.model || this.config.modelSettings.model) {
            command.push('--model', request.model || this.config.modelSettings.model);
        }
        // Add API key if provided
        if (this.config.modelSettings.apiKey) {
            command.push('--api-key', this.config.modelSettings.apiKey);
        }
        // Add files
        if (request.files.length > 0) {
            command.push(...request.files);
        }
        // Add read-only files
        if (request.readOnlyFiles && request.readOnlyFiles.length > 0) {
            command.push('--read', ...request.readOnlyFiles);
        }
        // Add edit format
        if (request.editFormat) {
            command.push('--edit-format', request.editFormat);
        }
        // Add flags
        if (request.dryRun) {
            command.push('--dry-run');
        }
        if (request.autoCommit !== false && this.config.autoCommit) {
            command.push('--auto-commits');
        }
        else {
            command.push('--no-auto-commits');
        }
        // Add other useful flags
        command.push('--yes'); // Auto-confirm
        command.push('--no-pretty'); // Disable pretty output for parsing
        return command;
    }
    /**
     * Run AIDER process
     */
    async runAiderProcess(sessionId, command, prompt) {
        return new Promise((resolve, reject) => {
            const process = (0, child_process_1.spawn)(command[0], command.slice(1), {
                cwd: this.config.workingDirectory,
                stdio: ['pipe', 'pipe', 'pipe']
            });
            const output = [];
            const errors = [];
            // Store process reference
            this.processes.set(sessionId, process);
            // Handle stdout
            process.stdout.on('data', (data) => {
                const lines = data.toString().split('\n').filter(Boolean);
                output.push(...lines);
                this.emit('aider_output', { sessionId, lines });
            });
            // Handle stderr
            process.stderr.on('data', (data) => {
                const lines = data.toString().split('\n').filter(Boolean);
                errors.push(...lines);
                this.emit('aider_error', { sessionId, lines });
            });
            // Handle process completion
            process.on('close', (code) => {
                this.processes.delete(sessionId);
                resolve({ output, errors, exitCode: code || 0 });
            });
            // Handle process errors
            process.on('error', (err) => {
                this.processes.delete(sessionId);
                reject(new Error(`AIDER process error: ${err.message}`));
            });
            // Send prompt to AIDER
            if (process.stdin) {
                process.stdin.write(prompt + '\n');
                process.stdin.end();
            }
            // Set timeout
            setTimeout(() => {
                if (this.processes.has(sessionId)) {
                    process.kill('SIGTERM');
                    this.processes.delete(sessionId);
                    reject(new Error('AIDER process timeout'));
                }
            }, 300000); // 5 minutes timeout
        });
    }
    /**
     * Parse AIDER output
     */
    async parseAiderOutput(result, startTime) {
        const executionTime = Date.now() - startTime;
        const success = result.exitCode === 0;
        // Parse file changes from output
        const changes = await this.parseFileChanges(result.output);
        // Extract commit hash if available
        const commitHash = this.extractCommitHash(result.output);
        return {
            success,
            changes,
            output: result.output,
            errors: result.errors,
            commitHash,
            executionTime
        };
    }
    /**
     * Parse file changes from AIDER output
     */
    async parseFileChanges(output) {
        const changes = [];
        // AIDER typically outputs file changes in a specific format
        // This is a simplified parser - in practice, you'd need more robust parsing
        for (const line of output) {
            if (line.includes('Modified:') || line.includes('Created:') || line.includes('Deleted:')) {
                const match = line.match(/(Modified|Created|Deleted):\s+(.+)/);
                if (match) {
                    const action = match[1].toLowerCase();
                    const filePath = match[2].trim();
                    changes.push({
                        path: filePath,
                        action: action === 'modify' ? 'modify' : action === 'create' ? 'create' : 'delete'
                    });
                }
            }
        }
        return changes;
    }
    /**
     * Extract commit hash from output
     */
    extractCommitHash(output) {
        for (const line of output) {
            const match = line.match(/Commit\s+([a-f0-9]{7,40})/i);
            if (match) {
                return match[1];
            }
        }
        return undefined;
    }
    /**
     * Kill AIDER process for session
     */
    async killProcess(sessionId) {
        const process = this.processes.get(sessionId);
        if (process) {
            process.kill('SIGTERM');
            this.processes.delete(sessionId);
            console.log(`🔧 Killed AIDER process for session ${sessionId}`);
        }
    }
    /**
     * Get active AIDER processes
     */
    getActiveProcesses() {
        return Array.from(this.processes.keys());
    }
    /**
     * Shutdown AIDER integration
     */
    async shutdown() {
        console.log('🔄 Shutting down AIDER Integration...');
        // Kill all active processes
        for (const sessionId of this.processes.keys()) {
            await this.killProcess(sessionId);
        }
        this.isInitialized = false;
        this.removeAllListeners();
        console.log('✅ AIDER Integration shutdown complete');
    }
}
exports.AiderIntegration = AiderIntegration;
// Export default instance
exports.aiderIntegration = new AiderIntegration({
    pythonPath: process.env.PYTHON_PATH || 'python',
    aiderPath: process.env.AIDER_PATH || 'aider',
    modelSettings: {
        model: process.env.AIDER_MODEL || 'gpt-4',
        apiKey: process.env.OPENAI_API_KEY,
    },
    gitIntegration: true,
    autoCommit: true,
    workingDirectory: process.env.AIDER_WORKING_DIR || './workspace'
});
exports.default = AiderIntegration;
//# sourceMappingURL=aider-integration.js.map