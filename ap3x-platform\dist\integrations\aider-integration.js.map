{"version": 3, "file": "aider-integration.js", "sourceRoot": "", "sources": ["../../src/integrations/aider-integration.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;GAYG;;;;;;AAEH,iDAAmD;AACnD,mCAAqC;AACrC,gDAAuB;AACvB,2DAA4B;AAmC5B;;GAEG;AACH,MAAa,gBAAiB,SAAQ,qBAAY;IAKhD,YAAY,MAAmB;QAC7B,KAAK,EAAE,CAAA;QAJD,cAAS,GAA8B,IAAI,GAAG,EAAE,CAAA;QAChD,kBAAa,GAAG,KAAK,CAAA;QAI3B,IAAI,CAAC,MAAM,GAAG,MAAM,CAAA;IACtB,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,UAAU;QACd,IAAI,IAAI,CAAC,aAAa;YAAE,OAAM;QAE9B,OAAO,CAAC,GAAG,CAAC,sCAAsC,CAAC,CAAA;QAEnD,IAAI,CAAC;YACH,4BAA4B;YAC5B,MAAM,IAAI,CAAC,uBAAuB,EAAE,CAAA;YAEpC,0BAA0B;YAC1B,MAAM,IAAI,CAAC,qBAAqB,EAAE,CAAA;YAElC,2BAA2B;YAC3B,MAAM,IAAI,CAAC,sBAAsB,EAAE,CAAA;YAEnC,IAAI,CAAC,aAAa,GAAG,IAAI,CAAA;YACzB,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,CAAA;YACxB,OAAO,CAAC,GAAG,CAAC,8CAA8C,CAAC,CAAA;QAE7D,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,2CAA2C,EAAE,KAAK,CAAC,CAAA;YACjE,MAAM,KAAK,CAAA;QACb,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,eAAe,CAAC,SAAiB,EAAE,OAAqB;QAC5D,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,CAAC;YACxB,MAAM,IAAI,KAAK,CAAC,mCAAmC,CAAC,CAAA;QACtD,CAAC;QAED,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAA;QAC5B,OAAO,CAAC,GAAG,CAAC,4CAA4C,SAAS,EAAE,CAAC,CAAA;QAEpE,IAAI,CAAC;YACH,wBAAwB;YACxB,MAAM,OAAO,GAAG,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAA;YAE/C,wBAAwB;YACxB,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,SAAS,EAAE,OAAO,EAAE,OAAO,CAAC,MAAM,CAAC,CAAA;YAE7E,gBAAgB;YAChB,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,gBAAgB,CAAC,MAAM,EAAE,SAAS,CAAC,CAAA;YAE/D,IAAI,CAAC,IAAI,CAAC,qBAAqB,EAAE,EAAE,SAAS,EAAE,OAAO,EAAE,QAAQ,EAAE,CAAC,CAAA;YAClE,OAAO,QAAQ,CAAA;QAEjB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,wCAAwC,SAAS,GAAG,EAAE,KAAK,CAAC,CAAA;YAC1E,IAAI,CAAC,IAAI,CAAC,kBAAkB,EAAE,EAAE,SAAS,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC,CAAA;YAC5D,MAAM,KAAK,CAAA;QACb,CAAC;IACH,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,uBAAuB;QACnC,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACrC,MAAM,OAAO,GAAG,IAAA,qBAAK,EAAC,IAAI,CAAC,MAAM,CAAC,UAAU,EAAE,CAAC,IAAI,EAAE,wCAAwC,CAAC,CAAC,CAAA;YAE/F,IAAI,MAAM,GAAG,EAAE,CAAA;YACf,IAAI,KAAK,GAAG,EAAE,CAAA;YAEd,OAAO,CAAC,MAAM,CAAC,EAAE,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,EAAE;gBACjC,MAAM,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAA;YAC3B,CAAC,CAAC,CAAA;YAEF,OAAO,CAAC,MAAM,CAAC,EAAE,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,EAAE;gBACjC,KAAK,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAA;YAC1B,CAAC,CAAC,CAAA;YAEF,OAAO,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,IAAI,EAAE,EAAE;gBAC3B,IAAI,IAAI,KAAK,CAAC,EAAE,CAAC;oBACf,OAAO,CAAC,GAAG,CAAC,oBAAoB,MAAM,CAAC,IAAI,EAAE,EAAE,CAAC,CAAA;oBAChD,OAAO,EAAE,CAAA;gBACX,CAAC;qBAAM,CAAC;oBACN,MAAM,CAAC,IAAI,KAAK,CAAC,mCAAmC,KAAK,EAAE,CAAC,CAAC,CAAA;gBAC/D,CAAC;YACH,CAAC,CAAC,CAAA;YAEF,OAAO,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,GAAG,EAAE,EAAE;gBAC1B,MAAM,CAAC,IAAI,KAAK,CAAC,uCAAuC,GAAG,CAAC,OAAO,EAAE,CAAC,CAAC,CAAA;YACzE,CAAC,CAAC,CAAA;QACJ,CAAC,CAAC,CAAA;IACJ,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,qBAAqB;QACjC,IAAI,CAAC;YACH,MAAM,kBAAE,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,gBAAgB,CAAC,CAAA;QAC/C,CAAC;QAAC,MAAM,CAAC;YACP,MAAM,kBAAE,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,gBAAgB,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAA;QACnE,CAAC;QAED,sCAAsC;QACtC,IAAI,IAAI,CAAC,MAAM,CAAC,cAAc,EAAE,CAAC;YAC/B,MAAM,IAAI,CAAC,iBAAiB,EAAE,CAAA;QAChC,CAAC;IACH,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,iBAAiB;QAC7B,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACrC,MAAM,OAAO,GAAG,IAAA,qBAAK,EAAC,KAAK,EAAE,CAAC,MAAM,CAAC,EAAE;gBACrC,GAAG,EAAE,IAAI,CAAC,MAAM,CAAC,gBAAgB;aAClC,CAAC,CAAA;YAEF,OAAO,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,IAAI,EAAE,EAAE;gBAC3B,IAAI,IAAI,KAAK,CAAC,EAAE,CAAC;oBACf,OAAO,CAAC,GAAG,CAAC,8BAA8B,CAAC,CAAA;oBAC3C,OAAO,EAAE,CAAA;gBACX,CAAC;qBAAM,CAAC;oBACN,4CAA4C;oBAC5C,OAAO,EAAE,CAAA;gBACX,CAAC;YACH,CAAC,CAAC,CAAA;YAEF,OAAO,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,GAAG,EAAE,EAAE;gBAC1B,OAAO,CAAC,IAAI,CAAC,+BAA+B,EAAE,GAAG,CAAC,OAAO,CAAC,CAAA;gBAC1D,OAAO,EAAE,CAAA,CAAC,uCAAuC;YACnD,CAAC,CAAC,CAAA;QACJ,CAAC,CAAC,CAAA;IACJ,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,sBAAsB;QAClC,4BAA4B;QAC5B,MAAM,QAAQ,GAAG,cAAI,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,gBAAgB,EAAE,SAAS,CAAC,CAAA;QACnE,MAAM,kBAAE,CAAC,SAAS,CAAC,QAAQ,EAAE,iDAAiD,CAAC,CAAA;QAE/E,IAAI,CAAC;YACH,mCAAmC;YACnC,MAAM,WAAW,GAAiB;gBAChC,MAAM,EAAE,8CAA8C;gBACtD,KAAK,EAAE,CAAC,QAAQ,CAAC;gBACjB,MAAM,EAAE,IAAI;aACb,CAAA;YAED,MAAM,IAAI,CAAC,eAAe,CAAC,MAAM,EAAE,WAAW,CAAC,CAAA;YAC/C,OAAO,CAAC,GAAG,CAAC,mCAAmC,CAAC,CAAA;QAElD,CAAC;gBAAS,CAAC;YACT,qBAAqB;YACrB,IAAI,CAAC;gBACH,MAAM,kBAAE,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAA;YAC3B,CAAC;YAAC,MAAM,CAAC;gBACP,wBAAwB;YAC1B,CAAC;QACH,CAAC;IACH,CAAC;IAED;;OAEG;IACK,iBAAiB,CAAC,OAAqB;QAC7C,MAAM,OAAO,GAAG;YACd,IAAI,CAAC,MAAM,CAAC,UAAU;YACtB,IAAI,EAAE,OAAO;SACd,CAAA;QAED,qBAAqB;QACrB,IAAI,OAAO,CAAC,KAAK,IAAI,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,KAAK,EAAE,CAAC;YACrD,OAAO,CAAC,IAAI,CAAC,SAAS,EAAE,OAAO,CAAC,KAAK,IAAI,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,KAAK,CAAC,CAAA;QAC3E,CAAC;QAED,0BAA0B;QAC1B,IAAI,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,MAAM,EAAE,CAAC;YACrC,OAAO,CAAC,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,MAAM,CAAC,CAAA;QAC7D,CAAC;QAED,YAAY;QACZ,IAAI,OAAO,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC7B,OAAO,CAAC,IAAI,CAAC,GAAG,OAAO,CAAC,KAAK,CAAC,CAAA;QAChC,CAAC;QAED,sBAAsB;QACtB,IAAI,OAAO,CAAC,aAAa,IAAI,OAAO,CAAC,aAAa,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC9D,OAAO,CAAC,IAAI,CAAC,QAAQ,EAAE,GAAG,OAAO,CAAC,aAAa,CAAC,CAAA;QAClD,CAAC;QAED,kBAAkB;QAClB,IAAI,OAAO,CAAC,UAAU,EAAE,CAAC;YACvB,OAAO,CAAC,IAAI,CAAC,eAAe,EAAE,OAAO,CAAC,UAAU,CAAC,CAAA;QACnD,CAAC;QAED,YAAY;QACZ,IAAI,OAAO,CAAC,MAAM,EAAE,CAAC;YACnB,OAAO,CAAC,IAAI,CAAC,WAAW,CAAC,CAAA;QAC3B,CAAC;QAED,IAAI,OAAO,CAAC,UAAU,KAAK,KAAK,IAAI,IAAI,CAAC,MAAM,CAAC,UAAU,EAAE,CAAC;YAC3D,OAAO,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAA;QAChC,CAAC;aAAM,CAAC;YACN,OAAO,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAA;QACnC,CAAC;QAED,yBAAyB;QACzB,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,CAAA,CAAC,eAAe;QACrC,OAAO,CAAC,IAAI,CAAC,aAAa,CAAC,CAAA,CAAC,oCAAoC;QAEhE,OAAO,OAAO,CAAA;IAChB,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,eAAe,CAC3B,SAAiB,EACjB,OAAiB,EACjB,MAAc;QAEd,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACrC,MAAM,OAAO,GAAG,IAAA,qBAAK,EAAC,OAAO,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE;gBAClD,GAAG,EAAE,IAAI,CAAC,MAAM,CAAC,gBAAgB;gBACjC,KAAK,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC;aAChC,CAAC,CAAA;YAEF,MAAM,MAAM,GAAa,EAAE,CAAA;YAC3B,MAAM,MAAM,GAAa,EAAE,CAAA;YAE3B,0BAA0B;YAC1B,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,SAAS,EAAE,OAAO,CAAC,CAAA;YAEtC,gBAAgB;YAChB,OAAO,CAAC,MAAM,CAAC,EAAE,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,EAAE;gBACjC,MAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,CAAA;gBACzD,MAAM,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC,CAAA;gBACrB,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,EAAE,SAAS,EAAE,KAAK,EAAE,CAAC,CAAA;YACjD,CAAC,CAAC,CAAA;YAEF,gBAAgB;YAChB,OAAO,CAAC,MAAM,CAAC,EAAE,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,EAAE;gBACjC,MAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,CAAA;gBACzD,MAAM,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC,CAAA;gBACrB,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,EAAE,SAAS,EAAE,KAAK,EAAE,CAAC,CAAA;YAChD,CAAC,CAAC,CAAA;YAEF,4BAA4B;YAC5B,OAAO,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,IAAI,EAAE,EAAE;gBAC3B,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,SAAS,CAAC,CAAA;gBAChC,OAAO,CAAC,EAAE,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,IAAI,CAAC,EAAE,CAAC,CAAA;YAClD,CAAC,CAAC,CAAA;YAEF,wBAAwB;YACxB,OAAO,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,GAAG,EAAE,EAAE;gBAC1B,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,SAAS,CAAC,CAAA;gBAChC,MAAM,CAAC,IAAI,KAAK,CAAC,wBAAwB,GAAG,CAAC,OAAO,EAAE,CAAC,CAAC,CAAA;YAC1D,CAAC,CAAC,CAAA;YAEF,uBAAuB;YACvB,IAAI,OAAO,CAAC,KAAK,EAAE,CAAC;gBAClB,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,MAAM,GAAG,IAAI,CAAC,CAAA;gBAClC,OAAO,CAAC,KAAK,CAAC,GAAG,EAAE,CAAA;YACrB,CAAC;YAED,cAAc;YACd,UAAU,CAAC,GAAG,EAAE;gBACd,IAAI,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,SAAS,CAAC,EAAE,CAAC;oBAClC,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,CAAA;oBACvB,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,SAAS,CAAC,CAAA;oBAChC,MAAM,CAAC,IAAI,KAAK,CAAC,uBAAuB,CAAC,CAAC,CAAA;gBAC5C,CAAC;YACH,CAAC,EAAE,MAAM,CAAC,CAAA,CAAC,oBAAoB;QACjC,CAAC,CAAC,CAAA;IACJ,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,gBAAgB,CAC5B,MAAgE,EAChE,SAAiB;QAEjB,MAAM,aAAa,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAA;QAC5C,MAAM,OAAO,GAAG,MAAM,CAAC,QAAQ,KAAK,CAAC,CAAA;QAErC,iCAAiC;QACjC,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,MAAM,CAAC,CAAA;QAE1D,mCAAmC;QACnC,MAAM,UAAU,GAAG,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,MAAM,CAAC,CAAA;QAExD,OAAO;YACL,OAAO;YACP,OAAO;YACP,MAAM,EAAE,MAAM,CAAC,MAAM;YACrB,MAAM,EAAE,MAAM,CAAC,MAAM;YACrB,UAAU;YACV,aAAa;SACd,CAAA;IACH,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,gBAAgB,CAAC,MAAgB;QAC7C,MAAM,OAAO,GAAiB,EAAE,CAAA;QAEhC,4DAA4D;QAC5D,4EAA4E;QAC5E,KAAK,MAAM,IAAI,IAAI,MAAM,EAAE,CAAC;YAC1B,IAAI,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,EAAE,CAAC;gBACzF,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,oCAAoC,CAAC,CAAA;gBAC9D,IAAI,KAAK,EAAE,CAAC;oBACV,MAAM,MAAM,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,WAAW,EAAoC,CAAA;oBACvE,MAAM,QAAQ,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAA;oBAEhC,OAAO,CAAC,IAAI,CAAC;wBACX,IAAI,EAAE,QAAQ;wBACd,MAAM,EAAE,MAAM,KAAK,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,MAAM,KAAK,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,QAAQ;qBACnF,CAAC,CAAA;gBACJ,CAAC;YACH,CAAC;QACH,CAAC;QAED,OAAO,OAAO,CAAA;IAChB,CAAC;IAED;;OAEG;IACK,iBAAiB,CAAC,MAAgB;QACxC,KAAK,MAAM,IAAI,IAAI,MAAM,EAAE,CAAC;YAC1B,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,4BAA4B,CAAC,CAAA;YACtD,IAAI,KAAK,EAAE,CAAC;gBACV,OAAO,KAAK,CAAC,CAAC,CAAC,CAAA;YACjB,CAAC;QACH,CAAC;QACD,OAAO,SAAS,CAAA;IAClB,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,WAAW,CAAC,SAAiB;QACjC,MAAM,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,SAAS,CAAC,CAAA;QAC7C,IAAI,OAAO,EAAE,CAAC;YACZ,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,CAAA;YACvB,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,SAAS,CAAC,CAAA;YAChC,OAAO,CAAC,GAAG,CAAC,uCAAuC,SAAS,EAAE,CAAC,CAAA;QACjE,CAAC;IACH,CAAC;IAED;;OAEG;IACH,kBAAkB;QAChB,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC,CAAA;IAC1C,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,QAAQ;QACZ,OAAO,CAAC,GAAG,CAAC,uCAAuC,CAAC,CAAA;QAEpD,4BAA4B;QAC5B,KAAK,MAAM,SAAS,IAAI,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,EAAE,CAAC;YAC9C,MAAM,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,CAAA;QACnC,CAAC;QAED,IAAI,CAAC,aAAa,GAAG,KAAK,CAAA;QAC1B,IAAI,CAAC,kBAAkB,EAAE,CAAA;QACzB,OAAO,CAAC,GAAG,CAAC,uCAAuC,CAAC,CAAA;IACtD,CAAC;CACF;AAnYD,4CAmYC;AAED,0BAA0B;AACb,QAAA,gBAAgB,GAAG,IAAI,gBAAgB,CAAC;IACnD,UAAU,EAAE,OAAO,CAAC,GAAG,CAAC,WAAW,IAAI,QAAQ;IAC/C,SAAS,EAAE,OAAO,CAAC,GAAG,CAAC,UAAU,IAAI,OAAO;IAC5C,aAAa,EAAE;QACb,KAAK,EAAE,OAAO,CAAC,GAAG,CAAC,WAAW,IAAI,OAAO;QACzC,MAAM,EAAE,OAAO,CAAC,GAAG,CAAC,cAAc;KACnC;IACD,cAAc,EAAE,IAAI;IACpB,UAAU,EAAE,IAAI;IAChB,gBAAgB,EAAE,OAAO,CAAC,GAAG,CAAC,iBAAiB,IAAI,aAAa;CACjE,CAAC,CAAA;AAEF,kBAAe,gBAAgB,CAAA"}