/**
 * AP3X Platform - Advanced Context Engine Integration
 *
 * Integrates the Neo4j-powered Context Engine with AIDER and AG3NT Framework
 * to provide deep codebase understanding and intelligent context retrieval.
 *
 * Features:
 * - Neo4j graph database integration
 * - Real-time code indexing
 * - Semantic code search
 * - Dependency analysis
 * - Code relationship mapping
 * - Intelligent context enhancement
 */
import { EventEmitter } from 'events';
import { FileChange } from '../core/ap3x-bridge';
export interface ContextEngineConfig {
    apiUrl: string;
    neo4jUrl?: string;
    neo4jUser?: string;
    neo4jPassword?: string;
    enableRealTimeIndexing: boolean;
    enableHybridRetrieval: boolean;
    maxResults: number;
    similarityThreshold: number;
}
export interface ContextQuery {
    query: string;
    files?: string[];
    includeRelated: boolean;
    maxResults?: number;
    filters?: ContextFilters;
    enhanceWithAI?: boolean;
}
export interface ContextFilters {
    fileTypes?: string[];
    directories?: string[];
    excludePatterns?: string[];
    dateRange?: {
        from: Date;
        to: Date;
    };
    authors?: string[];
    complexity?: 'low' | 'medium' | 'high';
}
export interface ContextResult {
    query: string;
    results: ContextItem[];
    insights: ContextInsight[];
    relationships: CodeRelationship[];
    suggestions: string[];
    executionTime: number;
    totalResults: number;
}
export interface ContextItem {
    id: string;
    type: 'file' | 'function' | 'class' | 'variable' | 'import' | 'comment';
    path: string;
    name: string;
    content: string;
    startLine: number;
    endLine: number;
    relevanceScore: number;
    metadata: {
        language: string;
        complexity: number;
        dependencies: string[];
        usages: number;
        lastModified: Date;
        author: string;
    };
}
export interface ContextInsight {
    type: 'pattern' | 'antipattern' | 'dependency' | 'duplication' | 'complexity' | 'security';
    title: string;
    description: string;
    severity: 'low' | 'medium' | 'high' | 'critical';
    affectedFiles: string[];
    suggestions: string[];
    confidence: number;
}
export interface CodeRelationship {
    id: string;
    type: 'imports' | 'extends' | 'implements' | 'calls' | 'references' | 'modifies';
    source: {
        file: string;
        name: string;
        type: string;
    };
    target: {
        file: string;
        name: string;
        type: string;
    };
    strength: number;
    metadata: any;
}
export interface IndexingRequest {
    projectPath: string;
    files?: string[];
    incremental?: boolean;
    includeTests?: boolean;
    includeNodeModules?: boolean;
}
export interface IndexingResult {
    success: boolean;
    filesIndexed: number;
    nodesCreated: number;
    relationshipsCreated: number;
    executionTime: number;
    errors: string[];
}
/**
 * Advanced Context Engine Integration
 */
export declare class ContextEngineIntegration extends EventEmitter {
    private config;
    private apiClient;
    private isInitialized;
    private indexingQueue;
    private isIndexing;
    constructor(config: ContextEngineConfig);
    /**
     * Initialize Context Engine integration
     */
    initialize(): Promise<void>;
    /**
     * Setup API interceptors
     */
    private setupInterceptors;
    /**
     * Test connection to Context Engine
     */
    private testConnection;
    /**
     * Setup real-time indexing
     */
    private setupRealTimeIndexing;
    /**
     * Query context with advanced search
     */
    queryContext(query: ContextQuery): Promise<ContextResult>;
    /**
     * Index project files
     */
    indexProject(request: IndexingRequest): Promise<IndexingResult>;
    /**
     * Index files incrementally
     */
    indexFiles(files: string[], projectPath: string): Promise<IndexingResult>;
    /**
     * Process indexing queue
     */
    private processIndexingQueue;
    /**
     * Enhance context for code editing
     */
    enhanceContextForEditing(prompt: string, files: string[], projectPath: string): Promise<{
        enhancedPrompt: string;
        relevantContext: ContextItem[];
        insights: ContextInsight[];
        suggestions: string[];
    }>;
    /**
     * Analyze code patterns
     */
    private analyzeCodePatterns;
    /**
     * Generate enhanced prompt
     */
    private generateEnhancedPrompt;
    /**
     * Track file changes for real-time indexing
     */
    trackFileChanges(changes: FileChange[], projectPath: string): Promise<void>;
    /**
     * Remove files from index
     */
    private removeFromIndex;
    /**
     * Get project statistics
     */
    getProjectStats(projectPath: string): Promise<{
        totalFiles: number;
        totalNodes: number;
        totalRelationships: number;
        languages: {
            [key: string]: number;
        };
        complexity: {
            average: number;
            distribution: {
                [key: string]: number;
            };
        };
        lastIndexed: Date;
    }>;
    /**
     * Search similar code
     */
    searchSimilarCode(codeSnippet: string, projectPath: string, options?: {
        language?: string;
        minSimilarity?: number;
        maxResults?: number;
    }): Promise<ContextItem[]>;
    /**
     * Get code dependencies
     */
    getCodeDependencies(filePath: string, projectPath: string): Promise<{
        dependencies: CodeRelationship[];
        dependents: CodeRelationship[];
        circularDependencies: string[][];
    }>;
    /**
     * Clear project index
     */
    clearProjectIndex(projectPath: string): Promise<void>;
    /**
     * Get health status
     */
    getHealthStatus(): Promise<{
        status: 'healthy' | 'degraded' | 'unhealthy';
        neo4j: boolean;
        api: boolean;
        indexing: boolean;
        lastCheck: Date;
    }>;
    /**
     * Shutdown Context Engine integration
     */
    shutdown(): Promise<void>;
}
export declare const contextEngineIntegration: ContextEngineIntegration;
export default ContextEngineIntegration;
//# sourceMappingURL=context-engine-integration.d.ts.map