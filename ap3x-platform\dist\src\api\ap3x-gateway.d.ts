/**
 * AP3X Platform - Unified API Gateway
 *
 * Central API gateway that routes requests between AIDER, AG3NT Framework,
 * and Context Engine, providing a unified interface for the frontend.
 *
 * Features:
 * - RESTful API endpoints
 * - WebSocket support for real-time updates
 * - Request routing and load balancing
 * - Authentication and authorization
 * - Rate limiting and monitoring
 * - Error handling and logging
 */
import { AP3XBridge } from '../core/ap3x-bridge';
export interface GatewayConfig {
    port: number;
    corsOrigins: string[];
    enableRateLimit: boolean;
    enableWebSockets: boolean;
    enableAuthentication: boolean;
}
export interface APIResponse<T = any> {
    success: boolean;
    data?: T;
    error?: string;
    timestamp: string;
    requestId: string;
}
/**
 * AP3X API Gateway
 */
export declare class AP3XGateway {
    private app;
    private server;
    private io?;
    private bridge;
    private config;
    private isRunning;
    constructor(bridge: AP3XBridge, config: GatewayConfig);
    /**
     * Setup Express middleware
     */
    private setupMiddleware;
    /**
     * Setup API routes
     */
    private setupRoutes;
    /**
     * Setup WebSocket connections
     */
    private setupWebSockets;
    /**
     * Setup event listeners for bridge events
     */
    private setupEventListeners;
    /**
     * Broadcast message to all clients
     */
    private broadcast;
    /**
     * Broadcast message to specific session
     */
    private broadcastToSession;
    /**
     * Create standardized API response
     */
    private createResponse;
    /**
     * Start the gateway server
     */
    start(): Promise<void>;
    /**
     * Stop the gateway server
     */
    stop(): Promise<void>;
}
export declare const ap3xGateway: AP3XGateway;
export default AP3XGateway;
declare global {
    namespace Express {
        interface Request {
            requestId: string;
        }
    }
}
//# sourceMappingURL=ap3x-gateway.d.ts.map