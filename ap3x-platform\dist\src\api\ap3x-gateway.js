"use strict";
/**
 * AP3X Platform - Unified API Gateway
 *
 * Central API gateway that routes requests between AIDER, AG3NT Framework,
 * and Context Engine, providing a unified interface for the frontend.
 *
 * Features:
 * - RESTful API endpoints
 * - WebSocket support for real-time updates
 * - Request routing and load balancing
 * - Authentication and authorization
 * - Rate limiting and monitoring
 * - Error handling and logging
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ap3xGateway = exports.AP3XGateway = void 0;
const express_1 = __importDefault(require("express"));
const http_1 = require("http");
const socket_io_1 = require("socket.io");
const cors_1 = __importDefault(require("cors"));
const helmet_1 = __importDefault(require("helmet"));
const express_rate_limit_1 = __importDefault(require("express-rate-limit"));
const ap3x_bridge_1 = require("../core/ap3x-bridge");
/**
 * AP3X API Gateway
 */
class AP3XGateway {
    constructor(bridge, config) {
        this.isRunning = false;
        this.bridge = bridge;
        this.config = config;
        this.app = (0, express_1.default)();
        this.server = (0, http_1.createServer)(this.app);
        if (config.enableWebSockets) {
            this.io = new socket_io_1.Server(this.server, {
                cors: {
                    origin: config.corsOrigins,
                    methods: ['GET', 'POST']
                }
            });
        }
        this.setupMiddleware();
        this.setupRoutes();
        this.setupWebSockets();
        this.setupEventListeners();
    }
    /**
     * Setup Express middleware
     */
    setupMiddleware() {
        // Security
        this.app.use((0, helmet_1.default)());
        // CORS
        this.app.use((0, cors_1.default)({
            origin: this.config.corsOrigins,
            credentials: true
        }));
        // Body parsing
        this.app.use(express_1.default.json({ limit: '10mb' }));
        this.app.use(express_1.default.urlencoded({ extended: true }));
        // Rate limiting
        if (this.config.enableRateLimit) {
            const limiter = (0, express_rate_limit_1.default)({
                windowMs: 15 * 60 * 1000, // 15 minutes
                max: 100, // limit each IP to 100 requests per windowMs
                message: 'Too many requests from this IP'
            });
            this.app.use('/api/', limiter);
        }
        // Request ID and logging
        this.app.use((req, res, next) => {
            req.requestId = `req-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
            console.log(`${new Date().toISOString()} [${req.requestId}] ${req.method} ${req.path}`);
            next();
        });
    }
    /**
     * Setup API routes
     */
    setupRoutes() {
        // Health check
        this.app.get('/api/health', (req, res) => {
            res.json(this.createResponse(true, {
                status: 'healthy',
                platform: 'AP3X',
                version: '1.0.0',
                uptime: process.uptime(),
                components: {
                    bridge: this.bridge ? 'connected' : 'disconnected',
                    websockets: this.io ? 'enabled' : 'disabled'
                }
            }, req.requestId));
        });
        // Session management
        this.app.post('/api/sessions', async (req, res) => {
            try {
                const { userId, projectId } = req.body;
                const sessionId = await this.bridge.createSession(userId, projectId);
                res.json(this.createResponse(true, { sessionId }, req.requestId));
            }
            catch (error) {
                res.status(500).json(this.createResponse(false, null, req.requestId, error.message));
            }
        });
        this.app.get('/api/sessions/:sessionId', (req, res) => {
            try {
                const session = this.bridge.getSession(req.params.sessionId);
                if (!session) {
                    return res.status(404).json(this.createResponse(false, null, req.requestId, 'Session not found'));
                }
                res.json(this.createResponse(true, session, req.requestId));
            }
            catch (error) {
                res.status(500).json(this.createResponse(false, null, req.requestId, error.message));
            }
        });
        this.app.get('/api/sessions', (req, res) => {
            try {
                const sessions = this.bridge.getActiveSessions();
                res.json(this.createResponse(true, sessions, req.requestId));
            }
            catch (error) {
                res.status(500).json(this.createResponse(false, null, req.requestId, error.message));
            }
        });
        // Code editing
        this.app.post('/api/code/edit', async (req, res) => {
            try {
                const result = await this.bridge.executeCodeEditing(req.body);
                res.json(this.createResponse(true, result, req.requestId));
            }
            catch (error) {
                res.status(500).json(this.createResponse(false, null, req.requestId, error.message));
            }
        });
        // Agent management
        this.app.get('/api/agents', async (req, res) => {
            try {
                // Get registered agents from AG3NT Framework
                const agents = await this.bridge.ag3ntFramework.getRegisteredAgents();
                res.json(this.createResponse(true, agents, req.requestId));
            }
            catch (error) {
                res.status(500).json(this.createResponse(false, null, req.requestId, error.message));
            }
        });
        this.app.post('/api/agents/:agentType/execute', async (req, res) => {
            try {
                const { agentType } = req.params;
                const result = await this.bridge.ag3ntFramework.execute(agentType, req.body);
                res.json(this.createResponse(true, result, req.requestId));
            }
            catch (error) {
                res.status(500).json(this.createResponse(false, null, req.requestId, error.message));
            }
        });
        // Context engine
        this.app.post('/api/context/query', async (req, res) => {
            try {
                const result = await this.bridge.contextEngine.getContext(req.body.query, req.body.options);
                res.json(this.createResponse(true, result, req.requestId));
            }
            catch (error) {
                res.status(500).json(this.createResponse(false, null, req.requestId, error.message));
            }
        });
        // Project management
        this.app.get('/api/projects', (req, res) => {
            // Placeholder for project management
            res.json(this.createResponse(true, [], req.requestId));
        });
        this.app.post('/api/projects', (req, res) => {
            // Placeholder for project creation
            res.json(this.createResponse(true, { id: 'project-1' }, req.requestId));
        });
        // Workflow management
        this.app.get('/api/workflows', (req, res) => {
            // Placeholder for workflow management
            res.json(this.createResponse(true, [], req.requestId));
        });
        this.app.post('/api/workflows', (req, res) => {
            // Placeholder for workflow creation
            res.json(this.createResponse(true, { id: 'workflow-1' }, req.requestId));
        });
        // Error handling
        this.app.use((error, req, res, next) => {
            console.error(`Error in ${req.method} ${req.path}:`, error);
            res.status(500).json(this.createResponse(false, null, req.requestId, 'Internal server error'));
        });
        // 404 handler
        this.app.use('*', (req, res) => {
            res.status(404).json(this.createResponse(false, null, req.requestId, 'Endpoint not found'));
        });
    }
    /**
     * Setup WebSocket connections
     */
    setupWebSockets() {
        if (!this.io)
            return;
        this.io.on('connection', (socket) => {
            console.log(`WebSocket client connected: ${socket.id}`);
            // Join session room
            socket.on('join_session', (sessionId) => {
                socket.join(`session:${sessionId}`);
                console.log(`Client ${socket.id} joined session ${sessionId}`);
            });
            // Leave session room
            socket.on('leave_session', (sessionId) => {
                socket.leave(`session:${sessionId}`);
                console.log(`Client ${socket.id} left session ${sessionId}`);
            });
            // Handle real-time code editing
            socket.on('code_edit_request', async (data) => {
                try {
                    const result = await this.bridge.executeCodeEditing(data);
                    socket.emit('code_edit_result', result);
                    // Broadcast to session room
                    if (data.sessionId) {
                        socket.to(`session:${data.sessionId}`).emit('code_edit_update', result);
                    }
                }
                catch (error) {
                    socket.emit('code_edit_error', { error: error.message });
                }
            });
            socket.on('disconnect', () => {
                console.log(`WebSocket client disconnected: ${socket.id}`);
            });
        });
    }
    /**
     * Setup event listeners for bridge events
     */
    setupEventListeners() {
        // Bridge events
        this.bridge.on('session_created', (data) => {
            this.broadcastToSession(data.sessionId, 'session_created', data);
        });
        this.bridge.on('code_editing_completed', (data) => {
            this.broadcastToSession(data.sessionId, 'code_editing_completed', data);
        });
        this.bridge.on('agent_registered', (data) => {
            this.broadcast('agent_registered', data);
        });
        this.bridge.on('task_completed', (data) => {
            this.broadcast('task_completed', data);
        });
        this.bridge.on('context_updated', (data) => {
            this.broadcast('context_updated', data);
        });
    }
    /**
     * Broadcast message to all clients
     */
    broadcast(event, data) {
        if (this.io) {
            this.io.emit(event, data);
        }
    }
    /**
     * Broadcast message to specific session
     */
    broadcastToSession(sessionId, event, data) {
        if (this.io) {
            this.io.to(`session:${sessionId}`).emit(event, data);
        }
    }
    /**
     * Create standardized API response
     */
    createResponse(success, data, requestId, error) {
        return {
            success,
            data: data || undefined,
            error,
            timestamp: new Date().toISOString(),
            requestId
        };
    }
    /**
     * Start the gateway server
     */
    async start() {
        if (this.isRunning)
            return;
        // Initialize the bridge first
        await this.bridge.initialize();
        return new Promise((resolve) => {
            this.server.listen(this.config.port, () => {
                this.isRunning = true;
                console.log(`🚀 AP3X Gateway running on port ${this.config.port}`);
                console.log(`🌐 WebSockets: ${this.config.enableWebSockets ? 'enabled' : 'disabled'}`);
                resolve();
            });
        });
    }
    /**
     * Stop the gateway server
     */
    async stop() {
        if (!this.isRunning)
            return;
        return new Promise((resolve) => {
            this.server.close(() => {
                this.isRunning = false;
                console.log('🔄 AP3X Gateway stopped');
                resolve();
            });
        });
    }
}
exports.AP3XGateway = AP3XGateway;
// Create and export gateway instance
exports.ap3xGateway = new AP3XGateway(ap3x_bridge_1.ap3xBridge, {
    port: 3000,
    corsOrigins: ['http://localhost:3001', 'http://localhost:3000'],
    enableRateLimit: true,
    enableWebSockets: true,
    enableAuthentication: false // Will be enabled later
});
exports.default = AP3XGateway;
//# sourceMappingURL=ap3x-gateway.js.map