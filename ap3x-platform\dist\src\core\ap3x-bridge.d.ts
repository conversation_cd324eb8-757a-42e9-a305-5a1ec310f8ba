/**
 * AP3X Platform - Core Integration Bridge
 *
 * The central orchestrator that unifies AIDER, AG3NT Framework, and Context Engine
 * into the most powerful agentic coding platform ever created.
 *
 * Features:
 * - AIDER's proven code editing capabilities
 * - AG3NT's multi-agent coordination
 * - Context Engine's advanced code understanding
 * - Real-time collaboration and monitoring
 * - Visual workflow management
 * - Enterprise-grade features
 */
import { EventEmitter } from 'events';
export interface AP3XConfig {
    aider: AiderConfig;
    ag3nt: AG3NTConfig;
    contextEngine: ContextEngineConfig;
    platform: PlatformConfig;
}
export interface AiderConfig {
    pythonPath?: string;
    modelSettings?: any;
    gitIntegration: boolean;
    browserUI: boolean;
    streamlitPort?: number;
}
export interface AG3NTConfig {
    enableMCP: boolean;
    enableSequentialThinking: boolean;
    enableRAG: boolean;
    enableTaskDelegation: boolean;
    enableConsensus: boolean;
    enableWorkflowHandoffs: boolean;
    maxConcurrentAgents: number;
}
export interface ContextEngineConfig {
    neo4jUrl?: string;
    neo4jUser?: string;
    neo4jPassword?: string;
    enableHybridRetrieval: boolean;
    enableRealTimeIndexing: boolean;
    apiPort?: number;
}
export interface PlatformConfig {
    port: number;
    enableWebSockets: boolean;
    enableRealTimeCollaboration: boolean;
    enableVisualWorkflows: boolean;
    enableEnterpriseFeatures: boolean;
    maxConcurrentSessions: number;
}
export interface AP3XSession {
    sessionId: string;
    userId: string;
    projectId: string;
    aiderCoder?: any;
    ag3ntAgents: string[];
    contextScope: any;
    status: 'active' | 'paused' | 'completed' | 'error';
    startTime: Date;
    lastActivity: Date;
}
export interface CodeEditingRequest {
    sessionId: string;
    prompt: string;
    files: string[];
    agentType?: string;
    useMultiAgent: boolean;
    contextEnhancement: boolean;
}
export interface CodeEditingResult {
    success: boolean;
    changes: FileChange[];
    agentResults: any[];
    contextInsights: any[];
    executionTime: number;
    cost?: number;
}
export interface FileChange {
    path: string;
    action: 'create' | 'modify' | 'delete';
    content?: string;
    diff?: string;
    lineNumbers?: [number, number];
}
/**
 * AP3X Bridge - The core integration layer
 */
export declare class AP3XBridge extends EventEmitter {
    private aiderProcess?;
    private ag3ntFramework;
    private contextEngine;
    private sessions;
    private config;
    private isInitialized;
    constructor(config: AP3XConfig);
    /**
     * Initialize the AP3X platform
     */
    initialize(): Promise<void>;
    /**
     * Initialize AIDER integration
     */
    private initializeAiderIntegration;
    /**
     * Setup event listeners for cross-component communication
     */
    private setupEventListeners;
    /**
     * Create a new coding session
     */
    createSession(userId: string, projectId: string): Promise<string>;
    /**
     * Execute code editing with unified AI capabilities
     */
    executeCodeEditing(request: CodeEditingRequest): Promise<CodeEditingResult>;
    /**
     * Enhance context using the Context Engine
     */
    private enhanceContext;
    /**
     * Execute multi-agent code editing
     */
    private executeMultiAgentEditing;
    /**
     * Execute single-agent code editing using AIDER
     */
    private executeSingleAgentEditing;
    /**
     * Parse agent results into file changes
     */
    private parseAgentChanges;
    /**
     * Get session information
     */
    getSession(sessionId: string): AP3XSession | undefined;
    /**
     * Get all active sessions
     */
    getActiveSessions(): AP3XSession[];
    /**
     * Shutdown the platform
     */
    shutdown(): Promise<void>;
}
export declare const ap3xBridge: AP3XBridge;
export default AP3XBridge;
//# sourceMappingURL=ap3x-bridge.d.ts.map