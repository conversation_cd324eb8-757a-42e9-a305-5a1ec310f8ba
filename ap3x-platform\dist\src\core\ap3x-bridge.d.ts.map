{"version": 3, "file": "ap3x-bridge.d.ts", "sourceRoot": "", "sources": ["../../../src/core/ap3x-bridge.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;GAaG;AAEH,OAAO,EAAE,YAAY,EAAE,MAAM,QAAQ,CAAA;AAIrC,MAAM,WAAW,UAAU;IACzB,KAAK,EAAE,WAAW,CAAA;IAClB,KAAK,EAAE,WAAW,CAAA;IAClB,aAAa,EAAE,mBAAmB,CAAA;IAClC,QAAQ,EAAE,cAAc,CAAA;CACzB;AAED,MAAM,WAAW,WAAW;IAC1B,UAAU,CAAC,EAAE,MAAM,CAAA;IACnB,aAAa,CAAC,EAAE,GAAG,CAAA;IACnB,cAAc,EAAE,OAAO,CAAA;IACvB,SAAS,EAAE,OAAO,CAAA;IAClB,aAAa,CAAC,EAAE,MAAM,CAAA;CACvB;AAED,MAAM,WAAW,WAAW;IAC1B,SAAS,EAAE,OAAO,CAAA;IAClB,wBAAwB,EAAE,OAAO,CAAA;IACjC,SAAS,EAAE,OAAO,CAAA;IAClB,oBAAoB,EAAE,OAAO,CAAA;IAC7B,eAAe,EAAE,OAAO,CAAA;IACxB,sBAAsB,EAAE,OAAO,CAAA;IAC/B,mBAAmB,EAAE,MAAM,CAAA;CAC5B;AAED,MAAM,WAAW,mBAAmB;IAClC,QAAQ,CAAC,EAAE,MAAM,CAAA;IACjB,SAAS,CAAC,EAAE,MAAM,CAAA;IAClB,aAAa,CAAC,EAAE,MAAM,CAAA;IACtB,qBAAqB,EAAE,OAAO,CAAA;IAC9B,sBAAsB,EAAE,OAAO,CAAA;IAC/B,OAAO,CAAC,EAAE,MAAM,CAAA;CACjB;AAED,MAAM,WAAW,cAAc;IAC7B,IAAI,EAAE,MAAM,CAAA;IACZ,gBAAgB,EAAE,OAAO,CAAA;IACzB,2BAA2B,EAAE,OAAO,CAAA;IACpC,qBAAqB,EAAE,OAAO,CAAA;IAC9B,wBAAwB,EAAE,OAAO,CAAA;IACjC,qBAAqB,EAAE,MAAM,CAAA;CAC9B;AAED,MAAM,WAAW,WAAW;IAC1B,SAAS,EAAE,MAAM,CAAA;IACjB,MAAM,EAAE,MAAM,CAAA;IACd,SAAS,EAAE,MAAM,CAAA;IACjB,UAAU,CAAC,EAAE,GAAG,CAAA;IAChB,WAAW,EAAE,MAAM,EAAE,CAAA;IACrB,YAAY,EAAE,GAAG,CAAA;IACjB,MAAM,EAAE,QAAQ,GAAG,QAAQ,GAAG,WAAW,GAAG,OAAO,CAAA;IACnD,SAAS,EAAE,IAAI,CAAA;IACf,YAAY,EAAE,IAAI,CAAA;CACnB;AAED,MAAM,WAAW,kBAAkB;IACjC,SAAS,EAAE,MAAM,CAAA;IACjB,MAAM,EAAE,MAAM,CAAA;IACd,KAAK,EAAE,MAAM,EAAE,CAAA;IACf,SAAS,CAAC,EAAE,MAAM,CAAA;IAClB,aAAa,EAAE,OAAO,CAAA;IACtB,kBAAkB,EAAE,OAAO,CAAA;CAC5B;AAED,MAAM,WAAW,iBAAiB;IAChC,OAAO,EAAE,OAAO,CAAA;IAChB,OAAO,EAAE,UAAU,EAAE,CAAA;IACrB,YAAY,EAAE,GAAG,EAAE,CAAA;IACnB,eAAe,EAAE,GAAG,EAAE,CAAA;IACtB,aAAa,EAAE,MAAM,CAAA;IACrB,IAAI,CAAC,EAAE,MAAM,CAAA;CACd;AAED,MAAM,WAAW,UAAU;IACzB,IAAI,EAAE,MAAM,CAAA;IACZ,MAAM,EAAE,QAAQ,GAAG,QAAQ,GAAG,QAAQ,CAAA;IACtC,OAAO,CAAC,EAAE,MAAM,CAAA;IAChB,IAAI,CAAC,EAAE,MAAM,CAAA;IACb,WAAW,CAAC,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC,CAAA;CAC/B;AAED;;GAEG;AACH,qBAAa,UAAW,SAAQ,YAAY;IAC1C,OAAO,CAAC,YAAY,CAAC,CAAK;IAC1B,OAAO,CAAC,cAAc,CAAgB;IACtC,OAAO,CAAC,aAAa,CAAsB;IAC3C,OAAO,CAAC,QAAQ,CAAsC;IACtD,OAAO,CAAC,MAAM,CAAY;IAC1B,OAAO,CAAC,aAAa,CAAQ;gBAEjB,MAAM,EAAE,UAAU;IA4B9B;;OAEG;IACG,UAAU,IAAI,OAAO,CAAC,IAAI,CAAC;IA+BjC;;OAEG;YACW,0BAA0B;IAMxC;;OAEG;IACH,OAAO,CAAC,mBAAmB;IAgB3B;;OAEG;IACG,aAAa,CAAC,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC;IAoBvE;;OAEG;IACG,kBAAkB,CAAC,OAAO,EAAE,kBAAkB,GAAG,OAAO,CAAC,iBAAiB,CAAC;IAsCjF;;OAEG;YACW,cAAc;IAe5B;;OAEG;YACW,wBAAwB;IAsBtC;;OAEG;YACW,yBAAyB;IAevC;;OAEG;IACH,OAAO,CAAC,iBAAiB;IAKzB;;OAEG;IACH,UAAU,CAAC,SAAS,EAAE,MAAM,GAAG,WAAW,GAAG,SAAS;IAItD;;OAEG;IACH,iBAAiB,IAAI,WAAW,EAAE;IAIlC;;OAEG;IACG,QAAQ,IAAI,OAAO,CAAC,IAAI,CAAC;CAgBhC;AAGD,eAAO,MAAM,UAAU,YA4BrB,CAAA;AAEF,eAAe,UAAU,CAAA"}