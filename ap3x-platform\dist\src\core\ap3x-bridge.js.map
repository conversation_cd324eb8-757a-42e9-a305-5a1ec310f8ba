{"version": 3, "file": "ap3x-bridge.js", "sourceRoot": "", "sources": ["../../../src/core/ap3x-bridge.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;GAaG;;;AAEH,mCAAqC;AACrC,6FAAwF;AACxF,mHAA6G;AAmF7G;;GAEG;AACH,MAAa,UAAW,SAAQ,qBAAY;IAQ1C,YAAY,MAAkB;QAC5B,KAAK,EAAE,CAAA;QALD,aAAQ,GAA6B,IAAI,GAAG,EAAE,CAAA;QAE9C,kBAAa,GAAG,KAAK,CAAA;QAI3B,IAAI,CAAC,MAAM,GAAG,MAAM,CAAA;QAEpB,6BAA6B;QAC7B,IAAI,CAAC,cAAc,GAAG,IAAI,gCAAc,CAAC;YACvC,aAAa,EAAE;gBACb,SAAS,EAAE,MAAM,CAAC,KAAK,CAAC,SAAS;gBACjC,wBAAwB,EAAE,MAAM,CAAC,KAAK,CAAC,wBAAwB;gBAC/D,SAAS,EAAE,MAAM,CAAC,KAAK,CAAC,SAAS;aAClC;YACD,YAAY,EAAE;gBACZ,oBAAoB,EAAE,MAAM,CAAC,KAAK,CAAC,oBAAoB;gBACvD,eAAe,EAAE,MAAM,CAAC,KAAK,CAAC,eAAe;gBAC7C,sBAAsB,EAAE,MAAM,CAAC,KAAK,CAAC,sBAAsB;aAC5D;YACD,MAAM,EAAE;gBACN,qBAAqB,EAAE,MAAM,CAAC,KAAK,CAAC,mBAAmB;aACxD;SACF,CAAC,CAAA;QAEF,4BAA4B;QAC5B,IAAI,CAAC,aAAa,GAAG,IAAI,6CAAoB,CAAC;YAC5C,qBAAqB,EAAE,MAAM,CAAC,aAAa,CAAC,qBAAqB;YACjE,sBAAsB,EAAE,MAAM,CAAC,aAAa,CAAC,sBAAsB;SACpE,CAAC,CAAA;IACJ,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,UAAU;QACd,IAAI,IAAI,CAAC,aAAa;YAAE,OAAM;QAE9B,OAAO,CAAC,GAAG,CAAC,kCAAkC,CAAC,CAAA;QAE/C,IAAI,CAAC;YACH,6BAA6B;YAC7B,OAAO,CAAC,GAAG,CAAC,oCAAoC,CAAC,CAAA;YACjD,MAAM,IAAI,CAAC,cAAc,CAAC,UAAU,EAAE,CAAA;YAEtC,4BAA4B;YAC5B,OAAO,CAAC,GAAG,CAAC,mCAAmC,CAAC,CAAA;YAChD,MAAM,IAAI,CAAC,aAAa,CAAC,UAAU,EAAE,CAAA;YAErC,+BAA+B;YAC/B,OAAO,CAAC,GAAG,CAAC,qCAAqC,CAAC,CAAA;YAClD,MAAM,IAAI,CAAC,0BAA0B,EAAE,CAAA;YAEvC,wBAAwB;YACxB,IAAI,CAAC,mBAAmB,EAAE,CAAA;YAE1B,IAAI,CAAC,aAAa,GAAG,IAAI,CAAA;YACzB,IAAI,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAA;YACjC,OAAO,CAAC,GAAG,CAAC,2CAA2C,CAAC,CAAA;QAE1D,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,uCAAuC,EAAE,KAAK,CAAC,CAAA;YAC7D,MAAM,KAAK,CAAA;QACb,CAAC;IACH,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,0BAA0B;QACtC,oEAAoE;QACpE,gDAAgD;QAChD,OAAO,CAAC,GAAG,CAAC,2CAA2C,CAAC,CAAA;IAC1D,CAAC;IAED;;OAEG;IACK,mBAAmB;QACzB,yBAAyB;QACzB,IAAI,CAAC,cAAc,CAAC,EAAE,CAAC,kBAAkB,EAAE,CAAC,IAAI,EAAE,EAAE;YAClD,IAAI,CAAC,IAAI,CAAC,kBAAkB,EAAE,IAAI,CAAC,CAAA;QACrC,CAAC,CAAC,CAAA;QAEF,IAAI,CAAC,cAAc,CAAC,EAAE,CAAC,gBAAgB,EAAE,CAAC,IAAI,EAAE,EAAE;YAChD,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE,IAAI,CAAC,CAAA;QACnC,CAAC,CAAC,CAAA;QAEF,wBAAwB;QACxB,IAAI,CAAC,aAAa,CAAC,EAAE,CAAC,iBAAiB,EAAE,CAAC,IAAI,EAAE,EAAE;YAChD,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE,IAAI,CAAC,CAAA;QACpC,CAAC,CAAC,CAAA;IACJ,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,aAAa,CAAC,MAAc,EAAE,SAAiB;QACnD,MAAM,SAAS,GAAG,QAAQ,IAAI,CAAC,GAAG,EAAE,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAA;QAEjF,MAAM,OAAO,GAAgB;YAC3B,SAAS;YACT,MAAM;YACN,SAAS;YACT,WAAW,EAAE,EAAE;YACf,YAAY,EAAE,EAAE;YAChB,MAAM,EAAE,QAAQ;YAChB,SAAS,EAAE,IAAI,IAAI,EAAE;YACrB,YAAY,EAAE,IAAI,IAAI,EAAE;SACzB,CAAA;QAED,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,SAAS,EAAE,OAAO,CAAC,CAAA;QACrC,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE,EAAE,SAAS,EAAE,MAAM,EAAE,SAAS,EAAE,CAAC,CAAA;QAE9D,OAAO,SAAS,CAAA;IAClB,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,kBAAkB,CAAC,OAA2B;QAClD,MAAM,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,OAAO,CAAC,SAAS,CAAC,CAAA;QACpD,IAAI,CAAC,OAAO,EAAE,CAAC;YACb,MAAM,IAAI,KAAK,CAAC,WAAW,OAAO,CAAC,SAAS,YAAY,CAAC,CAAA;QAC3D,CAAC;QAED,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAA;QAE5B,IAAI,CAAC;YACH,0BAA0B;YAC1B,OAAO,CAAC,YAAY,GAAG,IAAI,IAAI,EAAE,CAAA;YAEjC,+BAA+B;YAC/B,IAAI,eAAe,GAAU,EAAE,CAAA;YAC/B,IAAI,OAAO,CAAC,kBAAkB,EAAE,CAAC;gBAC/B,eAAe,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,CAAA;YACtD,CAAC;YAED,oCAAoC;YACpC,IAAI,MAAyB,CAAA;YAC7B,IAAI,OAAO,CAAC,aAAa,EAAE,CAAC;gBAC1B,MAAM,GAAG,MAAM,IAAI,CAAC,wBAAwB,CAAC,OAAO,EAAE,eAAe,CAAC,CAAA;YACxE,CAAC;iBAAM,CAAC;gBACN,MAAM,GAAG,MAAM,IAAI,CAAC,yBAAyB,CAAC,OAAO,EAAE,eAAe,CAAC,CAAA;YACzE,CAAC;YAED,2BAA2B;YAC3B,MAAM,CAAC,aAAa,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAA;YAE7C,IAAI,CAAC,IAAI,CAAC,wBAAwB,EAAE,EAAE,SAAS,EAAE,OAAO,CAAC,SAAS,EAAE,MAAM,EAAE,CAAC,CAAA;YAC7E,OAAO,MAAM,CAAA;QAEf,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,sBAAsB,EAAE,KAAK,CAAC,CAAA;YAC5C,MAAM,KAAK,CAAA;QACb,CAAC;IACH,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,cAAc,CAAC,OAA2B;QACtD,IAAI,CAAC;YACH,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,OAAO,CAAC,MAAM,EAAE;gBACxE,KAAK,EAAE,OAAO,CAAC,KAAK;gBACpB,cAAc,EAAE,IAAI;gBACpB,UAAU,EAAE,EAAE;aACf,CAAC,CAAA;YAEF,OAAO,aAAa,CAAC,QAAQ,IAAI,EAAE,CAAA;QACrC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,IAAI,CAAC,6BAA6B,EAAE,KAAK,CAAC,CAAA;YAClD,OAAO,EAAE,CAAA;QACX,CAAC;IACH,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,wBAAwB,CACpC,OAA2B,EAC3B,eAAsB;QAEtB,mDAAmD;QACnD,MAAM,SAAS,GAAG,OAAO,CAAC,SAAS,IAAI,UAAU,CAAA;QAEjD,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,SAAS,EAAE;YAC1D,MAAM,EAAE,OAAO,CAAC,MAAM;YACtB,KAAK,EAAE,OAAO,CAAC,KAAK;YACpB,eAAe;SAChB,CAAC,CAAA;QAEF,OAAO;YACL,OAAO,EAAE,MAAM,CAAC,OAAO;YACvB,OAAO,EAAE,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC;YACvC,YAAY,EAAE,CAAC,MAAM,CAAC;YACtB,eAAe;YACf,aAAa,EAAE,CAAC,CAAC,wBAAwB;SAC1C,CAAA;IACH,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,yBAAyB,CACrC,OAA2B,EAC3B,eAAsB;QAEtB,yCAAyC;QACzC,gCAAgC;QAChC,OAAO;YACL,OAAO,EAAE,IAAI;YACb,OAAO,EAAE,EAAE;YACX,YAAY,EAAE,EAAE;YAChB,eAAe;YACf,aAAa,EAAE,CAAC;SACjB,CAAA;IACH,CAAC;IAED;;OAEG;IACK,iBAAiB,CAAC,MAAW;QACnC,qDAAqD;QACrD,OAAO,EAAE,CAAA;IACX,CAAC;IAED;;OAEG;IACH,UAAU,CAAC,SAAiB;QAC1B,OAAO,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,SAAS,CAAC,CAAA;IACrC,CAAC;IAED;;OAEG;IACH,iBAAiB;QACf,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,KAAK,QAAQ,CAAC,CAAA;IAC9E,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,QAAQ;QACZ,OAAO,CAAC,GAAG,CAAC,mCAAmC,CAAC,CAAA;QAEhD,2BAA2B;QAC3B,MAAM,IAAI,CAAC,cAAc,CAAC,QAAQ,EAAE,CAAA;QAEpC,0BAA0B;QAC1B,MAAM,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAE,CAAA;QAEnC,iBAAiB;QACjB,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAA;QAErB,IAAI,CAAC,aAAa,GAAG,KAAK,CAAA;QAC1B,IAAI,CAAC,kBAAkB,EAAE,CAAA;QACzB,OAAO,CAAC,GAAG,CAAC,mCAAmC,CAAC,CAAA;IAClD,CAAC;CACF;AAxQD,gCAwQC;AAED,4BAA4B;AACf,QAAA,UAAU,GAAG,IAAI,UAAU,CAAC;IACvC,KAAK,EAAE;QACL,cAAc,EAAE,IAAI;QACpB,SAAS,EAAE,IAAI;QACf,aAAa,EAAE,IAAI;KACpB;IACD,KAAK,EAAE;QACL,SAAS,EAAE,IAAI;QACf,wBAAwB,EAAE,IAAI;QAC9B,SAAS,EAAE,IAAI;QACf,oBAAoB,EAAE,IAAI;QAC1B,eAAe,EAAE,IAAI;QACrB,sBAAsB,EAAE,IAAI;QAC5B,mBAAmB,EAAE,EAAE;KACxB;IACD,aAAa,EAAE;QACb,qBAAqB,EAAE,IAAI;QAC3B,sBAAsB,EAAE,IAAI;QAC5B,OAAO,EAAE,IAAI;KACd;IACD,QAAQ,EAAE;QACR,IAAI,EAAE,IAAI;QACV,gBAAgB,EAAE,IAAI;QACtB,2BAA2B,EAAE,IAAI;QACjC,qBAAqB,EAAE,IAAI;QAC3B,wBAAwB,EAAE,IAAI;QAC9B,qBAAqB,EAAE,GAAG;KAC3B;CACF,CAAC,CAAA;AAEF,kBAAe,UAAU,CAAA"}