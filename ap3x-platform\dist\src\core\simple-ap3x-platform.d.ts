/**
 * AP3X Platform - Simplified Startup Version
 *
 * A simplified version of the AP3X Platform that can start without
 * all the complex dependencies, demonstrating the core concept.
 */
import { EventEmitter } from 'events';
export interface SimpleAP3XConfig {
    port: number;
    host: string;
    environment: 'development' | 'staging' | 'production';
    enableCORS: boolean;
    enableRateLimit: boolean;
    corsOrigins: string[];
}
export declare class SimpleAP3XPlatform extends EventEmitter {
    private config;
    private app;
    private server;
    private io;
    private isRunning;
    constructor(config: SimpleAP3XConfig);
    private setupMiddleware;
    private setupRoutes;
    private setupWebSocket;
    start(): Promise<void>;
    stop(): Promise<void>;
    getStatus(): {
        running: boolean;
        port: number;
        host: string;
        environment: "development" | "staging" | "production";
        uptime: number;
        timestamp: string;
    };
}
export default SimpleAP3XPlatform;
//# sourceMappingURL=simple-ap3x-platform.d.ts.map