{"version": 3, "file": "simple-ap3x-platform.js", "sourceRoot": "", "sources": ["../../../src/core/simple-ap3x-platform.ts"], "names": [], "mappings": ";AAAA;;;;;GAKG;;;;;;AAEH,sDAA6B;AAC7B,+BAAmC;AACnC,yCAAoD;AACpD,gDAAuB;AACvB,oDAA2B;AAC3B,4EAA0C;AAC1C,mCAAqC;AAWrC,MAAa,kBAAmB,SAAQ,qBAAY;IAOlD,YAAY,MAAwB;QAClC,KAAK,EAAE,CAAA;QAHD,cAAS,GAAG,KAAK,CAAA;QAIvB,IAAI,CAAC,MAAM,GAAG,MAAM,CAAA;QACpB,IAAI,CAAC,GAAG,GAAG,IAAA,iBAAO,GAAE,CAAA;QACpB,IAAI,CAAC,MAAM,GAAG,IAAA,mBAAY,EAAC,IAAI,CAAC,GAAG,CAAC,CAAA;QACpC,IAAI,CAAC,EAAE,GAAG,IAAI,kBAAc,CAAC,IAAI,CAAC,MAAM,EAAE;YACxC,IAAI,EAAE;gBACJ,MAAM,EAAE,MAAM,CAAC,WAAW;gBAC1B,OAAO,EAAE,CAAC,KAAK,EAAE,MAAM,CAAC;aACzB;SACF,CAAC,CAAA;QAEF,IAAI,CAAC,eAAe,EAAE,CAAA;QACtB,IAAI,CAAC,WAAW,EAAE,CAAA;QAClB,IAAI,CAAC,cAAc,EAAE,CAAA;IACvB,CAAC;IAEO,eAAe;QACrB,sBAAsB;QACtB,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,IAAA,gBAAM,GAAE,CAAC,CAAA;QAEtB,OAAO;QACP,IAAI,IAAI,CAAC,MAAM,CAAC,UAAU,EAAE,CAAC;YAC3B,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,IAAA,cAAI,EAAC;gBAChB,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC,WAAW;gBAC/B,WAAW,EAAE,IAAI;aAClB,CAAC,CAAC,CAAA;QACL,CAAC;QAED,gBAAgB;QAChB,IAAI,IAAI,CAAC,MAAM,CAAC,eAAe,EAAE,CAAC;YAChC,MAAM,OAAO,GAAG,IAAA,4BAAS,EAAC;gBACxB,QAAQ,EAAE,EAAE,GAAG,EAAE,GAAG,IAAI,EAAE,aAAa;gBACvC,GAAG,EAAE,GAAG,EAAE,6CAA6C;gBACvD,OAAO,EAAE,gCAAgC;aAC1C,CAAC,CAAA;YACF,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,OAAO,EAAE,OAAO,CAAC,CAAA;QAChC,CAAC;QAED,eAAe;QACf,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,iBAAO,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,MAAM,EAAE,CAAC,CAAC,CAAA;QAC7C,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,iBAAO,CAAC,UAAU,CAAC,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC,CAAC,CAAA;QAEpD,kBAAkB;QAClB,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,EAAE;YAC9B,OAAO,CAAC,GAAG,CAAC,GAAG,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,MAAM,GAAG,CAAC,MAAM,IAAI,GAAG,CAAC,IAAI,EAAE,CAAC,CAAA;YACtE,IAAI,EAAE,CAAA;QACR,CAAC,CAAC,CAAA;IACJ,CAAC;IAEO,WAAW;QACjB,eAAe;QACf,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,aAAa,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE;YACvC,GAAG,CAAC,IAAI,CAAC;gBACP,MAAM,EAAE,SAAS;gBACjB,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;gBACnC,OAAO,EAAE,OAAO;gBAChB,QAAQ,EAAE,MAAM;gBAChB,UAAU,EAAE;oBACV,GAAG,EAAE,OAAO;oBACZ,UAAU,EAAE,OAAO;oBACnB,QAAQ,EAAE,OAAO;iBAClB;aACF,CAAC,CAAA;QACJ,CAAC,CAAC,CAAA;QAEF,gBAAgB;QAChB,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,WAAW,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE;YACrC,GAAG,CAAC,IAAI,CAAC;gBACP,IAAI,EAAE,eAAe;gBACrB,WAAW,EAAE,wDAAwD;gBACrE,OAAO,EAAE,OAAO;gBAChB,QAAQ,EAAE;oBACR,0BAA0B;oBAC1B,yBAAyB;oBACzB,0BAA0B;oBAC1B,yBAAyB;oBACzB,qBAAqB;iBACtB;gBACD,MAAM,EAAE,SAAS;gBACjB,MAAM,EAAE,OAAO,CAAC,MAAM,EAAE;gBACxB,WAAW,EAAE,IAAI,CAAC,MAAM,CAAC,WAAW;aACrC,CAAC,CAAA;QACJ,CAAC,CAAC,CAAA;QAEF,2BAA2B;QAC3B,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,eAAe,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE;YACzC,GAAG,CAAC,IAAI,CAAC;gBACP,OAAO,EAAE,IAAI;gBACb,IAAI,EAAE;oBACJ;wBACE,SAAS,EAAE,gBAAgB;wBAC3B,MAAM,EAAE,WAAW;wBACnB,SAAS,EAAE,cAAc;wBACzB,MAAM,EAAE,QAAQ;wBAChB,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;wBACnC,UAAU,EAAE,CAAC;qBACd;iBACF;gBACD,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;aACpC,CAAC,CAAA;QACJ,CAAC,CAAC,CAAA;QAEF,yBAAyB;QACzB,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,aAAa,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE;YACvC,GAAG,CAAC,IAAI,CAAC;gBACP,OAAO,EAAE,IAAI;gBACb,IAAI,EAAE;oBACJ;wBACE,EAAE,EAAE,kBAAkB;wBACtB,IAAI,EAAE,UAAU;wBAChB,IAAI,EAAE,gBAAgB;wBACtB,MAAM,EAAE,QAAQ;wBAChB,YAAY,EAAE,CAAC,UAAU,EAAE,UAAU,EAAE,cAAc,CAAC;qBACvD;oBACD;wBACE,EAAE,EAAE,kBAAkB;wBACtB,IAAI,EAAE,gBAAgB;wBACtB,IAAI,EAAE,gBAAgB;wBACtB,MAAM,EAAE,QAAQ;wBAChB,YAAY,EAAE,CAAC,OAAO,EAAE,YAAY,EAAE,KAAK,EAAE,WAAW,CAAC;qBAC1D;oBACD;wBACE,EAAE,EAAE,iBAAiB;wBACrB,IAAI,EAAE,eAAe;wBACrB,IAAI,EAAE,eAAe;wBACrB,MAAM,EAAE,QAAQ;wBAChB,YAAY,EAAE,CAAC,QAAQ,EAAE,QAAQ,EAAE,WAAW,EAAE,MAAM,CAAC;qBACxD;iBACF;gBACD,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;aACpC,CAAC,CAAA;QACJ,CAAC,CAAC,CAAA;QAEF,+BAA+B;QAC/B,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,gBAAgB,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE;YAC3C,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,aAAa,EAAE,GAAG,GAAG,CAAC,IAAI,CAAA;YAEjD,sBAAsB;YACtB,UAAU,CAAC,GAAG,EAAE;gBACd,GAAG,CAAC,IAAI,CAAC;oBACP,OAAO,EAAE,IAAI;oBACb,IAAI,EAAE;wBACJ,SAAS,EAAE,gBAAgB;wBAC3B,OAAO,EAAE;4BACP;gCACE,IAAI,EAAE,KAAK,EAAE,CAAC,CAAC,CAAC,IAAI,SAAS;gCAC7B,MAAM,EAAE,QAAQ;gCAChB,WAAW,EAAE,wBAAwB,MAAM,EAAE;6BAC9C;yBACF;wBACD,UAAU,EAAE,aAAa,CAAC,CAAC,CAAC,CAAC,kBAAkB,EAAE,kBAAkB,CAAC,CAAC,CAAC,CAAC,CAAC,kBAAkB,CAAC;wBAC3F,aAAa,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,IAAI,CAAC,GAAG,IAAI;qBACvD;oBACD,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;iBACpC,CAAC,CAAA;YACJ,CAAC,EAAE,IAAI,CAAC,CAAA;QACV,CAAC,CAAC,CAAA;QAEF,2BAA2B;QAC3B,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,QAAQ,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE;YAClC,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBACnB,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,wBAAwB;gBAC/B,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;aACpC,CAAC,CAAA;QACJ,CAAC,CAAC,CAAA;QAEF,oCAAoC;QACpC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,iBAAO,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAA;QAEtC,gBAAgB;QAChB,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE;YAC7B,GAAG,CAAC,IAAI,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OA+DR,CAAC,CAAA;QACJ,CAAC,CAAC,CAAA;IACJ,CAAC;IAEO,cAAc;QACpB,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,YAAY,EAAE,CAAC,MAAM,EAAE,EAAE;YAClC,OAAO,CAAC,GAAG,CAAC,wBAAwB,MAAM,CAAC,EAAE,EAAE,CAAC,CAAA;YAEhD,uBAAuB;YACvB,MAAM,CAAC,IAAI,CAAC,iBAAiB,EAAE;gBAC7B,MAAM,EAAE,WAAW;gBACnB,QAAQ,EAAE,MAAM;gBAChB,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;aACpC,CAAC,CAAA;YAEF,qBAAqB;YACrB,MAAM,CAAC,EAAE,CAAC,sBAAsB,EAAE,CAAC,IAAI,EAAE,EAAE;gBACzC,OAAO,CAAC,GAAG,CAAC,6BAA6B,EAAE,IAAI,CAAC,CAAA;gBAEhD,0BAA0B;gBAC1B,UAAU,CAAC,GAAG,EAAE;oBACd,MAAM,CAAC,IAAI,CAAC,qBAAqB,EAAE;wBACjC,OAAO,EAAE,kBAAkB;wBAC3B,MAAM,EAAE,SAAS;wBACjB,IAAI,EAAE,wBAAwB;qBAC/B,CAAC,CAAA;gBACJ,CAAC,EAAE,IAAI,CAAC,CAAA;gBAER,UAAU,CAAC,GAAG,EAAE;oBACd,MAAM,CAAC,IAAI,CAAC,qBAAqB,EAAE;wBACjC,OAAO,EAAE,kBAAkB;wBAC3B,MAAM,EAAE,SAAS;wBACjB,IAAI,EAAE,4BAA4B;qBACnC,CAAC,CAAA;gBACJ,CAAC,EAAE,IAAI,CAAC,CAAA;gBAER,UAAU,CAAC,GAAG,EAAE;oBACd,MAAM,CAAC,IAAI,CAAC,mBAAmB,EAAE;wBAC/B,SAAS,EAAE,IAAI,CAAC,SAAS;wBACzB,MAAM,EAAE,SAAS;wBACjB,OAAO,EAAE,CAAC,oBAAoB,EAAE,oBAAoB,CAAC;qBACtD,CAAC,CAAA;gBACJ,CAAC,EAAE,IAAI,CAAC,CAAA;YACV,CAAC,CAAC,CAAA;YAEF,MAAM,CAAC,EAAE,CAAC,YAAY,EAAE,GAAG,EAAE;gBAC3B,OAAO,CAAC,GAAG,CAAC,2BAA2B,MAAM,CAAC,EAAE,EAAE,CAAC,CAAA;YACrD,CAAC,CAAC,CAAA;QACJ,CAAC,CAAC,CAAA;IACJ,CAAC;IAED,KAAK,CAAC,KAAK;QACT,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;YACnB,OAAO,CAAC,GAAG,CAAC,qCAAqC,CAAC,CAAA;YAClD,OAAM;QACR,CAAC;QAED,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACrC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,GAAG,EAAE;gBAC1D,IAAI,CAAC,SAAS,GAAG,IAAI,CAAA;gBACrB,OAAO,CAAC,GAAG,CAAC;;;;;;;;;;;;;;;;;;;;oBAoBA,IAAI,CAAC,MAAM,CAAC,IAAI,IAAI,IAAI,CAAC,MAAM,CAAC,IAAI;oBACpC,IAAI,CAAC,MAAM,CAAC,IAAI,IAAI,IAAI,CAAC,MAAM,CAAC,IAAI;kBACtC,IAAI,CAAC,MAAM,CAAC,IAAI,IAAI,IAAI,CAAC,MAAM,CAAC,IAAI;oBAClC,IAAI,CAAC,MAAM,CAAC,IAAI,IAAI,IAAI,CAAC,MAAM,CAAC,IAAI;;;SAG/C,CAAC,CAAA;gBAEF,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAA;gBACpB,OAAO,EAAE,CAAA;YACX,CAAC,CAAC,CAAA;YAEF,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,KAAU,EAAE,EAAE;gBACrC,OAAO,CAAC,KAAK,CAAC,kCAAkC,EAAE,KAAK,CAAC,CAAA;gBACxD,MAAM,CAAC,KAAK,CAAC,CAAA;YACf,CAAC,CAAC,CAAA;QACJ,CAAC,CAAC,CAAA;IACJ,CAAC;IAED,KAAK,CAAC,IAAI;QACR,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC;YACpB,OAAO,CAAC,GAAG,CAAC,iCAAiC,CAAC,CAAA;YAC9C,OAAM;QACR,CAAC;QAED,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE;YAC7B,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG,EAAE;gBACrB,IAAI,CAAC,SAAS,GAAG,KAAK,CAAA;gBACtB,OAAO,CAAC,GAAG,CAAC,sCAAsC,CAAC,CAAA;gBACnD,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAA;gBACpB,OAAO,EAAE,CAAA;YACX,CAAC,CAAC,CAAA;QACJ,CAAC,CAAC,CAAA;IACJ,CAAC;IAED,SAAS;QACP,OAAO;YACL,OAAO,EAAE,IAAI,CAAC,SAAS;YACvB,IAAI,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI;YACtB,IAAI,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI;YACtB,WAAW,EAAE,IAAI,CAAC,MAAM,CAAC,WAAW;YACpC,MAAM,EAAE,OAAO,CAAC,MAAM,EAAE;YACxB,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;SACpC,CAAA;IACH,CAAC;CACF;AAhXD,gDAgXC;AAED,kBAAe,kBAAkB,CAAA"}