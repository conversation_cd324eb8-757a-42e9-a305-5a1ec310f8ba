/**
 * AP3X Platform - Main Entry Point
 *
 * The most powerful agentic coding platform ever created.
 * Combines AIDER's proven code editing, AG3NT's multi-agent coordination,
 * and advanced context understanding into a unified platform.
 *
 * Features:
 * - Multi-agent code editing workflows
 * - Real-time collaboration
 * - Visual workflow designer
 * - Advanced context understanding
 * - Enterprise-grade features
 * - bolt.new-style user experience
 */
export interface AP3XPlatformConfig {
    port?: number;
    enableWebSockets?: boolean;
    enableRealTimeCollaboration?: boolean;
    enableVisualWorkflows?: boolean;
    enableEnterpriseFeatures?: boolean;
    corsOrigins?: string[];
}
/**
 * AP3X Platform - Main orchestrator
 */
export declare class AP3XPlatform {
    private config;
    private isRunning;
    constructor(config?: AP3XPlatformConfig);
    /**
     * Start the AP3X platform
     */
    start(): Promise<void>;
    /**
     * Stop the AP3X platform
     */
    stop(): Promise<void>;
    /**
     * Get platform status
     */
    getStatus(): any;
}
export { AP3XBridge, ap3xBridge } from './core/ap3x-bridge';
export { AP3XGateway, ap3xGateway } from './api/ap3x-gateway';
export declare const ap3xPlatform: AP3XPlatform;
export declare function startAP3X(config?: AP3XPlatformConfig): Promise<AP3XPlatform>;
export default AP3XPlatform;
//# sourceMappingURL=index.d.ts.map