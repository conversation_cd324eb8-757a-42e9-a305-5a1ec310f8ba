"use strict";
/**
 * AP3X Platform - Main Entry Point
 *
 * The most powerful agentic coding platform ever created.
 * Combines AIDER's proven code editing, AG3NT's multi-agent coordination,
 * and advanced context understanding into a unified platform.
 *
 * Features:
 * - Multi-agent code editing workflows
 * - Real-time collaboration
 * - Visual workflow designer
 * - Advanced context understanding
 * - Enterprise-grade features
 * - bolt.new-style user experience
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.ap3xPlatform = exports.ap3xGateway = exports.AP3XGateway = exports.ap3xBridge = exports.AP3XBridge = exports.AP3XPlatform = void 0;
exports.startAP3X = startAP3X;
const ap3x_bridge_1 = require("./core/ap3x-bridge");
const ap3x_gateway_1 = require("./api/ap3x-gateway");
/**
 * AP3X Platform - Main orchestrator
 */
class AP3XPlatform {
    constructor(config = {}) {
        this.isRunning = false;
        this.config = {
            port: 3000,
            enableWebSockets: true,
            enableRealTimeCollaboration: true,
            enableVisualWorkflows: true,
            enableEnterpriseFeatures: true,
            corsOrigins: ['http://localhost:3001', 'http://localhost:3000'],
            ...config
        };
    }
    /**
     * Start the AP3X platform
     */
    async start() {
        if (this.isRunning) {
            console.log('⚠️  AP3X Platform is already running');
            return;
        }
        console.log('🚀 Starting AP3X Platform...');
        console.log('='.repeat(80));
        console.log('🎯 The Most Powerful Agentic Coding Platform Ever Created');
        console.log('💼 Enterprise-Grade Multi-Agent Development Environment');
        console.log('🏆 Surpassing bolt.new, Cursor, and v0.dev Combined');
        console.log('='.repeat(80));
        try {
            // Start the API gateway (which initializes the bridge)
            await ap3x_gateway_1.ap3xGateway.start();
            this.isRunning = true;
            console.log('✅ AP3X Platform started successfully!');
            console.log('');
            console.log('🌐 Platform URLs:');
            console.log(`   API Gateway: http://localhost:${this.config.port}`);
            console.log(`   Health Check: http://localhost:${this.config.port}/api/health`);
            console.log(`   WebSockets: ${this.config.enableWebSockets ? 'enabled' : 'disabled'}`);
            console.log('');
            console.log('🚀 Ready to revolutionize coding with AI agents!');
        }
        catch (error) {
            console.error('❌ Failed to start AP3X Platform:', error);
            throw error;
        }
    }
    /**
     * Stop the AP3X platform
     */
    async stop() {
        if (!this.isRunning) {
            console.log('⚠️  AP3X Platform is not running');
            return;
        }
        console.log('🔄 Stopping AP3X Platform...');
        try {
            // Stop the API gateway
            await ap3x_gateway_1.ap3xGateway.stop();
            // Shutdown the bridge
            await ap3x_bridge_1.ap3xBridge.shutdown();
            this.isRunning = false;
            console.log('✅ AP3X Platform stopped successfully');
        }
        catch (error) {
            console.error('❌ Error stopping AP3X Platform:', error);
            throw error;
        }
    }
    /**
     * Get platform status
     */
    getStatus() {
        return {
            running: this.isRunning,
            config: this.config,
            uptime: process.uptime(),
            memory: process.memoryUsage(),
            version: '1.0.0'
        };
    }
}
exports.AP3XPlatform = AP3XPlatform;
// Export main classes and instances
var ap3x_bridge_2 = require("./core/ap3x-bridge");
Object.defineProperty(exports, "AP3XBridge", { enumerable: true, get: function () { return ap3x_bridge_2.AP3XBridge; } });
Object.defineProperty(exports, "ap3xBridge", { enumerable: true, get: function () { return ap3x_bridge_2.ap3xBridge; } });
var ap3x_gateway_2 = require("./api/ap3x-gateway");
Object.defineProperty(exports, "AP3XGateway", { enumerable: true, get: function () { return ap3x_gateway_2.AP3XGateway; } });
Object.defineProperty(exports, "ap3xGateway", { enumerable: true, get: function () { return ap3x_gateway_2.ap3xGateway; } });
// Create and export platform instance
exports.ap3xPlatform = new AP3XPlatform();
// Quick start function
async function startAP3X(config) {
    const platform = new AP3XPlatform(config);
    await platform.start();
    return platform;
}
// Graceful shutdown handling
process.on('SIGINT', async () => {
    console.log('\n🔄 Received SIGINT, shutting down gracefully...');
    try {
        await exports.ap3xPlatform.stop();
        process.exit(0);
    }
    catch (error) {
        console.error('Error during shutdown:', error);
        process.exit(1);
    }
});
process.on('SIGTERM', async () => {
    console.log('\n🔄 Received SIGTERM, shutting down gracefully...');
    try {
        await exports.ap3xPlatform.stop();
        process.exit(0);
    }
    catch (error) {
        console.error('Error during shutdown:', error);
        process.exit(1);
    }
});
// Export default
exports.default = AP3XPlatform;
// If this file is run directly, start the platform
if (require.main === module) {
    startAP3X().catch((error) => {
        console.error('Failed to start AP3X Platform:', error);
        process.exit(1);
    });
}
//# sourceMappingURL=index.js.map