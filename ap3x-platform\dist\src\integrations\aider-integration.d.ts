/**
 * AP3X Platform - AIDER Integration
 *
 * Integrates AIDER's proven code editing capabilities with the AP3X platform.
 * Provides a bridge between AIDER's Python-based coder and our TypeScript platform.
 *
 * Features:
 * - AIDER process management
 * - Code editing request handling
 * - Git integration
 * - File watching and synchronization
 * - Error handling and recovery
 */
import { EventEmitter } from 'events';
import { FileChange } from '../core/ap3x-bridge';
export interface AiderConfig {
    pythonPath: string;
    aiderPath: string;
    modelSettings: {
        model: string;
        apiKey?: string;
        baseUrl?: string;
    };
    gitIntegration: boolean;
    autoCommit: boolean;
    workingDirectory: string;
}
export interface AiderRequest {
    prompt: string;
    files: string[];
    readOnlyFiles?: string[];
    model?: string;
    editFormat?: string;
    dryRun?: boolean;
    autoCommit?: boolean;
}
export interface AiderResponse {
    success: boolean;
    changes: FileChange[];
    output: string[];
    errors: string[];
    commitHash?: string;
    executionTime: number;
}
/**
 * AIDER Integration Manager
 */
export declare class AiderIntegration extends EventEmitter {
    private config;
    private processes;
    private isInitialized;
    constructor(config: AiderConfig);
    /**
     * Initialize AIDER integration
     */
    initialize(): Promise<void>;
    /**
     * Execute code editing with AIDER
     */
    executeCodeEdit(sessionId: string, request: AiderRequest): Promise<AiderResponse>;
    /**
     * Verify AIDER installation
     */
    private verifyAiderInstallation;
    /**
     * Setup working directory
     */
    private setupWorkingDirectory;
    /**
     * Initialize git repository
     */
    private initializeGitRepo;
    /**
     * Test AIDER functionality
     */
    private testAiderFunctionality;
    /**
     * Build AIDER command
     */
    private buildAiderCommand;
    /**
     * Run AIDER process
     */
    private runAiderProcess;
    /**
     * Parse AIDER output
     */
    private parseAiderOutput;
    /**
     * Parse file changes from AIDER output
     */
    private parseFileChanges;
    /**
     * Extract commit hash from output
     */
    private extractCommitHash;
    /**
     * Kill AIDER process for session
     */
    killProcess(sessionId: string): Promise<void>;
    /**
     * Get active AIDER processes
     */
    getActiveProcesses(): string[];
    /**
     * Shutdown AIDER integration
     */
    shutdown(): Promise<void>;
}
export declare const aiderIntegration: AiderIntegration;
export default AiderIntegration;
//# sourceMappingURL=aider-integration.d.ts.map