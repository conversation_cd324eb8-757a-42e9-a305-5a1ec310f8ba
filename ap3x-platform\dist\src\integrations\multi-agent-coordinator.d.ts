/**
 * AP3X Platform - Multi-Agent Code Editing Coordinator
 *
 * Coordinates between AIDER's code editing and AG3NT's multi-agent system
 * to enable sophisticated collaborative coding workflows.
 *
 * Features:
 * - Agent task delegation
 * - Workflow orchestration
 * - Conflict resolution
 * - Progress tracking
 * - Quality assurance
 */
import { EventEmitter } from 'events';
import { AG3NTFramework } from '../../../ag3nt-framework-standalone/src/ag3nt-framework';
import { AiderIntegration } from './aider-integration';
import { FileChange } from '../core/ap3x-bridge';
export interface MultiAgentRequest {
    sessionId: string;
    prompt: string;
    files: string[];
    workflow: WorkflowType;
    agents: AgentConfig[];
    options: MultiAgentOptions;
}
export interface AgentConfig {
    type: string;
    role: 'planner' | 'coder' | 'reviewer' | 'tester';
    priority: number;
    capabilities: string[];
    constraints?: any;
}
export interface MultiAgentOptions {
    useConsensus: boolean;
    requireReview: boolean;
    autoTest: boolean;
    maxIterations: number;
    timeoutMs: number;
    conflictResolution: 'priority' | 'consensus' | 'human';
}
export type WorkflowType = 'sequential' | 'parallel' | 'hierarchical' | 'consensus' | 'review-based';
export interface MultiAgentResponse {
    success: boolean;
    workflow: WorkflowExecution;
    finalChanges: FileChange[];
    agentResults: AgentResult[];
    conflicts: Conflict[];
    executionTime: number;
    qualityScore: number;
}
export interface WorkflowExecution {
    id: string;
    type: WorkflowType;
    status: 'running' | 'completed' | 'failed' | 'cancelled';
    steps: WorkflowStep[];
    currentStep: number;
    startTime: Date;
    endTime?: Date;
}
export interface WorkflowStep {
    id: string;
    agentId: string;
    agentType: string;
    task: string;
    status: 'pending' | 'running' | 'completed' | 'failed';
    input: any;
    output?: any;
    startTime?: Date;
    endTime?: Date;
    dependencies: string[];
}
export interface AgentResult {
    agentId: string;
    agentType: string;
    success: boolean;
    changes: FileChange[];
    output: any;
    metrics: {
        executionTime: number;
        linesChanged: number;
        filesModified: number;
        qualityScore: number;
    };
    feedback?: string;
}
export interface Conflict {
    id: string;
    type: 'file_conflict' | 'logic_conflict' | 'style_conflict';
    description: string;
    affectedFiles: string[];
    agents: string[];
    resolution?: ConflictResolution;
}
export interface ConflictResolution {
    strategy: 'merge' | 'priority' | 'manual' | 'consensus';
    chosenAgent?: string;
    mergedResult?: any;
    humanInput?: string;
}
/**
 * Multi-Agent Code Editing Coordinator
 */
export declare class MultiAgentCoordinator extends EventEmitter {
    private ag3ntFramework;
    private aiderIntegration;
    private activeWorkflows;
    private isInitialized;
    constructor(ag3ntFramework: AG3NTFramework, aiderIntegration: AiderIntegration);
    /**
     * Initialize the coordinator
     */
    initialize(): Promise<void>;
    /**
     * Execute multi-agent code editing
     */
    executeMultiAgentEditing(request: MultiAgentRequest): Promise<MultiAgentResponse>;
    /**
     * Create workflow execution
     */
    private createWorkflowExecution;
    /**
     * Create workflow steps based on agents and workflow type
     */
    private createWorkflowSteps;
    /**
     * Create agent-specific task
     */
    private createAgentTask;
    /**
     * Execute workflow
     */
    private executeWorkflow;
    /**
     * Execute sequential workflow
     */
    private executeSequentialWorkflow;
    /**
     * Execute parallel workflow
     */
    private executeParallelWorkflow;
    /**
     * Execute hierarchical workflow
     */
    private executeHierarchicalWorkflow;
    /**
     * Execute individual agent step
     */
    private executeAgentStep;
    /**
     * Parse AG3NT changes
     */
    private parseAG3NTChanges;
    /**
     * Apply changes to file list
     */
    private applyChangesToFileList;
    /**
     * Detect conflicts between agent results
     */
    private detectConflicts;
    /**
     * Merge parallel changes
     */
    private mergeParallelChanges;
    /**
     * Resolve conflicts
     */
    private resolveConflicts;
    /**
     * Calculate quality score
     */
    private calculateQualityScore;
    /**
     * Calculate step quality score
     */
    private calculateStepQualityScore;
    /**
     * Count lines changed
     */
    private countLinesChanged;
    /**
     * Setup event listeners
     */
    private setupEventListeners;
    /**
     * Get active workflows
     */
    getActiveWorkflows(): WorkflowExecution[];
    /**
     * Cancel workflow
     */
    cancelWorkflow(workflowId: string): Promise<void>;
    /**
     * Shutdown coordinator
     */
    shutdown(): Promise<void>;
}
export default MultiAgentCoordinator;
//# sourceMappingURL=multi-agent-coordinator.d.ts.map