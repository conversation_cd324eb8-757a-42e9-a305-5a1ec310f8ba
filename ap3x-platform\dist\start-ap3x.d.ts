#!/usr/bin/env ts-node
/**
 * AP3X Platform - Startup Script
 *
 * The ultimate startup script for the most powerful agentic coding platform ever created.
 * This script initializes and starts the complete AP3X ecosystem.
 *
 * Usage:
 *   npm run start
 *   npm run dev
 *   ts-node start-ap3x.ts
 *
 * Environment Variables:
 *   PORT - Server port (default: 3000)
 *   HOST - Server host (default: localhost)
 *   NODE_ENV - Environment (development/staging/production)
 *   OPENAI_API_KEY - OpenAI API key for AIDER
 *   PYTHON_PATH - Path to Python executable
 *   NEO4J_URL - Neo4j database URL
 *   NEO4J_USER - Neo4j username
 *   NEO4J_PASSWORD - Neo4j password
 *   JWT_SECRET - JWT secret for authentication
 */
/**
 * Main startup function
 */
declare function main(): Promise<void>;
export { main as startAP3X };
//# sourceMappingURL=start-ap3x.d.ts.map