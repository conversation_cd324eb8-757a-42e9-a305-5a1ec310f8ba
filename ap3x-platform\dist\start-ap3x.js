#!/usr/bin/env ts-node
"use strict";
/**
 * AP3X Platform - Startup Script
 *
 * The ultimate startup script for the most powerful agentic coding platform ever created.
 * This script initializes and starts the complete AP3X ecosystem.
 *
 * Usage:
 *   npm run start
 *   npm run dev
 *   ts-node start-ap3x.ts
 *
 * Environment Variables:
 *   PORT - Server port (default: 3000)
 *   HOST - Server host (default: localhost)
 *   NODE_ENV - Environment (development/staging/production)
 *   OPENAI_API_KEY - OpenAI API key for AIDER
 *   PYTHON_PATH - Path to Python executable
 *   NEO4J_URL - Neo4j database URL
 *   NEO4J_USER - Neo4j username
 *   NEO4J_PASSWORD - Neo4j password
 *   JWT_SECRET - JWT secret for authentication
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.startAP3X = main;
const ap3x_platform_1 = require("./src/core/ap3x-platform");
const dotenv_1 = require("dotenv");
const fs_1 = __importDefault(require("fs"));
// Load environment variables
(0, dotenv_1.config)();
/**
 * Display startup banner
 */
function displayBanner() {
    const banner = `
╔══════════════════════════════════════════════════════════════════════════════╗
║                                                                              ║
║     █████╗ ██████╗ ██████╗ ██╗  ██╗    ██████╗ ██╗      █████╗ ████████╗    ║
║    ██╔══██╗██╔══██╗╚════██╗╚██╗██╔╝    ██╔══██╗██║     ██╔══██╗╚══██╔══╝    ║
║    ███████║██████╔╝ █████╔╝ ╚███╔╝     ██████╔╝██║     ███████║   ██║       ║
║    ██╔══██║██╔═══╝  ╚═══██╗ ██╔██╗     ██╔═══╝ ██║     ██╔══██║   ██║       ║
║    ██║  ██║██║     ██████╔╝██╔╝ ██╗    ██║     ███████╗██║  ██║   ██║       ║
║    ╚═╝  ╚═╝╚═╝     ╚═════╝ ╚═╝  ╚═╝    ╚═╝     ╚══════╝╚═╝  ╚═╝   ╚═╝       ║
║                                                                              ║
║                THE MOST POWERFUL AGENTIC CODING PLATFORM                    ║
║                           EVER CREATED                                      ║
║                                                                              ║
║  🚀 AIDER's Proven Code Editing    🤖 AG3NT's Multi-Agent Coordination     ║
║  🧠 Neo4j Context Understanding    🔄 Real-time Collaboration               ║
║  🎨 Visual Workflow Designer       👥 Enterprise User Management           ║
║  ⚡ WebSocket Real-time Updates    🔒 Enterprise Security                   ║
║                                                                              ║
║              FRANKENSTEIN'S MONSTER OF CODING PLATFORMS                     ║
║                        🔥 ALIVE AND READY! 🔥                              ║
║                                                                              ║
╚══════════════════════════════════════════════════════════════════════════════╝
`;
    console.log(banner);
    console.log('🎯 Initializing the revolution in software development...');
    console.log('');
}
/**
 * Validate environment and dependencies
 */
async function validateEnvironment() {
    console.log('🔍 Validating environment and dependencies...');
    const checks = [
        {
            name: 'Node.js Version',
            check: () => {
                const version = process.version;
                const major = parseInt(version.slice(1).split('.')[0]);
                return major >= 18;
            },
            error: 'Node.js 18+ is required'
        },
        {
            name: 'OpenAI API Key',
            check: () => !!process.env.OPENAI_API_KEY,
            error: 'OPENAI_API_KEY environment variable is required'
        },
        {
            name: 'Python Installation',
            check: async () => {
                try {
                    const { spawn } = await Promise.resolve().then(() => __importStar(require('child_process')));
                    return new Promise((resolve) => {
                        const python = spawn(process.env.PYTHON_PATH || 'python', ['--version']);
                        python.on('close', (code) => resolve(code === 0));
                        python.on('error', () => resolve(false));
                    });
                }
                catch {
                    return false;
                }
            },
            error: 'Python is required for AIDER integration'
        },
        {
            name: 'Workspace Directory',
            check: () => {
                const workspaceDir = process.env.AIDER_WORKING_DIR || './workspace';
                try {
                    if (!fs_1.default.existsSync(workspaceDir)) {
                        fs_1.default.mkdirSync(workspaceDir, { recursive: true });
                    }
                    return true;
                }
                catch {
                    return false;
                }
            },
            error: 'Cannot create workspace directory'
        }
    ];
    for (const check of checks) {
        try {
            const result = await check.check();
            if (result) {
                console.log(`   ✅ ${check.name}`);
            }
            else {
                console.error(`   ❌ ${check.name}: ${check.error}`);
                process.exit(1);
            }
        }
        catch (error) {
            console.error(`   ❌ ${check.name}: ${check.error}`);
            process.exit(1);
        }
    }
    console.log('✅ Environment validation complete');
    console.log('');
}
/**
 * Create platform configuration
 */
function createConfiguration() {
    const config = {
        ...ap3x_platform_1.defaultConfig,
        // Override with environment variables
        port: parseInt(process.env.PORT || '3000'),
        host: process.env.HOST || 'localhost',
        environment: process.env.NODE_ENV || 'development',
        aider: {
            ...ap3x_platform_1.defaultConfig.aider,
            pythonPath: process.env.PYTHON_PATH || 'python',
            modelSettings: {
                model: process.env.AIDER_MODEL || 'gpt-4',
                apiKey: process.env.OPENAI_API_KEY,
            },
            workingDirectory: process.env.AIDER_WORKING_DIR || './workspace',
        },
        contextEngine: {
            ...ap3x_platform_1.defaultConfig.contextEngine,
            apiUrl: process.env.CONTEXT_ENGINE_URL || 'http://localhost:3001',
            neo4jUrl: process.env.NEO4J_URL || 'bolt://localhost:7687',
            neo4jUser: process.env.NEO4J_USER || 'neo4j',
            neo4jPassword: process.env.NEO4J_PASSWORD || 'password',
        },
        enterprise: {
            ...ap3x_platform_1.defaultConfig.enterprise,
            jwtSecret: process.env.JWT_SECRET || 'ap3x-platform-secret-key-change-in-production',
        },
        security: {
            ...ap3x_platform_1.defaultConfig.security,
            corsOrigins: process.env.CORS_ORIGINS?.split(',') || ['http://localhost:3001', 'http://localhost:3000'],
        },
    };
    return config;
}
/**
 * Setup graceful shutdown
 */
function setupGracefulShutdown(platform) {
    const shutdown = async (signal) => {
        console.log(`\n🔄 Received ${signal}, shutting down gracefully...`);
        try {
            await platform.stop();
            console.log('✅ AP3X Platform shutdown complete');
            process.exit(0);
        }
        catch (error) {
            console.error('❌ Error during shutdown:', error);
            process.exit(1);
        }
    };
    process.on('SIGINT', () => shutdown('SIGINT'));
    process.on('SIGTERM', () => shutdown('SIGTERM'));
    process.on('SIGUSR2', () => shutdown('SIGUSR2')); // nodemon restart
    // Handle uncaught exceptions
    process.on('uncaughtException', (error) => {
        console.error('💥 Uncaught Exception:', error);
        shutdown('uncaughtException');
    });
    process.on('unhandledRejection', (reason, promise) => {
        console.error('💥 Unhandled Rejection at:', promise, 'reason:', reason);
        shutdown('unhandledRejection');
    });
}
/**
 * Display startup information
 */
function displayStartupInfo(config) {
    console.log('📋 PLATFORM CONFIGURATION:');
    console.log(`   🌐 Environment: ${config.environment}`);
    console.log(`   🚪 Port: ${config.port}`);
    console.log(`   🏠 Host: ${config.host}`);
    console.log(`   🤖 AIDER Model: ${config.aider.modelSettings.model}`);
    console.log(`   🐍 Python: ${config.aider.pythonPath}`);
    console.log(`   📁 Workspace: ${config.aider.workingDirectory}`);
    console.log(`   🧠 Context Engine: ${config.contextEngine.apiUrl}`);
    console.log(`   🔄 Real-time: ${config.features.enableRealTimeCollaboration ? 'ENABLED' : 'DISABLED'}`);
    console.log(`   👥 User Management: ${config.enterprise.enableUserManagement ? 'ENABLED' : 'DISABLED'}`);
    console.log(`   🎨 Visual Workflows: ${config.features.enableVisualWorkflows ? 'ENABLED' : 'DISABLED'}`);
    console.log('');
}
/**
 * Main startup function
 */
async function main() {
    try {
        // Display banner
        displayBanner();
        // Validate environment
        await validateEnvironment();
        // Create configuration
        const config = createConfiguration();
        displayStartupInfo(config);
        // Create and start platform
        console.log('🚀 Creating AP3X Platform instance...');
        const platform = new ap3x_platform_1.AP3XPlatform(config);
        // Setup graceful shutdown
        setupGracefulShutdown(platform);
        // Setup event listeners
        platform.on('platform_started', () => {
            console.log('');
            console.log('🎉 SUCCESS! AP3X Platform is now running!');
            console.log('');
            console.log('🌟 QUICK START GUIDE:');
            console.log(`   1. Open your browser: http://${config.host}:${config.port}`);
            console.log(`   2. Check health: http://${config.host}:${config.port}/api/health`);
            console.log(`   3. Create your first project and start coding with AI agents!`);
            console.log('');
            console.log('📚 DOCUMENTATION:');
            console.log('   • API Reference: /api/docs');
            console.log('   • User Guide: /docs');
            console.log('   • GitHub: https://github.com/ap3x/ap3x-platform');
            console.log('');
            console.log('💬 SUPPORT:');
            console.log('   • Discord: https://discord.gg/ap3x');
            console.log('   • Email: <EMAIL>');
            console.log('');
            console.log('🔥 THE FUTURE OF CODING IS HERE! 🔥');
        });
        platform.on('platform_error', (error) => {
            console.error('💥 Platform Error:', error);
        });
        platform.on('component_ready', ({ component }) => {
            console.log(`   ✅ ${component} ready`);
        });
        // Start the platform
        await platform.start();
    }
    catch (error) {
        console.error('💥 Failed to start AP3X Platform:', error);
        process.exit(1);
    }
}
// Run the main function
if (require.main === module) {
    main().catch((error) => {
        console.error('💥 Startup error:', error);
        process.exit(1);
    });
}
//# sourceMappingURL=start-ap3x.js.map