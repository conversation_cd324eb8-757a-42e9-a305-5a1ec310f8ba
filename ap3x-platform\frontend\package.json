{"name": "ap3x-frontend", "version": "1.0.0", "description": "AP3X Platform - Modern React Frontend with bolt.new-style interface", "private": true, "scripts": {"dev": "next dev -p 3001", "build": "next build", "start": "next start -p 3001", "lint": "next lint", "type-check": "tsc --noEmit", "test": "jest", "test:watch": "jest --watch", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build"}, "dependencies": {"next": "^14.0.4", "react": "^18.2.0", "react-dom": "^18.2.0", "@types/node": "^20.10.0", "@types/react": "^18.2.42", "@types/react-dom": "^18.2.17", "typescript": "^5.3.2", "tailwindcss": "^3.3.6", "autoprefixer": "^10.4.16", "postcss": "^8.4.32", "@tailwindcss/forms": "^0.5.7", "@tailwindcss/typography": "^0.5.10", "@headlessui/react": "^1.7.17", "@heroicons/react": "^2.0.18", "framer-motion": "^10.16.16", "socket.io-client": "^4.7.4", "axios": "^1.6.2", "react-query": "^3.39.3", "zustand": "^4.4.7", "react-hook-form": "^7.48.2", "@hookform/resolvers": "^3.3.2", "zod": "^3.22.4", "react-hot-toast": "^2.4.1", "lucide-react": "^0.294.0", "monaco-editor": "^0.45.0", "@monaco-editor/react": "^4.6.0", "reactflow": "^11.10.1", "react-resizable-panels": "^0.0.55", "date-fns": "^2.30.0", "clsx": "^2.0.0", "class-variance-authority": "^0.7.0"}, "devDependencies": {"eslint": "^8.54.0", "eslint-config-next": "^14.0.4", "@typescript-eslint/eslint-plugin": "^6.12.0", "@typescript-eslint/parser": "^6.12.0", "prettier": "^3.1.0", "prettier-plugin-tailwindcss": "^0.5.7", "@types/jest": "^29.5.8", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "@testing-library/react": "^13.4.0", "@testing-library/jest-dom": "^6.1.5", "@storybook/addon-essentials": "^7.6.3", "@storybook/addon-interactions": "^7.6.3", "@storybook/addon-links": "^7.6.3", "@storybook/blocks": "^7.6.3", "@storybook/nextjs": "^7.6.3", "@storybook/react": "^7.6.3", "@storybook/testing-library": "^0.2.2", "storybook": "^7.6.3"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}}