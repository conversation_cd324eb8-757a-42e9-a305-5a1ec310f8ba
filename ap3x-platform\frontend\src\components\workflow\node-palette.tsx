'use client'

import { useState } from 'react'
import { motion } from 'framer-motion'
import {
  CpuChipIcon,
  BoltIcon,
  CodeBracketIcon,
  CheckCircleIcon,
  XCircleIcon,
  ClockIcon,
  DocumentTextIcon,
  BeakerIcon,
  EyeIcon,
  CogIcon,
  PlayIcon,
  PauseIcon,
} from '@heroicons/react/24/outline'
import { Card } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'

interface NodeTemplate {
  id: string
  type: string
  label: string
  description: string
  icon: React.ComponentType<{ className?: string }>
  category: 'agents' | 'triggers' | 'actions' | 'conditions'
  color: string
  defaultConfig: any
}

const nodeTemplates: NodeTemplate[] = [
  // Agent Nodes
  {
    id: 'planning-agent',
    type: 'agent',
    label: 'Planning Agent',
    description: 'Analyzes requirements and creates implementation plans',
    icon: CpuChipIcon,
    category: 'agents',
    color: 'primary',
    defaultConfig: {
      agentType: 'planning',
      model: 'gpt-4',
      temperature: 0.7,
      capabilities: ['planning', 'analysis', 'architecture'],
    },
  },
  {
    id: 'frontend-coder',
    type: 'agent',
    label: 'Frontend Coder',
    description: 'Develops React/Vue/Angular components and UI',
    icon: CodeBracketIcon,
    category: 'agents',
    color: 'accent',
    defaultConfig: {
      agentType: 'frontend-coder',
      model: 'gpt-4',
      temperature: 0.3,
      capabilities: ['react', 'typescript', 'css', 'ui-design'],
    },
  },
  {
    id: 'backend-coder',
    type: 'agent',
    label: 'Backend Coder',
    description: 'Builds APIs, databases, and server-side logic',
    icon: CogIcon,
    category: 'agents',
    color: 'success',
    defaultConfig: {
      agentType: 'backend-coder',
      model: 'gpt-4',
      temperature: 0.3,
      capabilities: ['nodejs', 'python', 'databases', 'apis'],
    },
  },
  {
    id: 'tester-agent',
    type: 'agent',
    label: 'Tester Agent',
    description: 'Creates and runs automated tests',
    icon: BeakerIcon,
    category: 'agents',
    color: 'warning',
    defaultConfig: {
      agentType: 'tester',
      model: 'gpt-4',
      temperature: 0.2,
      capabilities: ['unit-testing', 'integration-testing', 'e2e-testing'],
    },
  },
  {
    id: 'reviewer-agent',
    type: 'agent',
    label: 'Reviewer Agent',
    description: 'Reviews code quality, security, and best practices',
    icon: EyeIcon,
    category: 'agents',
    color: 'secondary',
    defaultConfig: {
      agentType: 'reviewer',
      model: 'gpt-4',
      temperature: 0.1,
      capabilities: ['code-review', 'security', 'best-practices'],
    },
  },

  // Trigger Nodes
  {
    id: 'manual-trigger',
    type: 'trigger',
    label: 'Manual Trigger',
    description: 'Start workflow manually',
    icon: PlayIcon,
    category: 'triggers',
    color: 'primary',
    defaultConfig: {
      triggerType: 'manual',
    },
  },
  {
    id: 'webhook-trigger',
    type: 'trigger',
    label: 'Webhook Trigger',
    description: 'Start workflow from HTTP webhook',
    icon: BoltIcon,
    category: 'triggers',
    color: 'accent',
    defaultConfig: {
      triggerType: 'webhook',
      method: 'POST',
      authentication: 'none',
    },
  },
  {
    id: 'schedule-trigger',
    type: 'trigger',
    label: 'Schedule Trigger',
    description: 'Start workflow on schedule',
    icon: ClockIcon,
    category: 'triggers',
    color: 'warning',
    defaultConfig: {
      triggerType: 'schedule',
      cron: '0 9 * * 1-5', // 9 AM weekdays
    },
  },

  // Action Nodes
  {
    id: 'file-action',
    type: 'action',
    label: 'File Action',
    description: 'Read, write, or modify files',
    icon: DocumentTextIcon,
    category: 'actions',
    color: 'success',
    defaultConfig: {
      actionType: 'file',
      operation: 'read',
    },
  },
  {
    id: 'notification-action',
    type: 'action',
    label: 'Notification',
    description: 'Send notifications via email, Slack, etc.',
    icon: BoltIcon,
    category: 'actions',
    color: 'primary',
    defaultConfig: {
      actionType: 'notification',
      channel: 'email',
    },
  },

  // Condition Nodes
  {
    id: 'approval-condition',
    type: 'condition',
    label: 'Approval Required',
    description: 'Wait for human approval before continuing',
    icon: CheckCircleIcon,
    category: 'conditions',
    color: 'warning',
    defaultConfig: {
      conditionType: 'approval',
      approvers: [],
      timeout: 3600000, // 1 hour
    },
  },
  {
    id: 'quality-gate',
    type: 'condition',
    label: 'Quality Gate',
    description: 'Check if quality metrics meet requirements',
    icon: CheckCircleIcon,
    category: 'conditions',
    color: 'success',
    defaultConfig: {
      conditionType: 'quality',
      minScore: 0.8,
      metrics: ['test-coverage', 'code-quality'],
    },
  },
  {
    id: 'error-handler',
    type: 'condition',
    label: 'Error Handler',
    description: 'Handle errors and exceptions',
    icon: XCircleIcon,
    category: 'conditions',
    color: 'error',
    defaultConfig: {
      conditionType: 'error',
      retryCount: 3,
      retryDelay: 5000,
    },
  },
]

const categories = [
  { id: 'agents', label: 'AI Agents', icon: CpuChipIcon },
  { id: 'triggers', label: 'Triggers', icon: PlayIcon },
  { id: 'actions', label: 'Actions', icon: BoltIcon },
  { id: 'conditions', label: 'Conditions', icon: CheckCircleIcon },
]

export function NodePalette() {
  const [selectedCategory, setSelectedCategory] = useState<string>('agents')
  const [searchQuery, setSearchQuery] = useState('')

  const filteredNodes = nodeTemplates.filter(node => {
    const matchesCategory = node.category === selectedCategory
    const matchesSearch = searchQuery === '' || 
      node.label.toLowerCase().includes(searchQuery.toLowerCase()) ||
      node.description.toLowerCase().includes(searchQuery.toLowerCase())
    
    return matchesCategory && matchesSearch
  })

  const onDragStart = (event: React.DragEvent, nodeType: string) => {
    event.dataTransfer.setData('application/reactflow', nodeType)
    event.dataTransfer.effectAllowed = 'move'
  }

  return (
    <div className="h-full flex flex-col">
      {/* Header */}
      <div className="p-4 border-b border-secondary-700">
        <h3 className="text-lg font-semibold text-white mb-3">Node Palette</h3>
        
        {/* Search */}
        <Input
          placeholder="Search nodes..."
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          className="input mb-3"
        />

        {/* Categories */}
        <div className="space-y-1">
          {categories.map((category) => {
            const Icon = category.icon
            return (
              <button
                key={category.id}
                onClick={() => setSelectedCategory(category.id)}
                className={`w-full flex items-center space-x-2 px-3 py-2 rounded-lg text-sm transition-colors ${
                  selectedCategory === category.id
                    ? 'bg-primary-600/20 text-primary-300 border border-primary-500/30'
                    : 'text-secondary-400 hover:bg-secondary-800 hover:text-white'
                }`}
              >
                <Icon className="w-4 h-4" />
                <span>{category.label}</span>
              </button>
            )
          })}
        </div>
      </div>

      {/* Node List */}
      <div className="flex-1 overflow-y-auto p-4 space-y-3">
        {filteredNodes.map((node) => {
          const Icon = node.icon
          return (
            <motion.div
              key={node.id}
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
            >
              <Card
                className="card-hover cursor-grab active:cursor-grabbing p-3"
                draggable
                onDragStart={(e) => onDragStart(e, node.type)}
              >
                <div className="flex items-start space-x-3">
                  <div className={`p-2 rounded-lg bg-${node.color}-600/20 flex-shrink-0`}>
                    <Icon className={`w-4 h-4 text-${node.color}-400`} />
                  </div>
                  
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center justify-between mb-1">
                      <h4 className="font-medium text-white text-sm truncate">
                        {node.label}
                      </h4>
                      <Badge className={`badge-${node.color} text-xs ml-2`}>
                        {node.category}
                      </Badge>
                    </div>
                    
                    <p className="text-xs text-secondary-400 leading-relaxed">
                      {node.description}
                    </p>

                    {/* Capabilities */}
                    {node.defaultConfig.capabilities && (
                      <div className="mt-2 flex flex-wrap gap-1">
                        {node.defaultConfig.capabilities.slice(0, 2).map((capability: string, index: number) => (
                          <Badge key={index} className="badge-secondary text-xs px-1 py-0">
                            {capability}
                          </Badge>
                        ))}
                        {node.defaultConfig.capabilities.length > 2 && (
                          <Badge className="badge-secondary text-xs px-1 py-0">
                            +{node.defaultConfig.capabilities.length - 2}
                          </Badge>
                        )}
                      </div>
                    )}
                  </div>
                </div>
              </Card>
            </motion.div>
          )
        })}

        {filteredNodes.length === 0 && (
          <div className="text-center py-8">
            <p className="text-secondary-400 text-sm">
              No nodes found matching "{searchQuery}"
            </p>
          </div>
        )}
      </div>

      {/* Instructions */}
      <div className="p-4 border-t border-secondary-700">
        <p className="text-xs text-secondary-500 leading-relaxed">
          Drag and drop nodes onto the canvas to build your workflow. 
          Connect nodes by dragging from output handles to input handles.
        </p>
      </div>
    </div>
  )
}
