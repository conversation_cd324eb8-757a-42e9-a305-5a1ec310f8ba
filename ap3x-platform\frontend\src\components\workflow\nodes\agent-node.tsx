'use client'

import { memo, useState } from 'react'
import { <PERSON><PERSON>, Position, NodeProps } from 'react-flow-renderer'
import { motion } from 'framer-motion'
import {
  CpuChipIcon,
  CogIcon,
  PlayIcon,
  CheckCircleIcon,
  ExclamationCircleIcon,
  ClockIcon,
} from '@heroicons/react/24/outline'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Card } from '@/components/ui/card'

export interface AgentNodeData {
  label: string
  agentType: string
  status: 'idle' | 'running' | 'completed' | 'error' | 'waiting'
  progress?: number
  config: {
    model?: string
    temperature?: number
    maxTokens?: number
    systemPrompt?: string
    capabilities?: string[]
  }
  metrics?: {
    executionTime?: number
    tokensUsed?: number
    cost?: number
  }
}

const statusColors = {
  idle: 'secondary',
  running: 'warning',
  completed: 'success',
  error: 'error',
  waiting: 'primary',
} as const

const statusIcons = {
  idle: CpuChipIcon,
  running: PlayIcon,
  completed: CheckCircleIcon,
  error: ExclamationCircleIcon,
  waiting: ClockIcon,
}

export const AgentNode = memo(({ data, selected }: NodeProps<AgentNodeData>) => {
  const [showConfig, setShowConfig] = useState(false)
  const StatusIcon = statusIcons[data.status]

  return (
    <>
      <motion.div
        initial={{ scale: 0.8, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        whileHover={{ scale: 1.02 }}
        className={`workflow-node ${selected ? 'workflow-node-selected' : ''} min-w-[200px]`}
      >
        {/* Input Handle */}
        <Handle
          type="target"
          position={Position.Top}
          className="w-3 h-3 bg-primary-500 border-2 border-white"
        />

        {/* Node Header */}
        <div className="flex items-center justify-between mb-3">
          <div className="flex items-center space-x-2">
            <div className={`p-2 rounded-lg bg-${statusColors[data.status]}-600/20`}>
              <StatusIcon className={`w-4 h-4 text-${statusColors[data.status]}-400`} />
            </div>
            <div>
              <h4 className="font-medium text-white text-sm">{data.label}</h4>
              <p className="text-xs text-secondary-400">{data.agentType}</p>
            </div>
          </div>
          
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setShowConfig(!showConfig)}
            className="p-1 h-auto"
          >
            <CogIcon className="w-4 h-4" />
          </Button>
        </div>

        {/* Status Badge */}
        <div className="flex items-center justify-between mb-3">
          <Badge className={`badge-${statusColors[data.status]} text-xs`}>
            {data.status.charAt(0).toUpperCase() + data.status.slice(1)}
          </Badge>
          
          {data.progress !== undefined && data.status === 'running' && (
            <div className="flex items-center space-x-2">
              <div className="w-16 h-1 bg-secondary-700 rounded-full overflow-hidden">
                <motion.div
                  className="h-full bg-warning-500"
                  initial={{ width: 0 }}
                  animate={{ width: `${data.progress}%` }}
                  transition={{ duration: 0.3 }}
                />
              </div>
              <span className="text-xs text-secondary-400">{Math.round(data.progress)}%</span>
            </div>
          )}
        </div>

        {/* Capabilities */}
        {data.config.capabilities && data.config.capabilities.length > 0 && (
          <div className="mb-3">
            <p className="text-xs text-secondary-400 mb-1">Capabilities:</p>
            <div className="flex flex-wrap gap-1">
              {data.config.capabilities.slice(0, 3).map((capability, index) => (
                <Badge key={index} className="badge-secondary text-xs px-1 py-0">
                  {capability}
                </Badge>
              ))}
              {data.config.capabilities.length > 3 && (
                <Badge className="badge-secondary text-xs px-1 py-0">
                  +{data.config.capabilities.length - 3}
                </Badge>
              )}
            </div>
          </div>
        )}

        {/* Metrics */}
        {data.metrics && (
          <div className="text-xs text-secondary-400 space-y-1">
            {data.metrics.executionTime && (
              <div className="flex justify-between">
                <span>Execution:</span>
                <span>{data.metrics.executionTime}ms</span>
              </div>
            )}
            {data.metrics.tokensUsed && (
              <div className="flex justify-between">
                <span>Tokens:</span>
                <span>{data.metrics.tokensUsed.toLocaleString()}</span>
              </div>
            )}
            {data.metrics.cost && (
              <div className="flex justify-between">
                <span>Cost:</span>
                <span>${data.metrics.cost.toFixed(4)}</span>
              </div>
            )}
          </div>
        )}

        {/* Configuration Panel */}
        {showConfig && (
          <motion.div
            initial={{ height: 0, opacity: 0 }}
            animate={{ height: 'auto', opacity: 1 }}
            exit={{ height: 0, opacity: 0 }}
            className="mt-3 pt-3 border-t border-secondary-600"
          >
            <div className="space-y-2 text-xs">
              {data.config.model && (
                <div className="flex justify-between">
                  <span className="text-secondary-400">Model:</span>
                  <span className="text-white">{data.config.model}</span>
                </div>
              )}
              {data.config.temperature !== undefined && (
                <div className="flex justify-between">
                  <span className="text-secondary-400">Temperature:</span>
                  <span className="text-white">{data.config.temperature}</span>
                </div>
              )}
              {data.config.maxTokens && (
                <div className="flex justify-between">
                  <span className="text-secondary-400">Max Tokens:</span>
                  <span className="text-white">{data.config.maxTokens}</span>
                </div>
              )}
              {data.config.systemPrompt && (
                <div>
                  <span className="text-secondary-400">System Prompt:</span>
                  <p className="text-white mt-1 text-xs bg-secondary-800 p-2 rounded">
                    {data.config.systemPrompt.length > 100
                      ? `${data.config.systemPrompt.substring(0, 100)}...`
                      : data.config.systemPrompt
                    }
                  </p>
                </div>
              )}
            </div>
          </motion.div>
        )}

        {/* Output Handle */}
        <Handle
          type="source"
          position={Position.Bottom}
          className="w-3 h-3 bg-primary-500 border-2 border-white"
        />

        {/* Running Animation */}
        {data.status === 'running' && (
          <motion.div
            className="absolute inset-0 border-2 border-warning-500 rounded-lg"
            animate={{
              opacity: [0.5, 1, 0.5],
            }}
            transition={{
              duration: 2,
              repeat: Infinity,
              ease: "easeInOut",
            }}
          />
        )}
      </motion.div>
    </>
  )
})

AgentNode.displayName = 'AgentNode'
