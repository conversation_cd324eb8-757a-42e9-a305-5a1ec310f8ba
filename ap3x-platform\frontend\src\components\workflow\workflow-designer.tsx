'use client'

import { useState, useCallback, useRef, useEffect } from 'react'
import React<PERSON>low, {
  Node,
  Edge,
  addEdge,
  Connection,
  useNodesState,
  useEdgesState,
  Controls,
  Background,
  MiniMap,
  ReactFlowProvider,
  ReactFlowInstance,
  NodeTypes,
  EdgeTypes,
} from 'react-flow-renderer'
import { motion, AnimatePresence } from 'framer-motion'
import {
  PlusIcon,
  PlayIcon,
  PauseIcon,
  StopIcon,
  SaveIcon,
  ShareIcon,
  CogIcon,
  TrashIcon,
} from '@heroicons/react/24/outline'
import { Button } from '@/components/ui/button'
import { Card } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { AgentNode } from './nodes/agent-node'
import { ConditionNode } from './nodes/condition-node'
import { TriggerNode } from './nodes/trigger-node'
import { ActionNode } from './nodes/action-node'
import { CustomEdge } from './edges/custom-edge'
import { NodePalette } from './node-palette'
import { WorkflowSettings } from './workflow-settings'
import { ExecutionPanel } from './execution-panel'
import toast from 'react-hot-toast'

// Node types
const nodeTypes: NodeTypes = {
  agent: AgentNode,
  condition: ConditionNode,
  trigger: TriggerNode,
  action: ActionNode,
}

// Edge types
const edgeTypes: EdgeTypes = {
  custom: CustomEdge,
}

// Initial nodes
const initialNodes: Node[] = [
  {
    id: 'start',
    type: 'trigger',
    position: { x: 100, y: 100 },
    data: {
      label: 'Start',
      type: 'manual',
      config: {},
    },
  },
]

// Initial edges
const initialEdges: Edge[] = []

export interface WorkflowDesignerProps {
  workflowId?: string
  onSave?: (workflow: any) => void
  onExecute?: (workflow: any) => void
  readonly?: boolean
}

export function WorkflowDesigner({
  workflowId,
  onSave,
  onExecute,
  readonly = false,
}: WorkflowDesignerProps) {
  const [nodes, setNodes, onNodesChange] = useNodesState(initialNodes)
  const [edges, setEdges, onEdgesChange] = useEdgesState(initialEdges)
  const [reactFlowInstance, setReactFlowInstance] = useState<ReactFlowInstance | null>(null)
  const [selectedNode, setSelectedNode] = useState<Node | null>(null)
  const [selectedEdge, setSelectedEdge] = useState<Edge | null>(null)
  const [isExecuting, setIsExecuting] = useState(false)
  const [executionStatus, setExecutionStatus] = useState<'idle' | 'running' | 'paused' | 'completed' | 'error'>('idle')
  const [showSettings, setShowSettings] = useState(false)
  const [showExecution, setShowExecution] = useState(false)
  const [workflowName, setWorkflowName] = useState('Untitled Workflow')
  const [workflowDescription, setWorkflowDescription] = useState('')
  const reactFlowWrapper = useRef<HTMLDivElement>(null)

  // Handle connection between nodes
  const onConnect = useCallback(
    (params: Connection) => {
      const edge = {
        ...params,
        type: 'custom',
        animated: true,
        data: {
          label: '',
          condition: null,
        },
      }
      setEdges((eds) => addEdge(edge, eds))
    },
    [setEdges]
  )

  // Handle drag over
  const onDragOver = useCallback((event: React.DragEvent) => {
    event.preventDefault()
    event.dataTransfer.dropEffect = 'move'
  }, [])

  // Handle drop
  const onDrop = useCallback(
    (event: React.DragEvent) => {
      event.preventDefault()

      const reactFlowBounds = reactFlowWrapper.current?.getBoundingClientRect()
      const type = event.dataTransfer.getData('application/reactflow')

      if (typeof type === 'undefined' || !type || !reactFlowInstance || !reactFlowBounds) {
        return
      }

      const position = reactFlowInstance.project({
        x: event.clientX - reactFlowBounds.left,
        y: event.clientY - reactFlowBounds.top,
      })

      const newNode: Node = {
        id: `${type}-${Date.now()}`,
        type,
        position,
        data: {
          label: `${type.charAt(0).toUpperCase() + type.slice(1)} Node`,
          type,
          config: {},
        },
      }

      setNodes((nds) => nds.concat(newNode))
    },
    [reactFlowInstance, setNodes]
  )

  // Handle node selection
  const onNodeClick = useCallback((event: React.MouseEvent, node: Node) => {
    setSelectedNode(node)
    setSelectedEdge(null)
  }, [])

  // Handle edge selection
  const onEdgeClick = useCallback((event: React.MouseEvent, edge: Edge) => {
    setSelectedEdge(edge)
    setSelectedNode(null)
  }, [])

  // Handle node deletion
  const onNodesDelete = useCallback(
    (deletedNodes: Node[]) => {
      if (readonly) return
      
      // Don't allow deletion of start node
      const filteredNodes = deletedNodes.filter(node => node.id !== 'start')
      if (filteredNodes.length !== deletedNodes.length) {
        toast.error('Cannot delete the start node')
      }
    },
    [readonly]
  )

  // Save workflow
  const handleSave = useCallback(async () => {
    if (readonly) return

    try {
      const workflow = {
        id: workflowId || `workflow-${Date.now()}`,
        name: workflowName,
        description: workflowDescription,
        nodes,
        edges,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      }

      if (onSave) {
        await onSave(workflow)
      }

      toast.success('Workflow saved successfully')
    } catch (error) {
      toast.error('Failed to save workflow')
      console.error('Save error:', error)
    }
  }, [workflowId, workflowName, workflowDescription, nodes, edges, onSave, readonly])

  // Execute workflow
  const handleExecute = useCallback(async () => {
    if (readonly || isExecuting) return

    try {
      setIsExecuting(true)
      setExecutionStatus('running')
      setShowExecution(true)

      const workflow = {
        id: workflowId || `workflow-${Date.now()}`,
        name: workflowName,
        description: workflowDescription,
        nodes,
        edges,
      }

      if (onExecute) {
        await onExecute(workflow)
      }

      setExecutionStatus('completed')
      toast.success('Workflow executed successfully')
    } catch (error) {
      setExecutionStatus('error')
      toast.error('Workflow execution failed')
      console.error('Execution error:', error)
    } finally {
      setIsExecuting(false)
    }
  }, [workflowId, workflowName, workflowDescription, nodes, edges, onExecute, readonly, isExecuting])

  // Validate workflow
  const validateWorkflow = useCallback(() => {
    const errors: string[] = []

    // Check for disconnected nodes
    const connectedNodeIds = new Set<string>()
    edges.forEach(edge => {
      connectedNodeIds.add(edge.source)
      connectedNodeIds.add(edge.target)
    })

    const disconnectedNodes = nodes.filter(node => 
      node.id !== 'start' && !connectedNodeIds.has(node.id)
    )

    if (disconnectedNodes.length > 0) {
      errors.push(`${disconnectedNodes.length} disconnected node(s) found`)
    }

    // Check for cycles
    const hasCycle = detectCycle(nodes, edges)
    if (hasCycle) {
      errors.push('Workflow contains cycles')
    }

    return errors
  }, [nodes, edges])

  // Detect cycles in workflow
  const detectCycle = (nodes: Node[], edges: Edge[]): boolean => {
    const visited = new Set<string>()
    const recursionStack = new Set<string>()

    const dfs = (nodeId: string): boolean => {
      visited.add(nodeId)
      recursionStack.add(nodeId)

      const outgoingEdges = edges.filter(edge => edge.source === nodeId)
      for (const edge of outgoingEdges) {
        if (!visited.has(edge.target)) {
          if (dfs(edge.target)) return true
        } else if (recursionStack.has(edge.target)) {
          return true
        }
      }

      recursionStack.delete(nodeId)
      return false
    }

    for (const node of nodes) {
      if (!visited.has(node.id)) {
        if (dfs(node.id)) return true
      }
    }

    return false
  }

  // Auto-save
  useEffect(() => {
    if (readonly) return

    const autoSaveInterval = setInterval(() => {
      if (nodes.length > 1 || edges.length > 0) {
        handleSave()
      }
    }, 30000) // Auto-save every 30 seconds

    return () => clearInterval(autoSaveInterval)
  }, [handleSave, nodes, edges, readonly])

  return (
    <div className="h-full flex flex-col bg-secondary-950">
      {/* Toolbar */}
      <div className="flex items-center justify-between p-4 bg-secondary-900 border-b border-secondary-700">
        <div className="flex items-center space-x-4">
          <div>
            <h2 className="text-lg font-semibold text-white">{workflowName}</h2>
            <p className="text-sm text-secondary-400">{workflowDescription || 'No description'}</p>
          </div>
          <Badge className={`badge-${executionStatus === 'running' ? 'warning' : executionStatus === 'completed' ? 'success' : executionStatus === 'error' ? 'error' : 'secondary'}`}>
            {executionStatus}
          </Badge>
        </div>

        <div className="flex items-center space-x-2">
          {!readonly && (
            <>
              <Button
                variant="ghost"
                size="sm"
                onClick={handleSave}
                className="btn-ghost"
              >
                <SaveIcon className="w-4 h-4 mr-2" />
                Save
              </Button>

              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowSettings(true)}
                className="btn-ghost"
              >
                <CogIcon className="w-4 h-4 mr-2" />
                Settings
              </Button>
            </>
          )}

          <Button
            variant="ghost"
            size="sm"
            onClick={() => setShowExecution(!showExecution)}
            className="btn-ghost"
          >
            <PlayIcon className="w-4 h-4 mr-2" />
            Execution
          </Button>

          {!readonly && (
            <Button
              onClick={handleExecute}
              disabled={isExecuting}
              className="btn-primary"
            >
              {isExecuting ? (
                <PauseIcon className="w-4 h-4 mr-2" />
              ) : (
                <PlayIcon className="w-4 h-4 mr-2" />
              )}
              {isExecuting ? 'Running...' : 'Execute'}
            </Button>
          )}
        </div>
      </div>

      <div className="flex-1 flex">
        {/* Node Palette */}
        {!readonly && (
          <div className="w-64 bg-secondary-900 border-r border-secondary-700">
            <NodePalette />
          </div>
        )}

        {/* Main Canvas */}
        <div className="flex-1 relative" ref={reactFlowWrapper}>
          <ReactFlowProvider>
            <ReactFlow
              nodes={nodes}
              edges={edges}
              onNodesChange={onNodesChange}
              onEdgesChange={onEdgesChange}
              onConnect={onConnect}
              onInit={setReactFlowInstance}
              onDrop={onDrop}
              onDragOver={onDragOver}
              onNodeClick={onNodeClick}
              onEdgeClick={onEdgeClick}
              onNodesDelete={onNodesDelete}
              nodeTypes={nodeTypes}
              edgeTypes={edgeTypes}
              fitView
              attributionPosition="bottom-left"
              className="workflow-canvas"
              deleteKeyCode={readonly ? null : 'Delete'}
            >
              <Background color="#1e293b" gap={20} />
              <Controls className="bg-secondary-800 border border-secondary-600" />
              <MiniMap
                className="bg-secondary-800 border border-secondary-600"
                nodeColor="#3b82f6"
                maskColor="rgba(0, 0, 0, 0.2)"
              />
            </ReactFlow>
          </ReactFlowProvider>

          {/* Validation Errors */}
          <AnimatePresence>
            {(() => {
              const errors = validateWorkflow()
              return errors.length > 0 ? (
                <motion.div
                  initial={{ opacity: 0, y: -20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -20 }}
                  className="absolute top-4 left-4 right-4 z-10"
                >
                  <Card className="card bg-error-600/20 border-error-500/30 p-4">
                    <h4 className="font-medium text-error-300 mb-2">Workflow Validation Errors</h4>
                    <ul className="text-sm text-error-400 space-y-1">
                      {errors.map((error, index) => (
                        <li key={index}>• {error}</li>
                      ))}
                    </ul>
                  </Card>
                </motion.div>
              ) : null
            })()}
          </AnimatePresence>
        </div>

        {/* Execution Panel */}
        <AnimatePresence>
          {showExecution && (
            <motion.div
              initial={{ width: 0, opacity: 0 }}
              animate={{ width: 400, opacity: 1 }}
              exit={{ width: 0, opacity: 0 }}
              className="bg-secondary-900 border-l border-secondary-700 overflow-hidden"
            >
              <ExecutionPanel
                workflow={{ nodes, edges }}
                status={executionStatus}
                onClose={() => setShowExecution(false)}
              />
            </motion.div>
          )}
        </AnimatePresence>
      </div>

      {/* Settings Modal */}
      <AnimatePresence>
        {showSettings && (
          <WorkflowSettings
            name={workflowName}
            description={workflowDescription}
            onNameChange={setWorkflowName}
            onDescriptionChange={setWorkflowDescription}
            onClose={() => setShowSettings(false)}
            onSave={handleSave}
          />
        )}
      </AnimatePresence>
    </div>
  )
}
