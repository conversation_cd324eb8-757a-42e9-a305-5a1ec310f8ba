{"name": "ms", "version": "2.0.0", "description": "Tiny milisecond conversion utility", "repository": "zeit/ms", "main": "./index", "files": ["index.js"], "scripts": {"precommit": "lint-staged", "lint": "eslint lib/* bin/*", "test": "mocha tests.js"}, "eslintConfig": {"extends": "eslint:recommended", "env": {"node": true, "es6": true}}, "lint-staged": {"*.js": ["npm run lint", "prettier --single-quote --write", "git add"]}, "license": "MIT", "devDependencies": {"eslint": "3.19.0", "expect.js": "0.3.1", "husky": "0.13.3", "lint-staged": "3.4.1", "mocha": "3.4.1"}}