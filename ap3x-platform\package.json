{"name": "ap3x-platform", "version": "1.0.0", "description": "The most powerful agentic coding platform ever created - combining AIDER, AG3NT Framework, and advanced context understanding", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc", "start": "node dist/index.js", "dev": "ts-node src/index.ts", "watch": "ts-node --watch src/index.ts", "test": "jest", "lint": "eslint src/**/*.ts", "clean": "<PERSON><PERSON><PERSON> dist", "prepare": "npm run build"}, "keywords": ["ai", "agents", "coding", "aider", "ag3nt", "multi-agent", "code-editing", "collaboration", "platform"], "author": "AP3X Team", "license": "MIT", "dependencies": {"express": "^4.18.2", "socket.io": "^4.7.4", "cors": "^2.8.5", "helmet": "^7.1.0", "express-rate-limit": "^7.1.5", "ws": "^8.14.2", "uuid": "^9.0.1", "dotenv": "^16.3.1", "winston": "^3.11.0"}, "devDependencies": {"@types/node": "^20.10.0", "@types/express": "^4.17.21", "@types/cors": "^2.8.17", "@types/uuid": "^9.0.7", "@types/ws": "^8.5.10", "typescript": "^5.3.2", "ts-node": "^10.9.1", "jest": "^29.7.0", "@types/jest": "^29.5.8", "eslint": "^8.54.0", "@typescript-eslint/eslint-plugin": "^6.12.0", "@typescript-eslint/parser": "^6.12.0", "rimraf": "^5.0.5", "nodemon": "^3.0.1"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "repository": {"type": "git", "url": "https://github.com/ap3x/ap3x-platform.git"}, "bugs": {"url": "https://github.com/ap3x/ap3x-platform/issues"}, "homepage": "https://github.com/ap3x/ap3x-platform#readme"}