#!/usr/bin/env ts-node

/**
 * AP3X Platform - Simple Startup Script
 * 
 * A simplified startup script that demonstrates the AP3X Platform
 * without complex dependencies.
 */

import { SimpleAP3XPlatform } from './src/core/simple-ap3x-platform'
import { config as dotenvConfig } from 'dotenv'

// Load environment variables
dotenvConfig()

/**
 * Create platform configuration
 */
function createConfiguration() {
  return {
    port: parseInt(process.env.PORT || '3000'),
    host: process.env.HOST || 'localhost',
    environment: (process.env.NODE_ENV as any) || 'development',
    enableCORS: true,
    enableRateLimit: true,
    corsOrigins: process.env.CORS_ORIGINS?.split(',') || ['http://localhost:3000', 'http://localhost:3001'],
  }
}

/**
 * Setup graceful shutdown
 */
function setupGracefulShutdown(platform: SimpleAP3XPlatform): void {
  const shutdown = async (signal: string) => {
    console.log(`\n🔄 Received ${signal}, shutting down gracefully...`)
    
    try {
      await platform.stop()
      console.log('✅ AP3X Platform shutdown complete')
      process.exit(0)
    } catch (error) {
      console.error('❌ Error during shutdown:', error)
      process.exit(1)
    }
  }

  process.on('SIGINT', () => shutdown('SIGINT'))
  process.on('SIGTERM', () => shutdown('SIGTERM'))
  process.on('SIGUSR2', () => shutdown('SIGUSR2')) // nodemon restart
}

/**
 * Main startup function
 */
async function main(): Promise<void> {
  try {
    console.log('🚀 Starting AP3X Platform (Simple Mode)...')
    
    // Create configuration
    const config = createConfiguration()
    
    console.log('📋 Configuration:')
    console.log(`   🌐 Environment: ${config.environment}`)
    console.log(`   🚪 Port: ${config.port}`)
    console.log(`   🏠 Host: ${config.host}`)
    console.log('')
    
    // Create and start platform
    const platform = new SimpleAP3XPlatform(config)
    
    // Setup graceful shutdown
    setupGracefulShutdown(platform)
    
    // Setup event listeners
    platform.on('started', () => {
      console.log('')
      console.log('🎉 SUCCESS! AP3X Platform is now running!')
      console.log('')
      console.log('🌟 QUICK START GUIDE:')
      console.log(`   1. Open your browser: http://${config.host}:${config.port}`)
      console.log(`   2. Check health: http://${config.host}:${config.port}/api/health`)
      console.log(`   3. View platform info: http://${config.host}:${config.port}/api/info`)
      console.log('')
      console.log('📚 API ENDPOINTS:')
      console.log('   • GET /api/health - Health check')
      console.log('   • GET /api/info - Platform information')
      console.log('   • GET /api/agents - Available agents')
      console.log('   • GET /api/sessions - Active sessions')
      console.log('   • POST /api/code/edit - Code editing (demo)')
      console.log('')
      console.log('🔥 THE FUTURE OF CODING IS HERE! 🔥')
    })
    
    // Start the platform
    await platform.start()
    
  } catch (error) {
    console.error('💥 Failed to start AP3X Platform:', error)
    process.exit(1)
  }
}

// Run the main function
if (require.main === module) {
  main().catch((error) => {
    console.error('💥 Startup error:', error)
    process.exit(1)
  })
}

export { main as startSimpleAP3X }
