/**
 * AP3X Platform - Unified API Gateway
 * 
 * Central API gateway that routes requests between AIDER, AG3NT Framework,
 * and Context Engine, providing a unified interface for the frontend.
 * 
 * Features:
 * - RESTful API endpoints
 * - WebSocket support for real-time updates
 * - Request routing and load balancing
 * - Authentication and authorization
 * - Rate limiting and monitoring
 * - Error handling and logging
 */

import express from 'express'
import { createServer } from 'http'
import { Server as SocketIOServer } from 'socket.io'
import cors from 'cors'
import helmet from 'helmet'
import rateLimit from 'express-rate-limit'
import { AP3XBridge, ap3xBridge } from '../core/ap3x-bridge'

export interface GatewayConfig {
  port: number
  corsOrigins: string[]
  enableRateLimit: boolean
  enableWebSockets: boolean
  enableAuthentication: boolean
}

export interface APIResponse<T = any> {
  success: boolean
  data?: T
  error?: string
  timestamp: string
  requestId: string
}

/**
 * AP3X API Gateway
 */
export class AP3XGateway {
  private app: express.Application
  private server: any
  private io?: SocketIOServer
  private bridge: AP3XBridge
  private config: GatewayConfig
  private isRunning = false

  constructor(bridge: AP3XBridge, config: GatewayConfig) {
    this.bridge = bridge
    this.config = config
    this.app = express()
    this.server = createServer(this.app)
    
    if (config.enableWebSockets) {
      this.io = new SocketIOServer(this.server, {
        cors: {
          origin: config.corsOrigins,
          methods: ['GET', 'POST']
        }
      })
    }

    this.setupMiddleware()
    this.setupRoutes()
    this.setupWebSockets()
    this.setupEventListeners()
  }

  /**
   * Setup Express middleware
   */
  private setupMiddleware(): void {
    // Security
    this.app.use(helmet())
    
    // CORS
    this.app.use(cors({
      origin: this.config.corsOrigins,
      credentials: true
    }))

    // Body parsing
    this.app.use(express.json({ limit: '10mb' }))
    this.app.use(express.urlencoded({ extended: true }))

    // Rate limiting
    if (this.config.enableRateLimit) {
      const limiter = rateLimit({
        windowMs: 15 * 60 * 1000, // 15 minutes
        max: 100, // limit each IP to 100 requests per windowMs
        message: 'Too many requests from this IP'
      })
      this.app.use('/api/', limiter)
    }

    // Request ID and logging
    this.app.use((req, res, next) => {
      req.requestId = `req-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
      console.log(`${new Date().toISOString()} [${req.requestId}] ${req.method} ${req.path}`)
      next()
    })
  }

  /**
   * Setup API routes
   */
  private setupRoutes(): void {
    // Health check
    this.app.get('/api/health', (req, res) => {
      res.json(this.createResponse(true, {
        status: 'healthy',
        platform: 'AP3X',
        version: '1.0.0',
        uptime: process.uptime(),
        components: {
          bridge: this.bridge ? 'connected' : 'disconnected',
          websockets: this.io ? 'enabled' : 'disabled'
        }
      }, req.requestId))
    })

    // Session management
    this.app.post('/api/sessions', async (req, res) => {
      try {
        const { userId, projectId } = req.body
        const sessionId = await this.bridge.createSession(userId, projectId)
        
        res.json(this.createResponse(true, { sessionId }, req.requestId))
      } catch (error) {
        res.status(500).json(this.createResponse(false, null, req.requestId, error.message))
      }
    })

    this.app.get('/api/sessions/:sessionId', (req, res) => {
      try {
        const session = this.bridge.getSession(req.params.sessionId)
        if (!session) {
          return res.status(404).json(this.createResponse(false, null, req.requestId, 'Session not found'))
        }
        
        res.json(this.createResponse(true, session, req.requestId))
      } catch (error) {
        res.status(500).json(this.createResponse(false, null, req.requestId, error.message))
      }
    })

    this.app.get('/api/sessions', (req, res) => {
      try {
        const sessions = this.bridge.getActiveSessions()
        res.json(this.createResponse(true, sessions, req.requestId))
      } catch (error) {
        res.status(500).json(this.createResponse(false, null, req.requestId, error.message))
      }
    })

    // Code editing
    this.app.post('/api/code/edit', async (req, res) => {
      try {
        const result = await this.bridge.executeCodeEditing(req.body)
        res.json(this.createResponse(true, result, req.requestId))
      } catch (error) {
        res.status(500).json(this.createResponse(false, null, req.requestId, error.message))
      }
    })

    // Agent management
    this.app.get('/api/agents', async (req, res) => {
      try {
        // Get registered agents from AG3NT Framework
        const agents = await this.bridge.ag3ntFramework.getRegisteredAgents()
        res.json(this.createResponse(true, agents, req.requestId))
      } catch (error) {
        res.status(500).json(this.createResponse(false, null, req.requestId, error.message))
      }
    })

    this.app.post('/api/agents/:agentType/execute', async (req, res) => {
      try {
        const { agentType } = req.params
        const result = await this.bridge.ag3ntFramework.execute(agentType, req.body)
        res.json(this.createResponse(true, result, req.requestId))
      } catch (error) {
        res.status(500).json(this.createResponse(false, null, req.requestId, error.message))
      }
    })

    // Context engine
    this.app.post('/api/context/query', async (req, res) => {
      try {
        const result = await this.bridge.contextEngine.getContext(req.body.query, req.body.options)
        res.json(this.createResponse(true, result, req.requestId))
      } catch (error) {
        res.status(500).json(this.createResponse(false, null, req.requestId, error.message))
      }
    })

    // Project management
    this.app.get('/api/projects', (req, res) => {
      // Placeholder for project management
      res.json(this.createResponse(true, [], req.requestId))
    })

    this.app.post('/api/projects', (req, res) => {
      // Placeholder for project creation
      res.json(this.createResponse(true, { id: 'project-1' }, req.requestId))
    })

    // Workflow management
    this.app.get('/api/workflows', (req, res) => {
      // Placeholder for workflow management
      res.json(this.createResponse(true, [], req.requestId))
    })

    this.app.post('/api/workflows', (req, res) => {
      // Placeholder for workflow creation
      res.json(this.createResponse(true, { id: 'workflow-1' }, req.requestId))
    })

    // Error handling
    this.app.use((error: any, req: any, res: any, next: any) => {
      console.error(`Error in ${req.method} ${req.path}:`, error)
      res.status(500).json(this.createResponse(false, null, req.requestId, 'Internal server error'))
    })

    // 404 handler
    this.app.use('*', (req, res) => {
      res.status(404).json(this.createResponse(false, null, req.requestId, 'Endpoint not found'))
    })
  }

  /**
   * Setup WebSocket connections
   */
  private setupWebSockets(): void {
    if (!this.io) return

    this.io.on('connection', (socket) => {
      console.log(`WebSocket client connected: ${socket.id}`)

      // Join session room
      socket.on('join_session', (sessionId: string) => {
        socket.join(`session:${sessionId}`)
        console.log(`Client ${socket.id} joined session ${sessionId}`)
      })

      // Leave session room
      socket.on('leave_session', (sessionId: string) => {
        socket.leave(`session:${sessionId}`)
        console.log(`Client ${socket.id} left session ${sessionId}`)
      })

      // Handle real-time code editing
      socket.on('code_edit_request', async (data) => {
        try {
          const result = await this.bridge.executeCodeEditing(data)
          socket.emit('code_edit_result', result)
          
          // Broadcast to session room
          if (data.sessionId) {
            socket.to(`session:${data.sessionId}`).emit('code_edit_update', result)
          }
        } catch (error) {
          socket.emit('code_edit_error', { error: error.message })
        }
      })

      socket.on('disconnect', () => {
        console.log(`WebSocket client disconnected: ${socket.id}`)
      })
    })
  }

  /**
   * Setup event listeners for bridge events
   */
  private setupEventListeners(): void {
    // Bridge events
    this.bridge.on('session_created', (data) => {
      this.broadcastToSession(data.sessionId, 'session_created', data)
    })

    this.bridge.on('code_editing_completed', (data) => {
      this.broadcastToSession(data.sessionId, 'code_editing_completed', data)
    })

    this.bridge.on('agent_registered', (data) => {
      this.broadcast('agent_registered', data)
    })

    this.bridge.on('task_completed', (data) => {
      this.broadcast('task_completed', data)
    })

    this.bridge.on('context_updated', (data) => {
      this.broadcast('context_updated', data)
    })
  }

  /**
   * Broadcast message to all clients
   */
  private broadcast(event: string, data: any): void {
    if (this.io) {
      this.io.emit(event, data)
    }
  }

  /**
   * Broadcast message to specific session
   */
  private broadcastToSession(sessionId: string, event: string, data: any): void {
    if (this.io) {
      this.io.to(`session:${sessionId}`).emit(event, data)
    }
  }

  /**
   * Create standardized API response
   */
  private createResponse<T>(
    success: boolean, 
    data: T | null, 
    requestId: string, 
    error?: string
  ): APIResponse<T> {
    return {
      success,
      data: data || undefined,
      error,
      timestamp: new Date().toISOString(),
      requestId
    }
  }

  /**
   * Start the gateway server
   */
  async start(): Promise<void> {
    if (this.isRunning) return

    // Initialize the bridge first
    await this.bridge.initialize()

    return new Promise((resolve) => {
      this.server.listen(this.config.port, () => {
        this.isRunning = true
        console.log(`🚀 AP3X Gateway running on port ${this.config.port}`)
        console.log(`🌐 WebSockets: ${this.config.enableWebSockets ? 'enabled' : 'disabled'}`)
        resolve()
      })
    })
  }

  /**
   * Stop the gateway server
   */
  async stop(): Promise<void> {
    if (!this.isRunning) return

    return new Promise((resolve) => {
      this.server.close(() => {
        this.isRunning = false
        console.log('🔄 AP3X Gateway stopped')
        resolve()
      })
    })
  }
}

// Create and export gateway instance
export const ap3xGateway = new AP3XGateway(ap3xBridge, {
  port: 3000,
  corsOrigins: ['http://localhost:3001', 'http://localhost:3000'],
  enableRateLimit: true,
  enableWebSockets: true,
  enableAuthentication: false // Will be enabled later
})

export default AP3XGateway

// Extend Express Request interface
declare global {
  namespace Express {
    interface Request {
      requestId: string
    }
  }
}
