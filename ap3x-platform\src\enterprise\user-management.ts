/**
 * AP3X Platform - Enterprise User Management
 * 
 * Comprehensive user management system for enterprise deployments.
 * 
 * Features:
 * - User authentication and authorization
 * - Role-based access control (RBAC)
 * - Team and organization management
 * - Single Sign-On (SSO) integration
 * - Audit logging
 * - User activity tracking
 */

import { EventEmitter } from 'events'
import bcrypt from 'bcrypt'
import jwt from 'jsonwebtoken'
import { v4 as uuidv4 } from 'uuid'

export interface User {
  id: string
  email: string
  username: string
  firstName: string
  lastName: string
  avatar?: string
  status: 'active' | 'inactive' | 'suspended' | 'pending'
  roles: Role[]
  teams: string[]
  organizationId: string
  preferences: UserPreferences
  metadata: {
    createdAt: Date
    updatedAt: Date
    lastLoginAt?: Date
    loginCount: number
    emailVerified: boolean
    twoFactorEnabled: boolean
  }
}

export interface Role {
  id: string
  name: string
  description: string
  permissions: Permission[]
  isSystemRole: boolean
  organizationId?: string
}

export interface Permission {
  id: string
  resource: string
  action: string
  conditions?: PermissionCondition[]
}

export interface PermissionCondition {
  field: string
  operator: 'equals' | 'not_equals' | 'in' | 'not_in' | 'contains' | 'starts_with'
  value: any
}

export interface UserPreferences {
  theme: 'light' | 'dark' | 'auto'
  language: string
  timezone: string
  notifications: {
    email: boolean
    push: boolean
    desktop: boolean
    frequency: 'immediate' | 'hourly' | 'daily' | 'weekly'
  }
  editor: {
    fontSize: number
    tabSize: number
    wordWrap: boolean
    minimap: boolean
    theme: string
  }
}

export interface Organization {
  id: string
  name: string
  slug: string
  description?: string
  logo?: string
  settings: OrganizationSettings
  subscription: {
    plan: 'free' | 'pro' | 'enterprise'
    status: 'active' | 'cancelled' | 'past_due'
    limits: {
      users: number
      projects: number
      agents: number
      storage: number // in GB
    }
  }
  metadata: {
    createdAt: Date
    updatedAt: Date
    ownerId: string
  }
}

export interface OrganizationSettings {
  allowUserRegistration: boolean
  requireEmailVerification: boolean
  enforceStrongPasswords: boolean
  sessionTimeout: number // in minutes
  maxLoginAttempts: number
  sso: {
    enabled: boolean
    provider?: 'google' | 'microsoft' | 'okta' | 'auth0' | 'saml'
    config?: any
  }
  security: {
    requireTwoFactor: boolean
    allowedDomains: string[]
    ipWhitelist: string[]
  }
}

export interface Team {
  id: string
  name: string
  description?: string
  organizationId: string
  members: TeamMember[]
  projects: string[]
  settings: TeamSettings
  metadata: {
    createdAt: Date
    updatedAt: Date
    createdBy: string
  }
}

export interface TeamMember {
  userId: string
  role: 'owner' | 'admin' | 'member' | 'viewer'
  joinedAt: Date
  invitedBy: string
}

export interface TeamSettings {
  visibility: 'public' | 'private'
  allowMemberInvites: boolean
  defaultProjectRole: string
}

export interface AuthToken {
  accessToken: string
  refreshToken: string
  expiresAt: Date
  tokenType: 'Bearer'
  scope: string[]
}

export interface LoginAttempt {
  id: string
  userId?: string
  email: string
  ipAddress: string
  userAgent: string
  success: boolean
  failureReason?: string
  timestamp: Date
}

/**
 * Enterprise User Management System
 */
export class UserManagement extends EventEmitter {
  private users: Map<string, User> = new Map()
  private organizations: Map<string, Organization> = new Map()
  private teams: Map<string, Team> = new Map()
  private roles: Map<string, Role> = new Map()
  private sessions: Map<string, { userId: string; expiresAt: Date }> = new Map()
  private loginAttempts: Map<string, LoginAttempt[]> = new Map()
  private jwtSecret: string
  private isInitialized = false

  constructor(jwtSecret: string) {
    super()
    this.jwtSecret = jwtSecret
  }

  /**
   * Initialize user management system
   */
  async initialize(): Promise<void> {
    if (this.isInitialized) return

    console.log('👥 Initializing User Management System...')

    try {
      // Create default system roles
      await this.createSystemRoles()

      // Create default organization if none exists
      if (this.organizations.size === 0) {
        await this.createDefaultOrganization()
      }

      // Start cleanup intervals
      this.startCleanupIntervals()

      this.isInitialized = true
      this.emit('initialized')
      console.log('✅ User Management System initialized successfully')

    } catch (error) {
      console.error('❌ Failed to initialize User Management System:', error)
      throw error
    }
  }

  /**
   * Create system roles
   */
  private async createSystemRoles(): Promise<void> {
    const systemRoles = [
      {
        id: 'system-admin',
        name: 'System Administrator',
        description: 'Full system access',
        permissions: [
          { id: 'all', resource: '*', action: '*' }
        ],
        isSystemRole: true,
      },
      {
        id: 'org-admin',
        name: 'Organization Administrator',
        description: 'Organization management access',
        permissions: [
          { id: 'org-manage', resource: 'organization', action: '*' },
          { id: 'user-manage', resource: 'user', action: '*' },
          { id: 'team-manage', resource: 'team', action: '*' },
          { id: 'project-manage', resource: 'project', action: '*' },
        ],
        isSystemRole: true,
      },
      {
        id: 'team-lead',
        name: 'Team Lead',
        description: 'Team and project management',
        permissions: [
          { id: 'team-manage', resource: 'team', action: 'read,write' },
          { id: 'project-manage', resource: 'project', action: '*' },
          { id: 'agent-manage', resource: 'agent', action: '*' },
        ],
        isSystemRole: true,
      },
      {
        id: 'developer',
        name: 'Developer',
        description: 'Development access',
        permissions: [
          { id: 'project-access', resource: 'project', action: 'read,write' },
          { id: 'agent-use', resource: 'agent', action: 'read,execute' },
          { id: 'workflow-manage', resource: 'workflow', action: '*' },
        ],
        isSystemRole: true,
      },
      {
        id: 'viewer',
        name: 'Viewer',
        description: 'Read-only access',
        permissions: [
          { id: 'project-view', resource: 'project', action: 'read' },
          { id: 'agent-view', resource: 'agent', action: 'read' },
          { id: 'workflow-view', resource: 'workflow', action: 'read' },
        ],
        isSystemRole: true,
      },
    ]

    for (const roleData of systemRoles) {
      this.roles.set(roleData.id, roleData as Role)
    }

    console.log('✅ System roles created')
  }

  /**
   * Create default organization
   */
  private async createDefaultOrganization(): Promise<void> {
    const defaultOrg: Organization = {
      id: 'default-org',
      name: 'Default Organization',
      slug: 'default',
      description: 'Default organization for AP3X Platform',
      settings: {
        allowUserRegistration: true,
        requireEmailVerification: false,
        enforceStrongPasswords: true,
        sessionTimeout: 480, // 8 hours
        maxLoginAttempts: 5,
        sso: { enabled: false },
        security: {
          requireTwoFactor: false,
          allowedDomains: [],
          ipWhitelist: [],
        },
      },
      subscription: {
        plan: 'enterprise',
        status: 'active',
        limits: {
          users: 1000,
          projects: 100,
          agents: 50,
          storage: 1000,
        },
      },
      metadata: {
        createdAt: new Date(),
        updatedAt: new Date(),
        ownerId: 'system',
      },
    }

    this.organizations.set(defaultOrg.id, defaultOrg)
    console.log('✅ Default organization created')
  }

  /**
   * Register new user
   */
  async registerUser(userData: {
    email: string
    username: string
    password: string
    firstName: string
    lastName: string
    organizationId?: string
  }): Promise<User> {
    // Validate email uniqueness
    const existingUser = Array.from(this.users.values()).find(u => u.email === userData.email)
    if (existingUser) {
      throw new Error('Email already registered')
    }

    // Validate username uniqueness
    const existingUsername = Array.from(this.users.values()).find(u => u.username === userData.username)
    if (existingUsername) {
      throw new Error('Username already taken')
    }

    // Hash password
    const hashedPassword = await bcrypt.hash(userData.password, 12)

    // Create user
    const user: User = {
      id: uuidv4(),
      email: userData.email,
      username: userData.username,
      firstName: userData.firstName,
      lastName: userData.lastName,
      status: 'active',
      roles: [this.roles.get('developer')!], // Default role
      teams: [],
      organizationId: userData.organizationId || 'default-org',
      preferences: this.getDefaultPreferences(),
      metadata: {
        createdAt: new Date(),
        updatedAt: new Date(),
        loginCount: 0,
        emailVerified: false,
        twoFactorEnabled: false,
      },
    }

    // Store user (in production, this would be in a database)
    this.users.set(user.id, user)

    // Store password separately (in production, use secure storage)
    // For demo purposes, we'll store it in memory
    (user as any).hashedPassword = hashedPassword

    this.emit('user_registered', { user })
    console.log(`👥 User registered: ${user.email}`)

    return user
  }

  /**
   * Authenticate user
   */
  async authenticateUser(email: string, password: string, ipAddress: string, userAgent: string): Promise<{
    user: User
    tokens: AuthToken
  }> {
    const loginAttempt: LoginAttempt = {
      id: uuidv4(),
      email,
      ipAddress,
      userAgent,
      success: false,
      timestamp: new Date(),
    }

    try {
      // Find user
      const user = Array.from(this.users.values()).find(u => u.email === email)
      if (!user) {
        loginAttempt.failureReason = 'User not found'
        this.recordLoginAttempt(loginAttempt)
        throw new Error('Invalid credentials')
      }

      loginAttempt.userId = user.id

      // Check if user is active
      if (user.status !== 'active') {
        loginAttempt.failureReason = 'Account inactive'
        this.recordLoginAttempt(loginAttempt)
        throw new Error('Account is not active')
      }

      // Check login attempts
      const recentAttempts = this.getRecentLoginAttempts(email)
      const org = this.organizations.get(user.organizationId)
      const maxAttempts = org?.settings.maxLoginAttempts || 5

      if (recentAttempts.length >= maxAttempts) {
        loginAttempt.failureReason = 'Too many attempts'
        this.recordLoginAttempt(loginAttempt)
        throw new Error('Too many login attempts. Please try again later.')
      }

      // Verify password
      const hashedPassword = (user as any).hashedPassword
      const isValidPassword = await bcrypt.compare(password, hashedPassword)

      if (!isValidPassword) {
        loginAttempt.failureReason = 'Invalid password'
        this.recordLoginAttempt(loginAttempt)
        throw new Error('Invalid credentials')
      }

      // Generate tokens
      const tokens = this.generateTokens(user)

      // Update user metadata
      user.metadata.lastLoginAt = new Date()
      user.metadata.loginCount++
      user.metadata.updatedAt = new Date()

      // Record successful login
      loginAttempt.success = true
      this.recordLoginAttempt(loginAttempt)

      this.emit('user_authenticated', { user, ipAddress, userAgent })
      console.log(`👥 User authenticated: ${user.email}`)

      return { user, tokens }

    } catch (error) {
      this.recordLoginAttempt(loginAttempt)
      throw error
    }
  }

  /**
   * Generate JWT tokens
   */
  private generateTokens(user: User): AuthToken {
    const payload = {
      userId: user.id,
      email: user.email,
      organizationId: user.organizationId,
      roles: user.roles.map(r => r.id),
    }

    const accessToken = jwt.sign(payload, this.jwtSecret, {
      expiresIn: '1h',
      issuer: 'ap3x-platform',
      audience: 'ap3x-users',
    })

    const refreshToken = jwt.sign(
      { userId: user.id, type: 'refresh' },
      this.jwtSecret,
      { expiresIn: '7d' }
    )

    return {
      accessToken,
      refreshToken,
      expiresAt: new Date(Date.now() + 60 * 60 * 1000), // 1 hour
      tokenType: 'Bearer',
      scope: user.roles.flatMap(r => r.permissions.map(p => `${p.resource}:${p.action}`)),
    }
  }

  /**
   * Verify JWT token
   */
  async verifyToken(token: string): Promise<User> {
    try {
      const payload = jwt.verify(token, this.jwtSecret) as any
      const user = this.users.get(payload.userId)
      
      if (!user || user.status !== 'active') {
        throw new Error('Invalid token')
      }

      return user
    } catch (error) {
      throw new Error('Invalid token')
    }
  }

  /**
   * Check user permissions
   */
  hasPermission(user: User, resource: string, action: string): boolean {
    for (const role of user.roles) {
      for (const permission of role.permissions) {
        if (this.matchesPermission(permission, resource, action)) {
          return true
        }
      }
    }
    return false
  }

  /**
   * Match permission against resource and action
   */
  private matchesPermission(permission: Permission, resource: string, action: string): boolean {
    // Check wildcard permissions
    if (permission.resource === '*' && permission.action === '*') {
      return true
    }

    // Check resource match
    const resourceMatch = permission.resource === '*' || permission.resource === resource

    // Check action match
    const actions = permission.action.split(',').map(a => a.trim())
    const actionMatch = actions.includes('*') || actions.includes(action)

    return resourceMatch && actionMatch
  }

  /**
   * Create team
   */
  async createTeam(teamData: {
    name: string
    description?: string
    organizationId: string
    createdBy: string
  }): Promise<Team> {
    const team: Team = {
      id: uuidv4(),
      name: teamData.name,
      description: teamData.description,
      organizationId: teamData.organizationId,
      members: [
        {
          userId: teamData.createdBy,
          role: 'owner',
          joinedAt: new Date(),
          invitedBy: teamData.createdBy,
        },
      ],
      projects: [],
      settings: {
        visibility: 'private',
        allowMemberInvites: true,
        defaultProjectRole: 'developer',
      },
      metadata: {
        createdAt: new Date(),
        updatedAt: new Date(),
        createdBy: teamData.createdBy,
      },
    }

    this.teams.set(team.id, team)
    this.emit('team_created', { team })

    return team
  }

  /**
   * Add user to team
   */
  async addUserToTeam(teamId: string, userId: string, role: TeamMember['role'], invitedBy: string): Promise<void> {
    const team = this.teams.get(teamId)
    if (!team) {
      throw new Error('Team not found')
    }

    const user = this.users.get(userId)
    if (!user) {
      throw new Error('User not found')
    }

    // Check if user is already a member
    const existingMember = team.members.find(m => m.userId === userId)
    if (existingMember) {
      throw new Error('User is already a team member')
    }

    // Add member
    team.members.push({
      userId,
      role,
      joinedAt: new Date(),
      invitedBy,
    })

    // Add team to user
    if (!user.teams.includes(teamId)) {
      user.teams.push(teamId)
    }

    team.metadata.updatedAt = new Date()
    user.metadata.updatedAt = new Date()

    this.emit('user_added_to_team', { teamId, userId, role })
  }

  /**
   * Get default user preferences
   */
  private getDefaultPreferences(): UserPreferences {
    return {
      theme: 'dark',
      language: 'en',
      timezone: 'UTC',
      notifications: {
        email: true,
        push: true,
        desktop: true,
        frequency: 'immediate',
      },
      editor: {
        fontSize: 14,
        tabSize: 2,
        wordWrap: true,
        minimap: true,
        theme: 'vs-dark',
      },
    }
  }

  /**
   * Record login attempt
   */
  private recordLoginAttempt(attempt: LoginAttempt): void {
    if (!this.loginAttempts.has(attempt.email)) {
      this.loginAttempts.set(attempt.email, [])
    }
    this.loginAttempts.get(attempt.email)!.push(attempt)
  }

  /**
   * Get recent login attempts
   */
  private getRecentLoginAttempts(email: string): LoginAttempt[] {
    const attempts = this.loginAttempts.get(email) || []
    const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000)
    
    return attempts.filter(attempt => 
      attempt.timestamp > oneHourAgo && !attempt.success
    )
  }

  /**
   * Start cleanup intervals
   */
  private startCleanupIntervals(): void {
    // Clean up old login attempts every hour
    setInterval(() => {
      const oneDayAgo = new Date(Date.now() - 24 * 60 * 60 * 1000)
      
      for (const [email, attempts] of this.loginAttempts) {
        const recentAttempts = attempts.filter(attempt => attempt.timestamp > oneDayAgo)
        if (recentAttempts.length === 0) {
          this.loginAttempts.delete(email)
        } else {
          this.loginAttempts.set(email, recentAttempts)
        }
      }
    }, 60 * 60 * 1000)

    // Clean up expired sessions every 15 minutes
    setInterval(() => {
      const now = new Date()
      for (const [sessionId, session] of this.sessions) {
        if (session.expiresAt < now) {
          this.sessions.delete(sessionId)
        }
      }
    }, 15 * 60 * 1000)
  }

  /**
   * Get user by ID
   */
  getUser(userId: string): User | undefined {
    return this.users.get(userId)
  }

  /**
   * Get organization by ID
   */
  getOrganization(orgId: string): Organization | undefined {
    return this.organizations.get(orgId)
  }

  /**
   * Get team by ID
   */
  getTeam(teamId: string): Team | undefined {
    return this.teams.get(teamId)
  }

  /**
   * Get users by organization
   */
  getUsersByOrganization(orgId: string): User[] {
    return Array.from(this.users.values()).filter(user => user.organizationId === orgId)
  }

  /**
   * Get teams by organization
   */
  getTeamsByOrganization(orgId: string): Team[] {
    return Array.from(this.teams.values()).filter(team => team.organizationId === orgId)
  }

  /**
   * Shutdown user management
   */
  async shutdown(): Promise<void> {
    console.log('🔄 Shutting down User Management System...')

    // Clear all data
    this.users.clear()
    this.organizations.clear()
    this.teams.clear()
    this.roles.clear()
    this.sessions.clear()
    this.loginAttempts.clear()

    this.isInitialized = false
    this.removeAllListeners()
    console.log('✅ User Management System shutdown complete')
  }
}

// Export default instance
export const userManagement = new UserManagement(
  process.env.JWT_SECRET || 'ap3x-platform-secret-key-change-in-production'
)

export default UserManagement
