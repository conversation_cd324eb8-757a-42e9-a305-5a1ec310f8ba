/**
 * AP3X Platform - Main Entry Point
 * 
 * The most powerful agentic coding platform ever created.
 * Combines AIDER's proven code editing, AG3NT's multi-agent coordination,
 * and advanced context understanding into a unified platform.
 * 
 * Features:
 * - Multi-agent code editing workflows
 * - Real-time collaboration
 * - Visual workflow designer
 * - Advanced context understanding
 * - Enterprise-grade features
 * - bolt.new-style user experience
 */

import { ap3xBridge } from './core/ap3x-bridge'
import { ap3xGateway } from './api/ap3x-gateway'

export interface AP3XPlatformConfig {
  port?: number
  enableWebSockets?: boolean
  enableRealTimeCollaboration?: boolean
  enableVisualWorkflows?: boolean
  enableEnterpriseFeatures?: boolean
  corsOrigins?: string[]
}

/**
 * AP3X Platform - Main orchestrator
 */
export class AP3XPlatform {
  private config: AP3XPlatformConfig
  private isRunning = false

  constructor(config: AP3XPlatformConfig = {}) {
    this.config = {
      port: 3000,
      enableWebSockets: true,
      enableRealTimeCollaboration: true,
      enableVisualWorkflows: true,
      enableEnterpriseFeatures: true,
      corsOrigins: ['http://localhost:3001', 'http://localhost:3000'],
      ...config
    }
  }

  /**
   * Start the AP3X platform
   */
  async start(): Promise<void> {
    if (this.isRunning) {
      console.log('⚠️  AP3X Platform is already running')
      return
    }

    console.log('🚀 Starting AP3X Platform...')
    console.log('=' .repeat(80))
    console.log('🎯 The Most Powerful Agentic Coding Platform Ever Created')
    console.log('💼 Enterprise-Grade Multi-Agent Development Environment')
    console.log('🏆 Surpassing bolt.new, Cursor, and v0.dev Combined')
    console.log('=' .repeat(80))

    try {
      // Start the API gateway (which initializes the bridge)
      await ap3xGateway.start()

      this.isRunning = true
      
      console.log('✅ AP3X Platform started successfully!')
      console.log('')
      console.log('🌐 Platform URLs:')
      console.log(`   API Gateway: http://localhost:${this.config.port}`)
      console.log(`   Health Check: http://localhost:${this.config.port}/api/health`)
      console.log(`   WebSockets: ${this.config.enableWebSockets ? 'enabled' : 'disabled'}`)
      console.log('')
      console.log('🚀 Ready to revolutionize coding with AI agents!')

    } catch (error) {
      console.error('❌ Failed to start AP3X Platform:', error)
      throw error
    }
  }

  /**
   * Stop the AP3X platform
   */
  async stop(): Promise<void> {
    if (!this.isRunning) {
      console.log('⚠️  AP3X Platform is not running')
      return
    }

    console.log('🔄 Stopping AP3X Platform...')

    try {
      // Stop the API gateway
      await ap3xGateway.stop()

      // Shutdown the bridge
      await ap3xBridge.shutdown()

      this.isRunning = false
      console.log('✅ AP3X Platform stopped successfully')

    } catch (error) {
      console.error('❌ Error stopping AP3X Platform:', error)
      throw error
    }
  }

  /**
   * Get platform status
   */
  getStatus(): any {
    return {
      running: this.isRunning,
      config: this.config,
      uptime: process.uptime(),
      memory: process.memoryUsage(),
      version: '1.0.0'
    }
  }
}

// Export main classes and instances
export { AP3XBridge, ap3xBridge } from './core/ap3x-bridge'
export { AP3XGateway, ap3xGateway } from './api/ap3x-gateway'

// Create and export platform instance
export const ap3xPlatform = new AP3XPlatform()

// Quick start function
export async function startAP3X(config?: AP3XPlatformConfig): Promise<AP3XPlatform> {
  const platform = new AP3XPlatform(config)
  await platform.start()
  return platform
}

// Graceful shutdown handling
process.on('SIGINT', async () => {
  console.log('\n🔄 Received SIGINT, shutting down gracefully...')
  try {
    await ap3xPlatform.stop()
    process.exit(0)
  } catch (error) {
    console.error('Error during shutdown:', error)
    process.exit(1)
  }
})

process.on('SIGTERM', async () => {
  console.log('\n🔄 Received SIGTERM, shutting down gracefully...')
  try {
    await ap3xPlatform.stop()
    process.exit(0)
  } catch (error) {
    console.error('Error during shutdown:', error)
    process.exit(1)
  }
})

// Export default
export default AP3XPlatform

// If this file is run directly, start the platform
if (require.main === module) {
  startAP3X().catch((error) => {
    console.error('Failed to start AP3X Platform:', error)
    process.exit(1)
  })
}
