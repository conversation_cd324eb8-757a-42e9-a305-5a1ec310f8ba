/**
 * AP3X Platform - Advanced Context Engine Integration
 * 
 * Integrates the Neo4j-powered Context Engine with AIDER and AG3NT Framework
 * to provide deep codebase understanding and intelligent context retrieval.
 * 
 * Features:
 * - Neo4j graph database integration
 * - Real-time code indexing
 * - Semantic code search
 * - Dependency analysis
 * - Code relationship mapping
 * - Intelligent context enhancement
 */

import { EventEmitter } from 'events'
import axios, { AxiosInstance } from 'axios'
import { FileChange } from '../core/ap3x-bridge'

export interface ContextEngineConfig {
  apiUrl: string
  neo4jUrl?: string
  neo4jUser?: string
  neo4jPassword?: string
  enableRealTimeIndexing: boolean
  enableHybridRetrieval: boolean
  maxResults: number
  similarityThreshold: number
}

export interface ContextQuery {
  query: string
  files?: string[]
  includeRelated: boolean
  maxResults?: number
  filters?: ContextFilters
  enhanceWithAI?: boolean
}

export interface ContextFilters {
  fileTypes?: string[]
  directories?: string[]
  excludePatterns?: string[]
  dateRange?: {
    from: Date
    to: Date
  }
  authors?: string[]
  complexity?: 'low' | 'medium' | 'high'
}

export interface ContextResult {
  query: string
  results: ContextItem[]
  insights: ContextInsight[]
  relationships: CodeRelationship[]
  suggestions: string[]
  executionTime: number
  totalResults: number
}

export interface ContextItem {
  id: string
  type: 'file' | 'function' | 'class' | 'variable' | 'import' | 'comment'
  path: string
  name: string
  content: string
  startLine: number
  endLine: number
  relevanceScore: number
  metadata: {
    language: string
    complexity: number
    dependencies: string[]
    usages: number
    lastModified: Date
    author: string
  }
}

export interface ContextInsight {
  type: 'pattern' | 'antipattern' | 'dependency' | 'duplication' | 'complexity' | 'security'
  title: string
  description: string
  severity: 'low' | 'medium' | 'high' | 'critical'
  affectedFiles: string[]
  suggestions: string[]
  confidence: number
}

export interface CodeRelationship {
  id: string
  type: 'imports' | 'extends' | 'implements' | 'calls' | 'references' | 'modifies'
  source: {
    file: string
    name: string
    type: string
  }
  target: {
    file: string
    name: string
    type: string
  }
  strength: number
  metadata: any
}

export interface IndexingRequest {
  projectPath: string
  files?: string[]
  incremental?: boolean
  includeTests?: boolean
  includeNodeModules?: boolean
}

export interface IndexingResult {
  success: boolean
  filesIndexed: number
  nodesCreated: number
  relationshipsCreated: number
  executionTime: number
  errors: string[]
}

/**
 * Advanced Context Engine Integration
 */
export class ContextEngineIntegration extends EventEmitter {
  private config: ContextEngineConfig
  private apiClient: AxiosInstance
  private isInitialized = false
  private indexingQueue: IndexingRequest[] = []
  private isIndexing = false

  constructor(config: ContextEngineConfig) {
    super()
    this.config = config
    
    // Initialize API client
    this.apiClient = axios.create({
      baseURL: config.apiUrl,
      timeout: 30000,
      headers: {
        'Content-Type': 'application/json',
      },
    })

    this.setupInterceptors()
  }

  /**
   * Initialize Context Engine integration
   */
  async initialize(): Promise<void> {
    if (this.isInitialized) return

    console.log('🧠 Initializing Context Engine Integration...')

    try {
      // Test connection
      await this.testConnection()

      // Setup real-time indexing if enabled
      if (this.config.enableRealTimeIndexing) {
        await this.setupRealTimeIndexing()
      }

      this.isInitialized = true
      this.emit('initialized')
      console.log('✅ Context Engine Integration initialized successfully')

    } catch (error) {
      console.error('❌ Failed to initialize Context Engine Integration:', error)
      throw error
    }
  }

  /**
   * Setup API interceptors
   */
  private setupInterceptors(): void {
    // Request interceptor
    this.apiClient.interceptors.request.use(
      (config) => {
        console.log(`🧠 Context Engine Request: ${config.method?.toUpperCase()} ${config.url}`)
        return config
      },
      (error) => {
        console.error('🧠 Context Engine Request Error:', error)
        return Promise.reject(error)
      }
    )

    // Response interceptor
    this.apiClient.interceptors.response.use(
      (response) => {
        console.log(`🧠 Context Engine Response: ${response.status} ${response.config.url}`)
        return response
      },
      (error) => {
        console.error('🧠 Context Engine Response Error:', error)
        this.emit('api_error', error)
        return Promise.reject(error)
      }
    )
  }

  /**
   * Test connection to Context Engine
   */
  private async testConnection(): Promise<void> {
    try {
      const response = await this.apiClient.get('/health')
      if (response.data.status !== 'healthy') {
        throw new Error('Context Engine is not healthy')
      }
      console.log('✅ Context Engine connection test passed')
    } catch (error) {
      throw new Error(`Context Engine connection failed: ${error.message}`)
    }
  }

  /**
   * Setup real-time indexing
   */
  private async setupRealTimeIndexing(): Promise<void> {
    // Start indexing queue processor
    setInterval(() => {
      this.processIndexingQueue()
    }, 5000) // Process every 5 seconds

    console.log('✅ Real-time indexing setup complete')
  }

  /**
   * Query context with advanced search
   */
  async queryContext(query: ContextQuery): Promise<ContextResult> {
    if (!this.isInitialized) {
      throw new Error('Context Engine not initialized')
    }

    const startTime = Date.now()
    console.log(`🧠 Querying context: "${query.query}"`)

    try {
      const response = await this.apiClient.post('/context/query', {
        query: query.query,
        files: query.files,
        includeRelated: query.includeRelated,
        maxResults: query.maxResults || this.config.maxResults,
        filters: query.filters,
        enhanceWithAI: query.enhanceWithAI || false,
        similarityThreshold: this.config.similarityThreshold,
      })

      const result: ContextResult = {
        ...response.data,
        executionTime: Date.now() - startTime,
      }

      this.emit('context_queried', { query, result })
      return result

    } catch (error) {
      console.error('❌ Context query failed:', error)
      this.emit('query_failed', { query, error })
      throw error
    }
  }

  /**
   * Index project files
   */
  async indexProject(request: IndexingRequest): Promise<IndexingResult> {
    if (!this.isInitialized) {
      throw new Error('Context Engine not initialized')
    }

    console.log(`🧠 Indexing project: ${request.projectPath}`)

    try {
      const response = await this.apiClient.post('/context/index', request)
      const result: IndexingResult = response.data

      this.emit('project_indexed', { request, result })
      return result

    } catch (error) {
      console.error('❌ Project indexing failed:', error)
      this.emit('indexing_failed', { request, error })
      throw error
    }
  }

  /**
   * Index files incrementally
   */
  async indexFiles(files: string[], projectPath: string): Promise<IndexingResult> {
    const request: IndexingRequest = {
      projectPath,
      files,
      incremental: true,
    }

    if (this.config.enableRealTimeIndexing) {
      // Add to queue for batch processing
      this.indexingQueue.push(request)
      return {
        success: true,
        filesIndexed: files.length,
        nodesCreated: 0,
        relationshipsCreated: 0,
        executionTime: 0,
        errors: [],
      }
    } else {
      return this.indexProject(request)
    }
  }

  /**
   * Process indexing queue
   */
  private async processIndexingQueue(): Promise<void> {
    if (this.isIndexing || this.indexingQueue.length === 0) return

    this.isIndexing = true

    try {
      // Batch requests by project
      const batches = new Map<string, string[]>()
      
      while (this.indexingQueue.length > 0) {
        const request = this.indexingQueue.shift()!
        if (!batches.has(request.projectPath)) {
          batches.set(request.projectPath, [])
        }
        batches.get(request.projectPath)!.push(...(request.files || []))
      }

      // Process each batch
      for (const [projectPath, files] of batches) {
        if (files.length > 0) {
          await this.indexProject({
            projectPath,
            files: [...new Set(files)], // Remove duplicates
            incremental: true,
          })
        }
      }

    } catch (error) {
      console.error('❌ Indexing queue processing failed:', error)
    } finally {
      this.isIndexing = false
    }
  }

  /**
   * Enhance context for code editing
   */
  async enhanceContextForEditing(
    prompt: string,
    files: string[],
    projectPath: string
  ): Promise<{
    enhancedPrompt: string
    relevantContext: ContextItem[]
    insights: ContextInsight[]
    suggestions: string[]
  }> {
    console.log(`🧠 Enhancing context for editing: ${files.length} files`)

    try {
      // Query relevant context
      const contextResult = await this.queryContext({
        query: prompt,
        files,
        includeRelated: true,
        maxResults: 20,
        enhanceWithAI: true,
      })

      // Analyze code patterns and dependencies
      const patterns = await this.analyzeCodePatterns(files, projectPath)
      
      // Generate enhanced prompt
      const enhancedPrompt = await this.generateEnhancedPrompt(
        prompt,
        contextResult.results,
        patterns
      )

      return {
        enhancedPrompt,
        relevantContext: contextResult.results,
        insights: contextResult.insights,
        suggestions: contextResult.suggestions,
      }

    } catch (error) {
      console.error('❌ Context enhancement failed:', error)
      // Return original prompt if enhancement fails
      return {
        enhancedPrompt: prompt,
        relevantContext: [],
        insights: [],
        suggestions: [],
      }
    }
  }

  /**
   * Analyze code patterns
   */
  private async analyzeCodePatterns(
    files: string[],
    projectPath: string
  ): Promise<{
    patterns: string[]
    antipatterns: string[]
    dependencies: CodeRelationship[]
    complexity: number
  }> {
    try {
      const response = await this.apiClient.post('/context/analyze', {
        files,
        projectPath,
        analysisTypes: ['patterns', 'dependencies', 'complexity'],
      })

      return response.data
    } catch (error) {
      console.warn('⚠️ Pattern analysis failed:', error)
      return {
        patterns: [],
        antipatterns: [],
        dependencies: [],
        complexity: 0,
      }
    }
  }

  /**
   * Generate enhanced prompt
   */
  private async generateEnhancedPrompt(
    originalPrompt: string,
    context: ContextItem[],
    patterns: any
  ): Promise<string> {
    try {
      const response = await this.apiClient.post('/context/enhance-prompt', {
        prompt: originalPrompt,
        context: context.slice(0, 10), // Limit context size
        patterns,
      })

      return response.data.enhancedPrompt
    } catch (error) {
      console.warn('⚠️ Prompt enhancement failed:', error)
      return originalPrompt
    }
  }

  /**
   * Track file changes for real-time indexing
   */
  async trackFileChanges(changes: FileChange[], projectPath: string): Promise<void> {
    if (!this.config.enableRealTimeIndexing) return

    const filesToIndex = changes
      .filter(change => change.action !== 'delete')
      .map(change => change.path)

    if (filesToIndex.length > 0) {
      await this.indexFiles(filesToIndex, projectPath)
    }

    // Handle deletions
    const deletedFiles = changes
      .filter(change => change.action === 'delete')
      .map(change => change.path)

    if (deletedFiles.length > 0) {
      await this.removeFromIndex(deletedFiles, projectPath)
    }

    this.emit('file_changes_tracked', { changes, projectPath })
  }

  /**
   * Remove files from index
   */
  private async removeFromIndex(files: string[], projectPath: string): Promise<void> {
    try {
      await this.apiClient.post('/context/remove', {
        files,
        projectPath,
      })
      console.log(`🧠 Removed ${files.length} files from index`)
    } catch (error) {
      console.error('❌ Failed to remove files from index:', error)
    }
  }

  /**
   * Get project statistics
   */
  async getProjectStats(projectPath: string): Promise<{
    totalFiles: number
    totalNodes: number
    totalRelationships: number
    languages: { [key: string]: number }
    complexity: {
      average: number
      distribution: { [key: string]: number }
    }
    lastIndexed: Date
  }> {
    try {
      const response = await this.apiClient.get(`/context/stats/${encodeURIComponent(projectPath)}`)
      return response.data
    } catch (error) {
      console.error('❌ Failed to get project stats:', error)
      throw error
    }
  }

  /**
   * Search similar code
   */
  async searchSimilarCode(
    codeSnippet: string,
    projectPath: string,
    options?: {
      language?: string
      minSimilarity?: number
      maxResults?: number
    }
  ): Promise<ContextItem[]> {
    try {
      const response = await this.apiClient.post('/context/similar', {
        code: codeSnippet,
        projectPath,
        language: options?.language,
        minSimilarity: options?.minSimilarity || this.config.similarityThreshold,
        maxResults: options?.maxResults || this.config.maxResults,
      })

      return response.data.results
    } catch (error) {
      console.error('❌ Similar code search failed:', error)
      return []
    }
  }

  /**
   * Get code dependencies
   */
  async getCodeDependencies(
    filePath: string,
    projectPath: string
  ): Promise<{
    dependencies: CodeRelationship[]
    dependents: CodeRelationship[]
    circularDependencies: string[][]
  }> {
    try {
      const response = await this.apiClient.get('/context/dependencies', {
        params: {
          file: filePath,
          project: projectPath,
        },
      })

      return response.data
    } catch (error) {
      console.error('❌ Failed to get code dependencies:', error)
      return {
        dependencies: [],
        dependents: [],
        circularDependencies: [],
      }
    }
  }

  /**
   * Clear project index
   */
  async clearProjectIndex(projectPath: string): Promise<void> {
    try {
      await this.apiClient.delete(`/context/index/${encodeURIComponent(projectPath)}`)
      console.log(`🧠 Cleared index for project: ${projectPath}`)
      this.emit('index_cleared', { projectPath })
    } catch (error) {
      console.error('❌ Failed to clear project index:', error)
      throw error
    }
  }

  /**
   * Get health status
   */
  async getHealthStatus(): Promise<{
    status: 'healthy' | 'degraded' | 'unhealthy'
    neo4j: boolean
    api: boolean
    indexing: boolean
    lastCheck: Date
  }> {
    try {
      const response = await this.apiClient.get('/health')
      return response.data
    } catch (error) {
      return {
        status: 'unhealthy',
        neo4j: false,
        api: false,
        indexing: false,
        lastCheck: new Date(),
      }
    }
  }

  /**
   * Shutdown Context Engine integration
   */
  async shutdown(): Promise<void> {
    console.log('🔄 Shutting down Context Engine Integration...')

    // Process remaining indexing queue
    if (this.indexingQueue.length > 0) {
      console.log(`🧠 Processing ${this.indexingQueue.length} remaining indexing requests...`)
      await this.processIndexingQueue()
    }

    this.isInitialized = false
    this.removeAllListeners()
    console.log('✅ Context Engine Integration shutdown complete')
  }
}

// Export default instance
export const contextEngineIntegration = new ContextEngineIntegration({
  apiUrl: process.env.CONTEXT_ENGINE_URL || 'http://localhost:3001',
  neo4jUrl: process.env.NEO4J_URL || 'bolt://localhost:7687',
  neo4jUser: process.env.NEO4J_USER || 'neo4j',
  neo4jPassword: process.env.NEO4J_PASSWORD || 'password',
  enableRealTimeIndexing: true,
  enableHybridRetrieval: true,
  maxResults: 50,
  similarityThreshold: 0.7,
})

export default ContextEngineIntegration
