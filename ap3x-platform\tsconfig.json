{"compilerOptions": {"target": "ES2020", "module": "commonjs", "lib": ["ES2020"], "outDir": "./dist", "rootDir": ".", "strict": false, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "declaration": true, "declarationMap": true, "sourceMap": true, "removeComments": false, "noImplicitAny": false, "noImplicitReturns": false, "noImplicitThis": false, "noUnusedLocals": false, "noUnusedParameters": false, "exactOptionalPropertyTypes": false, "noImplicitOverride": false, "noPropertyAccessFromIndexSignature": false, "noUncheckedIndexedAccess": false, "moduleResolution": "node", "baseUrl": ".", "paths": {"@/*": ["src/*"], "@/core/*": ["src/core/*"], "@/api/*": ["src/api/*"], "@/types/*": ["src/types/*"]}, "resolveJsonModule": true, "allowSyntheticDefaultImports": true, "experimentalDecorators": true, "emitDecoratorMetadata": true}, "include": ["src/**/*", "start-ap3x.ts"], "exclude": ["node_modules", "dist", "**/*.test.ts", "**/*.spec.ts", "frontend/**/*"]}