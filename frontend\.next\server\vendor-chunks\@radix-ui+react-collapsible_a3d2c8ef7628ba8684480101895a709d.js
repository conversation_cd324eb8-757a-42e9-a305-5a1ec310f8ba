"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@radix-ui+react-collapsible_a3d2c8ef7628ba8684480101895a709d";
exports.ids = ["vendor-chunks/@radix-ui+react-collapsible_a3d2c8ef7628ba8684480101895a709d"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@radix-ui+react-collapsible_a3d2c8ef7628ba8684480101895a709d/node_modules/@radix-ui/react-collapsible/dist/index.mjs":
/*!*************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@radix-ui+react-collapsible_a3d2c8ef7628ba8684480101895a709d/node_modules/@radix-ui/react-collapsible/dist/index.mjs ***!
  \*************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Collapsible: () => (/* binding */ Collapsible),\n/* harmony export */   CollapsibleContent: () => (/* binding */ CollapsibleContent),\n/* harmony export */   CollapsibleTrigger: () => (/* binding */ CollapsibleTrigger),\n/* harmony export */   Content: () => (/* binding */ Content),\n/* harmony export */   Root: () => (/* binding */ Root),\n/* harmony export */   Trigger: () => (/* binding */ Trigger),\n/* harmony export */   createCollapsibleScope: () => (/* binding */ createCollapsibleScope)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_primitive__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @radix-ui/primitive */ \"(ssr)/./node_modules/.pnpm/@radix-ui+primitive@1.1.1/node_modules/@radix-ui/primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_context__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-context */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-context@1.1_4fe40d510edca7ae4ca9c92afeb1ae6d/node_modules/@radix-ui/react-context/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_controllable_state__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-use-controllable-state */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-use-control_84b05e8d8acde331bf79519153011e46/node_modules/@radix-ui/react-use-controllable-state/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @radix-ui/react-use-layout-effect */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-use-layout-_7d9c308966eafc0f645092b629b133a3/node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @radix-ui/react-compose-refs */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-compose-ref_2a0e526a8f7e7aada080206d385bb572/node_modules/@radix-ui/react-compose-refs/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @radix-ui/react-primitive */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-primitive@2_997b35f2e2aa9d3174fc03a0f79e437b/node_modules/@radix-ui/react-primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @radix-ui/react-presence */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-presence@1._d340d9d39508cb250c25fc66451df4dd/node_modules/@radix-ui/react-presence/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_id__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-id */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-id@1.1.0_@types+react@19.1.8_react@19.1.0/node_modules/@radix-ui/react-id/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ Collapsible,CollapsibleContent,CollapsibleTrigger,Content,Root,Trigger,createCollapsibleScope auto */ // packages/react/collapsible/src/Collapsible.tsx\n\n\n\n\n\n\n\n\n\n\nvar COLLAPSIBLE_NAME = \"Collapsible\";\nvar [createCollapsibleContext, createCollapsibleScope] = (0,_radix_ui_react_context__WEBPACK_IMPORTED_MODULE_2__.createContextScope)(COLLAPSIBLE_NAME);\nvar [CollapsibleProvider, useCollapsibleContext] = createCollapsibleContext(COLLAPSIBLE_NAME);\nvar Collapsible = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeCollapsible, open: openProp, defaultOpen, disabled, onOpenChange, ...collapsibleProps } = props;\n    const [open = false, setOpen] = (0,_radix_ui_react_use_controllable_state__WEBPACK_IMPORTED_MODULE_3__.useControllableState)({\n        prop: openProp,\n        defaultProp: defaultOpen,\n        onChange: onOpenChange\n    });\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(CollapsibleProvider, {\n        scope: __scopeCollapsible,\n        disabled,\n        contentId: (0,_radix_ui_react_id__WEBPACK_IMPORTED_MODULE_4__.useId)(),\n        open,\n        onOpenToggle: react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n            \"Collapsible.useCallback\": ()=>setOpen({\n                    \"Collapsible.useCallback\": (prevOpen)=>!prevOpen\n                }[\"Collapsible.useCallback\"])\n        }[\"Collapsible.useCallback\"], [\n            setOpen\n        ]),\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_5__.Primitive.div, {\n            \"data-state\": getState(open),\n            \"data-disabled\": disabled ? \"\" : void 0,\n            ...collapsibleProps,\n            ref: forwardedRef\n        })\n    });\n});\nCollapsible.displayName = COLLAPSIBLE_NAME;\nvar TRIGGER_NAME = \"CollapsibleTrigger\";\nvar CollapsibleTrigger = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeCollapsible, ...triggerProps } = props;\n    const context = useCollapsibleContext(TRIGGER_NAME, __scopeCollapsible);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_5__.Primitive.button, {\n        type: \"button\",\n        \"aria-controls\": context.contentId,\n        \"aria-expanded\": context.open || false,\n        \"data-state\": getState(context.open),\n        \"data-disabled\": context.disabled ? \"\" : void 0,\n        disabled: context.disabled,\n        ...triggerProps,\n        ref: forwardedRef,\n        onClick: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_6__.composeEventHandlers)(props.onClick, context.onOpenToggle)\n    });\n});\nCollapsibleTrigger.displayName = TRIGGER_NAME;\nvar CONTENT_NAME = \"CollapsibleContent\";\nvar CollapsibleContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { forceMount, ...contentProps } = props;\n    const context = useCollapsibleContext(CONTENT_NAME, props.__scopeCollapsible);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_7__.Presence, {\n        present: forceMount || context.open,\n        children: ({ present })=>/* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(CollapsibleContentImpl, {\n                ...contentProps,\n                ref: forwardedRef,\n                present\n            })\n    });\n});\nCollapsibleContent.displayName = CONTENT_NAME;\nvar CollapsibleContentImpl = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeCollapsible, present, children, ...contentProps } = props;\n    const context = useCollapsibleContext(CONTENT_NAME, __scopeCollapsible);\n    const [isPresent, setIsPresent] = react__WEBPACK_IMPORTED_MODULE_0__.useState(present);\n    const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_8__.useComposedRefs)(forwardedRef, ref);\n    const heightRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(0);\n    const height = heightRef.current;\n    const widthRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(0);\n    const width = widthRef.current;\n    const isOpen = context.open || isPresent;\n    const isMountAnimationPreventedRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(isOpen);\n    const originalStylesRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(void 0);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"CollapsibleContentImpl.useEffect\": ()=>{\n            const rAF = requestAnimationFrame({\n                \"CollapsibleContentImpl.useEffect.rAF\": ()=>isMountAnimationPreventedRef.current = false\n            }[\"CollapsibleContentImpl.useEffect.rAF\"]);\n            return ({\n                \"CollapsibleContentImpl.useEffect\": ()=>cancelAnimationFrame(rAF)\n            })[\"CollapsibleContentImpl.useEffect\"];\n        }\n    }[\"CollapsibleContentImpl.useEffect\"], []);\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_9__.useLayoutEffect)({\n        \"CollapsibleContentImpl.useLayoutEffect\": ()=>{\n            const node = ref.current;\n            if (node) {\n                originalStylesRef.current = originalStylesRef.current || {\n                    transitionDuration: node.style.transitionDuration,\n                    animationName: node.style.animationName\n                };\n                node.style.transitionDuration = \"0s\";\n                node.style.animationName = \"none\";\n                const rect = node.getBoundingClientRect();\n                heightRef.current = rect.height;\n                widthRef.current = rect.width;\n                if (!isMountAnimationPreventedRef.current) {\n                    node.style.transitionDuration = originalStylesRef.current.transitionDuration;\n                    node.style.animationName = originalStylesRef.current.animationName;\n                }\n                setIsPresent(present);\n            }\n        }\n    }[\"CollapsibleContentImpl.useLayoutEffect\"], [\n        context.open,\n        present\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_5__.Primitive.div, {\n        \"data-state\": getState(context.open),\n        \"data-disabled\": context.disabled ? \"\" : void 0,\n        id: context.contentId,\n        hidden: !isOpen,\n        ...contentProps,\n        ref: composedRefs,\n        style: {\n            [`--radix-collapsible-content-height`]: height ? `${height}px` : void 0,\n            [`--radix-collapsible-content-width`]: width ? `${width}px` : void 0,\n            ...props.style\n        },\n        children: isOpen && children\n    });\n});\nfunction getState(open) {\n    return open ? \"open\" : \"closed\";\n}\nvar Root = Collapsible;\nvar Trigger = CollapsibleTrigger;\nvar Content = CollapsibleContent;\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vQHJhZGl4LXVpK3JlYWN0LWNvbGxhcHNpYmxlX2EzZDJjOGVmNzYyOGJhODY4NDQ4MDEwMTg5NWE3MDlkL25vZGVfbW9kdWxlcy9AcmFkaXgtdWkvcmVhY3QtY29sbGFwc2libGUvZGlzdC9pbmRleC5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQXVCO0FBQ2M7QUFDRjtBQUNFO0FBQ0w7QUFDQTtBQUNOO0FBQ0Q7QUFDSDtBQXlEZDtBQWpEUixJQUFNLG1CQUFtQjtBQUd6QixJQUFNLENBQUMsMEJBQTBCLHNCQUFzQixJQUFJLDJFQUFrQixDQUFDLGdCQUFnQjtBQVM5RixJQUFNLENBQUMscUJBQXFCLHFCQUFxQixJQUMvQyx5QkFBa0QsZ0JBQWdCO0FBV3BFLElBQU0sNEJBQW9CLDhDQUN4QixDQUFDLE9BQXNDO0lBQ3JDLE1BQU0sRUFDSixvQkFDQSxNQUFNLFVBQ04sYUFDQSxVQUNBLGNBQ0EsR0FBRyxrQkFDTCxHQUFJO0lBRUosTUFBTSxDQUFDLE9BQU8sT0FBTyxPQUFPLElBQUksNEZBQW9CLENBQUM7UUFDbkQsTUFBTTtRQUNOLGFBQWE7UUFDYixVQUFVO0lBQ1osQ0FBQztJQUVELE9BQ0UsdUVBQUM7UUFDQyxPQUFPO1FBQ1A7UUFDQSxXQUFXLHlEQUFLLENBQUM7UUFDakI7UUFDQSxjQUFvQjt1Q0FBWSxJQUFNOytDQUFRLENBQUMsV0FBYSxDQUFDLFFBQVE7O3NDQUFHO1lBQUMsT0FBTztTQUFDO1FBRWpGLGlGQUFDLGdFQUFTLENBQUMsS0FBVjtZQUNDLGNBQVksU0FBUyxJQUFJO1lBQ3pCLGlCQUFlLFdBQVcsS0FBSztZQUM5QixHQUFHO1lBQ0osS0FBSztRQUFBO0lBQ1A7QUFHTjtBQUdGLFlBQVksY0FBYztBQU0xQixJQUFNLGVBQWU7QUFNckIsSUFBTSxtQ0FBMkIsOENBQy9CLENBQUMsT0FBNkM7SUFDNUMsTUFBTSxFQUFFLG9CQUFvQixHQUFHLGFBQWEsSUFBSTtJQUNoRCxNQUFNLFVBQVUsc0JBQXNCLGNBQWMsa0JBQWtCO0lBQ3RFLE9BQ0UsdUVBQUMsZ0VBQVMsQ0FBQyxRQUFWO1FBQ0MsTUFBSztRQUNMLGlCQUFlLFFBQVE7UUFDdkIsaUJBQWUsUUFBUSxRQUFRO1FBQy9CLGNBQVksU0FBUyxRQUFRLElBQUk7UUFDakMsaUJBQWUsUUFBUSxXQUFXLEtBQUs7UUFDdkMsVUFBVSxRQUFRO1FBQ2pCLEdBQUc7UUFDSixLQUFLO1FBQ0wsU0FBUyx5RUFBb0IsQ0FBQyxNQUFNLFNBQVMsUUFBUSxZQUFZO0lBQUE7QUFHdkU7QUFHRixtQkFBbUIsY0FBYztBQU1qQyxJQUFNLGVBQWU7QUFXckIsSUFBTSxtQ0FBMkIsOENBQy9CLENBQUMsT0FBNkM7SUFDNUMsTUFBTSxFQUFFLFlBQVksR0FBRyxhQUFhLElBQUk7SUFDeEMsTUFBTSxVQUFVLHNCQUFzQixjQUFjLE1BQU0sa0JBQWtCO0lBQzVFLE9BQ0UsdUVBQUMsOERBQVEsRUFBUjtRQUFTLFNBQVMsY0FBYyxRQUFRO1FBQ3RDLFdBQUMsRUFBRSxRQUFRLElBQ1YsdUVBQUM7Z0JBQXdCLEdBQUc7Z0JBQWMsS0FBSztnQkFBYztZQUFBLENBQWtCO0lBQUEsQ0FFbkY7QUFFSjtBQUdGLG1CQUFtQixjQUFjO0FBU2pDLElBQU0sdUNBQStCLDhDQUduQyxDQUFDLE9BQWlEO0lBQ2xELE1BQU0sRUFBRSxvQkFBb0IsU0FBUyxVQUFVLEdBQUcsYUFBYSxJQUFJO0lBQ25FLE1BQU0sVUFBVSxzQkFBc0IsY0FBYyxrQkFBa0I7SUFDdEUsTUFBTSxDQUFDLFdBQVcsWUFBWSxJQUFVLDRDQUFTLE9BQU87SUFDeEQsTUFBTSxNQUFZLDBDQUFzQyxJQUFJO0lBQzVELE1BQU0sZUFBZSw2RUFBZSxDQUFDLGNBQWMsR0FBRztJQUN0RCxNQUFNLFlBQWtCLDBDQUEyQixDQUFDO0lBQ3BELE1BQU0sU0FBUyxVQUFVO0lBQ3pCLE1BQU0sV0FBaUIsMENBQTJCLENBQUM7SUFDbkQsTUFBTSxRQUFRLFNBQVM7SUFHdkIsTUFBTSxTQUFTLFFBQVEsUUFBUTtJQUMvQixNQUFNLCtCQUFxQywwQ0FBTyxNQUFNO0lBQ3hELE1BQU0sb0JBQTBCLDBDQUErQixNQUFTO0lBRWxFOzRDQUFVO1lBQ2QsTUFBTSxNQUFNO3dEQUFzQixJQUFPLDZCQUE2QixVQUFVLEtBQU07O1lBQ3RGO29EQUFPLElBQU0scUJBQXFCLEdBQUc7O1FBQ3ZDOzJDQUFHLENBQUMsQ0FBQztJQUVMLGtGQUFlO2tEQUFDO1lBQ2QsTUFBTSxPQUFPLElBQUk7WUFDakIsSUFBSSxNQUFNO2dCQUNSLGtCQUFrQixVQUFVLGtCQUFrQixXQUFXO29CQUN2RCxvQkFBb0IsS0FBSyxNQUFNO29CQUMvQixlQUFlLEtBQUssTUFBTTtnQkFDNUI7Z0JBRUEsS0FBSyxNQUFNLHFCQUFxQjtnQkFDaEMsS0FBSyxNQUFNLGdCQUFnQjtnQkFHM0IsTUFBTSxPQUFPLEtBQUssc0JBQXNCO2dCQUN4QyxVQUFVLFVBQVUsS0FBSztnQkFDekIsU0FBUyxVQUFVLEtBQUs7Z0JBR3hCLElBQUksQ0FBQyw2QkFBNkIsU0FBUztvQkFDekMsS0FBSyxNQUFNLHFCQUFxQixrQkFBa0IsUUFBUTtvQkFDMUQsS0FBSyxNQUFNLGdCQUFnQixrQkFBa0IsUUFBUTtnQkFDdkQ7Z0JBRUEsYUFBYSxPQUFPO1lBQ3RCO1FBT0Y7aURBQUc7UUFBQyxRQUFRO1FBQU0sT0FBTztLQUFDO0lBRTFCLE9BQ0UsdUVBQUMsZ0VBQVMsQ0FBQyxLQUFWO1FBQ0MsY0FBWSxTQUFTLFFBQVEsSUFBSTtRQUNqQyxpQkFBZSxRQUFRLFdBQVcsS0FBSztRQUN2QyxJQUFJLFFBQVE7UUFDWixRQUFRLENBQUM7UUFDUixHQUFHO1FBQ0osS0FBSztRQUNMLE9BQU87WUFDTCxDQUFDLG9DQUEyQyxHQUFHLFNBQVMsR0FBRyxNQUFNLE9BQU87WUFDeEUsQ0FBQyxtQ0FBMEMsR0FBRyxRQUFRLEdBQUcsS0FBSyxPQUFPO1lBQ3JFLEdBQUcsTUFBTTtRQUNYO1FBRUMsb0JBQVU7SUFBQTtBQUdqQixDQUFDO0FBSUQsU0FBUyxTQUFTLE1BQWdCO0lBQ2hDLE9BQU8sT0FBTyxTQUFTO0FBQ3pCO0FBRUEsSUFBTSxPQUFPO0FBQ2IsSUFBTSxVQUFVO0FBQ2hCLElBQU0sVUFBVSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxHdWVyclxcRG93bmxvYWRzXFxzcmNcXENvbGxhcHNpYmxlLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgKiBhcyBSZWFjdCBmcm9tICdyZWFjdCc7XG5pbXBvcnQgeyBjb21wb3NlRXZlbnRIYW5kbGVycyB9IGZyb20gJ0ByYWRpeC11aS9wcmltaXRpdmUnO1xuaW1wb3J0IHsgY3JlYXRlQ29udGV4dFNjb3BlIH0gZnJvbSAnQHJhZGl4LXVpL3JlYWN0LWNvbnRleHQnO1xuaW1wb3J0IHsgdXNlQ29udHJvbGxhYmxlU3RhdGUgfSBmcm9tICdAcmFkaXgtdWkvcmVhY3QtdXNlLWNvbnRyb2xsYWJsZS1zdGF0ZSc7XG5pbXBvcnQgeyB1c2VMYXlvdXRFZmZlY3QgfSBmcm9tICdAcmFkaXgtdWkvcmVhY3QtdXNlLWxheW91dC1lZmZlY3QnO1xuaW1wb3J0IHsgdXNlQ29tcG9zZWRSZWZzIH0gZnJvbSAnQHJhZGl4LXVpL3JlYWN0LWNvbXBvc2UtcmVmcyc7XG5pbXBvcnQgeyBQcmltaXRpdmUgfSBmcm9tICdAcmFkaXgtdWkvcmVhY3QtcHJpbWl0aXZlJztcbmltcG9ydCB7IFByZXNlbmNlIH0gZnJvbSAnQHJhZGl4LXVpL3JlYWN0LXByZXNlbmNlJztcbmltcG9ydCB7IHVzZUlkIH0gZnJvbSAnQHJhZGl4LXVpL3JlYWN0LWlkJztcblxuaW1wb3J0IHR5cGUgeyBTY29wZSB9IGZyb20gJ0ByYWRpeC11aS9yZWFjdC1jb250ZXh0JztcblxuLyogLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLVxuICogQ29sbGFwc2libGVcbiAqIC0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tKi9cblxuY29uc3QgQ09MTEFQU0lCTEVfTkFNRSA9ICdDb2xsYXBzaWJsZSc7XG5cbnR5cGUgU2NvcGVkUHJvcHM8UD4gPSBQICYgeyBfX3Njb3BlQ29sbGFwc2libGU/OiBTY29wZSB9O1xuY29uc3QgW2NyZWF0ZUNvbGxhcHNpYmxlQ29udGV4dCwgY3JlYXRlQ29sbGFwc2libGVTY29wZV0gPSBjcmVhdGVDb250ZXh0U2NvcGUoQ09MTEFQU0lCTEVfTkFNRSk7XG5cbnR5cGUgQ29sbGFwc2libGVDb250ZXh0VmFsdWUgPSB7XG4gIGNvbnRlbnRJZDogc3RyaW5nO1xuICBkaXNhYmxlZD86IGJvb2xlYW47XG4gIG9wZW46IGJvb2xlYW47XG4gIG9uT3BlblRvZ2dsZSgpOiB2b2lkO1xufTtcblxuY29uc3QgW0NvbGxhcHNpYmxlUHJvdmlkZXIsIHVzZUNvbGxhcHNpYmxlQ29udGV4dF0gPVxuICBjcmVhdGVDb2xsYXBzaWJsZUNvbnRleHQ8Q29sbGFwc2libGVDb250ZXh0VmFsdWU+KENPTExBUFNJQkxFX05BTUUpO1xuXG50eXBlIENvbGxhcHNpYmxlRWxlbWVudCA9IFJlYWN0LkVsZW1lbnRSZWY8dHlwZW9mIFByaW1pdGl2ZS5kaXY+O1xudHlwZSBQcmltaXRpdmVEaXZQcm9wcyA9IFJlYWN0LkNvbXBvbmVudFByb3BzV2l0aG91dFJlZjx0eXBlb2YgUHJpbWl0aXZlLmRpdj47XG5pbnRlcmZhY2UgQ29sbGFwc2libGVQcm9wcyBleHRlbmRzIFByaW1pdGl2ZURpdlByb3BzIHtcbiAgZGVmYXVsdE9wZW4/OiBib29sZWFuO1xuICBvcGVuPzogYm9vbGVhbjtcbiAgZGlzYWJsZWQ/OiBib29sZWFuO1xuICBvbk9wZW5DaGFuZ2U/KG9wZW46IGJvb2xlYW4pOiB2b2lkO1xufVxuXG5jb25zdCBDb2xsYXBzaWJsZSA9IFJlYWN0LmZvcndhcmRSZWY8Q29sbGFwc2libGVFbGVtZW50LCBDb2xsYXBzaWJsZVByb3BzPihcbiAgKHByb3BzOiBTY29wZWRQcm9wczxDb2xsYXBzaWJsZVByb3BzPiwgZm9yd2FyZGVkUmVmKSA9PiB7XG4gICAgY29uc3Qge1xuICAgICAgX19zY29wZUNvbGxhcHNpYmxlLFxuICAgICAgb3Blbjogb3BlblByb3AsXG4gICAgICBkZWZhdWx0T3BlbixcbiAgICAgIGRpc2FibGVkLFxuICAgICAgb25PcGVuQ2hhbmdlLFxuICAgICAgLi4uY29sbGFwc2libGVQcm9wc1xuICAgIH0gPSBwcm9wcztcblxuICAgIGNvbnN0IFtvcGVuID0gZmFsc2UsIHNldE9wZW5dID0gdXNlQ29udHJvbGxhYmxlU3RhdGUoe1xuICAgICAgcHJvcDogb3BlblByb3AsXG4gICAgICBkZWZhdWx0UHJvcDogZGVmYXVsdE9wZW4sXG4gICAgICBvbkNoYW5nZTogb25PcGVuQ2hhbmdlLFxuICAgIH0pO1xuXG4gICAgcmV0dXJuIChcbiAgICAgIDxDb2xsYXBzaWJsZVByb3ZpZGVyXG4gICAgICAgIHNjb3BlPXtfX3Njb3BlQ29sbGFwc2libGV9XG4gICAgICAgIGRpc2FibGVkPXtkaXNhYmxlZH1cbiAgICAgICAgY29udGVudElkPXt1c2VJZCgpfVxuICAgICAgICBvcGVuPXtvcGVufVxuICAgICAgICBvbk9wZW5Ub2dnbGU9e1JlYWN0LnVzZUNhbGxiYWNrKCgpID0+IHNldE9wZW4oKHByZXZPcGVuKSA9PiAhcHJldk9wZW4pLCBbc2V0T3Blbl0pfVxuICAgICAgPlxuICAgICAgICA8UHJpbWl0aXZlLmRpdlxuICAgICAgICAgIGRhdGEtc3RhdGU9e2dldFN0YXRlKG9wZW4pfVxuICAgICAgICAgIGRhdGEtZGlzYWJsZWQ9e2Rpc2FibGVkID8gJycgOiB1bmRlZmluZWR9XG4gICAgICAgICAgey4uLmNvbGxhcHNpYmxlUHJvcHN9XG4gICAgICAgICAgcmVmPXtmb3J3YXJkZWRSZWZ9XG4gICAgICAgIC8+XG4gICAgICA8L0NvbGxhcHNpYmxlUHJvdmlkZXI+XG4gICAgKTtcbiAgfVxuKTtcblxuQ29sbGFwc2libGUuZGlzcGxheU5hbWUgPSBDT0xMQVBTSUJMRV9OQU1FO1xuXG4vKiAtLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tXG4gKiBDb2xsYXBzaWJsZVRyaWdnZXJcbiAqIC0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tKi9cblxuY29uc3QgVFJJR0dFUl9OQU1FID0gJ0NvbGxhcHNpYmxlVHJpZ2dlcic7XG5cbnR5cGUgQ29sbGFwc2libGVUcmlnZ2VyRWxlbWVudCA9IFJlYWN0LkVsZW1lbnRSZWY8dHlwZW9mIFByaW1pdGl2ZS5idXR0b24+O1xudHlwZSBQcmltaXRpdmVCdXR0b25Qcm9wcyA9IFJlYWN0LkNvbXBvbmVudFByb3BzV2l0aG91dFJlZjx0eXBlb2YgUHJpbWl0aXZlLmJ1dHRvbj47XG5pbnRlcmZhY2UgQ29sbGFwc2libGVUcmlnZ2VyUHJvcHMgZXh0ZW5kcyBQcmltaXRpdmVCdXR0b25Qcm9wcyB7fVxuXG5jb25zdCBDb2xsYXBzaWJsZVRyaWdnZXIgPSBSZWFjdC5mb3J3YXJkUmVmPENvbGxhcHNpYmxlVHJpZ2dlckVsZW1lbnQsIENvbGxhcHNpYmxlVHJpZ2dlclByb3BzPihcbiAgKHByb3BzOiBTY29wZWRQcm9wczxDb2xsYXBzaWJsZVRyaWdnZXJQcm9wcz4sIGZvcndhcmRlZFJlZikgPT4ge1xuICAgIGNvbnN0IHsgX19zY29wZUNvbGxhcHNpYmxlLCAuLi50cmlnZ2VyUHJvcHMgfSA9IHByb3BzO1xuICAgIGNvbnN0IGNvbnRleHQgPSB1c2VDb2xsYXBzaWJsZUNvbnRleHQoVFJJR0dFUl9OQU1FLCBfX3Njb3BlQ29sbGFwc2libGUpO1xuICAgIHJldHVybiAoXG4gICAgICA8UHJpbWl0aXZlLmJ1dHRvblxuICAgICAgICB0eXBlPVwiYnV0dG9uXCJcbiAgICAgICAgYXJpYS1jb250cm9scz17Y29udGV4dC5jb250ZW50SWR9XG4gICAgICAgIGFyaWEtZXhwYW5kZWQ9e2NvbnRleHQub3BlbiB8fCBmYWxzZX1cbiAgICAgICAgZGF0YS1zdGF0ZT17Z2V0U3RhdGUoY29udGV4dC5vcGVuKX1cbiAgICAgICAgZGF0YS1kaXNhYmxlZD17Y29udGV4dC5kaXNhYmxlZCA/ICcnIDogdW5kZWZpbmVkfVxuICAgICAgICBkaXNhYmxlZD17Y29udGV4dC5kaXNhYmxlZH1cbiAgICAgICAgey4uLnRyaWdnZXJQcm9wc31cbiAgICAgICAgcmVmPXtmb3J3YXJkZWRSZWZ9XG4gICAgICAgIG9uQ2xpY2s9e2NvbXBvc2VFdmVudEhhbmRsZXJzKHByb3BzLm9uQ2xpY2ssIGNvbnRleHQub25PcGVuVG9nZ2xlKX1cbiAgICAgIC8+XG4gICAgKTtcbiAgfVxuKTtcblxuQ29sbGFwc2libGVUcmlnZ2VyLmRpc3BsYXlOYW1lID0gVFJJR0dFUl9OQU1FO1xuXG4vKiAtLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tXG4gKiBDb2xsYXBzaWJsZUNvbnRlbnRcbiAqIC0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tKi9cblxuY29uc3QgQ09OVEVOVF9OQU1FID0gJ0NvbGxhcHNpYmxlQ29udGVudCc7XG5cbnR5cGUgQ29sbGFwc2libGVDb250ZW50RWxlbWVudCA9IENvbGxhcHNpYmxlQ29udGVudEltcGxFbGVtZW50O1xuaW50ZXJmYWNlIENvbGxhcHNpYmxlQ29udGVudFByb3BzIGV4dGVuZHMgT21pdDxDb2xsYXBzaWJsZUNvbnRlbnRJbXBsUHJvcHMsICdwcmVzZW50Jz4ge1xuICAvKipcbiAgICogVXNlZCB0byBmb3JjZSBtb3VudGluZyB3aGVuIG1vcmUgY29udHJvbCBpcyBuZWVkZWQuIFVzZWZ1bCB3aGVuXG4gICAqIGNvbnRyb2xsaW5nIGFuaW1hdGlvbiB3aXRoIFJlYWN0IGFuaW1hdGlvbiBsaWJyYXJpZXMuXG4gICAqL1xuICBmb3JjZU1vdW50PzogdHJ1ZTtcbn1cblxuY29uc3QgQ29sbGFwc2libGVDb250ZW50ID0gUmVhY3QuZm9yd2FyZFJlZjxDb2xsYXBzaWJsZUNvbnRlbnRFbGVtZW50LCBDb2xsYXBzaWJsZUNvbnRlbnRQcm9wcz4oXG4gIChwcm9wczogU2NvcGVkUHJvcHM8Q29sbGFwc2libGVDb250ZW50UHJvcHM+LCBmb3J3YXJkZWRSZWYpID0+IHtcbiAgICBjb25zdCB7IGZvcmNlTW91bnQsIC4uLmNvbnRlbnRQcm9wcyB9ID0gcHJvcHM7XG4gICAgY29uc3QgY29udGV4dCA9IHVzZUNvbGxhcHNpYmxlQ29udGV4dChDT05URU5UX05BTUUsIHByb3BzLl9fc2NvcGVDb2xsYXBzaWJsZSk7XG4gICAgcmV0dXJuIChcbiAgICAgIDxQcmVzZW5jZSBwcmVzZW50PXtmb3JjZU1vdW50IHx8IGNvbnRleHQub3Blbn0+XG4gICAgICAgIHsoeyBwcmVzZW50IH0pID0+IChcbiAgICAgICAgICA8Q29sbGFwc2libGVDb250ZW50SW1wbCB7Li4uY29udGVudFByb3BzfSByZWY9e2ZvcndhcmRlZFJlZn0gcHJlc2VudD17cHJlc2VudH0gLz5cbiAgICAgICAgKX1cbiAgICAgIDwvUHJlc2VuY2U+XG4gICAgKTtcbiAgfVxuKTtcblxuQ29sbGFwc2libGVDb250ZW50LmRpc3BsYXlOYW1lID0gQ09OVEVOVF9OQU1FO1xuXG4vKiAtLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLSovXG5cbnR5cGUgQ29sbGFwc2libGVDb250ZW50SW1wbEVsZW1lbnQgPSBSZWFjdC5FbGVtZW50UmVmPHR5cGVvZiBQcmltaXRpdmUuZGl2PjtcbmludGVyZmFjZSBDb2xsYXBzaWJsZUNvbnRlbnRJbXBsUHJvcHMgZXh0ZW5kcyBQcmltaXRpdmVEaXZQcm9wcyB7XG4gIHByZXNlbnQ6IGJvb2xlYW47XG59XG5cbmNvbnN0IENvbGxhcHNpYmxlQ29udGVudEltcGwgPSBSZWFjdC5mb3J3YXJkUmVmPFxuICBDb2xsYXBzaWJsZUNvbnRlbnRJbXBsRWxlbWVudCxcbiAgQ29sbGFwc2libGVDb250ZW50SW1wbFByb3BzXG4+KChwcm9wczogU2NvcGVkUHJvcHM8Q29sbGFwc2libGVDb250ZW50SW1wbFByb3BzPiwgZm9yd2FyZGVkUmVmKSA9PiB7XG4gIGNvbnN0IHsgX19zY29wZUNvbGxhcHNpYmxlLCBwcmVzZW50LCBjaGlsZHJlbiwgLi4uY29udGVudFByb3BzIH0gPSBwcm9wcztcbiAgY29uc3QgY29udGV4dCA9IHVzZUNvbGxhcHNpYmxlQ29udGV4dChDT05URU5UX05BTUUsIF9fc2NvcGVDb2xsYXBzaWJsZSk7XG4gIGNvbnN0IFtpc1ByZXNlbnQsIHNldElzUHJlc2VudF0gPSBSZWFjdC51c2VTdGF0ZShwcmVzZW50KTtcbiAgY29uc3QgcmVmID0gUmVhY3QudXNlUmVmPENvbGxhcHNpYmxlQ29udGVudEltcGxFbGVtZW50PihudWxsKTtcbiAgY29uc3QgY29tcG9zZWRSZWZzID0gdXNlQ29tcG9zZWRSZWZzKGZvcndhcmRlZFJlZiwgcmVmKTtcbiAgY29uc3QgaGVpZ2h0UmVmID0gUmVhY3QudXNlUmVmPG51bWJlciB8IHVuZGVmaW5lZD4oMCk7XG4gIGNvbnN0IGhlaWdodCA9IGhlaWdodFJlZi5jdXJyZW50O1xuICBjb25zdCB3aWR0aFJlZiA9IFJlYWN0LnVzZVJlZjxudW1iZXIgfCB1bmRlZmluZWQ+KDApO1xuICBjb25zdCB3aWR0aCA9IHdpZHRoUmVmLmN1cnJlbnQ7XG4gIC8vIHdoZW4gb3BlbmluZyB3ZSB3YW50IGl0IHRvIGltbWVkaWF0ZWx5IG9wZW4gdG8gcmV0cmlldmUgZGltZW5zaW9uc1xuICAvLyB3aGVuIGNsb3Npbmcgd2UgZGVsYXkgYHByZXNlbnRgIHRvIHJldHJpZXZlIGRpbWVuc2lvbnMgYmVmb3JlIGNsb3NpbmdcbiAgY29uc3QgaXNPcGVuID0gY29udGV4dC5vcGVuIHx8IGlzUHJlc2VudDtcbiAgY29uc3QgaXNNb3VudEFuaW1hdGlvblByZXZlbnRlZFJlZiA9IFJlYWN0LnVzZVJlZihpc09wZW4pO1xuICBjb25zdCBvcmlnaW5hbFN0eWxlc1JlZiA9IFJlYWN0LnVzZVJlZjxSZWNvcmQ8c3RyaW5nLCBzdHJpbmc+Pih1bmRlZmluZWQpO1xuXG4gIFJlYWN0LnVzZUVmZmVjdCgoKSA9PiB7XG4gICAgY29uc3QgckFGID0gcmVxdWVzdEFuaW1hdGlvbkZyYW1lKCgpID0+IChpc01vdW50QW5pbWF0aW9uUHJldmVudGVkUmVmLmN1cnJlbnQgPSBmYWxzZSkpO1xuICAgIHJldHVybiAoKSA9PiBjYW5jZWxBbmltYXRpb25GcmFtZShyQUYpO1xuICB9LCBbXSk7XG5cbiAgdXNlTGF5b3V0RWZmZWN0KCgpID0+IHtcbiAgICBjb25zdCBub2RlID0gcmVmLmN1cnJlbnQ7XG4gICAgaWYgKG5vZGUpIHtcbiAgICAgIG9yaWdpbmFsU3R5bGVzUmVmLmN1cnJlbnQgPSBvcmlnaW5hbFN0eWxlc1JlZi5jdXJyZW50IHx8IHtcbiAgICAgICAgdHJhbnNpdGlvbkR1cmF0aW9uOiBub2RlLnN0eWxlLnRyYW5zaXRpb25EdXJhdGlvbixcbiAgICAgICAgYW5pbWF0aW9uTmFtZTogbm9kZS5zdHlsZS5hbmltYXRpb25OYW1lLFxuICAgICAgfTtcbiAgICAgIC8vIGJsb2NrIGFueSBhbmltYXRpb25zL3RyYW5zaXRpb25zIHNvIHRoZSBlbGVtZW50IHJlbmRlcnMgYXQgaXRzIGZ1bGwgZGltZW5zaW9uc1xuICAgICAgbm9kZS5zdHlsZS50cmFuc2l0aW9uRHVyYXRpb24gPSAnMHMnO1xuICAgICAgbm9kZS5zdHlsZS5hbmltYXRpb25OYW1lID0gJ25vbmUnO1xuXG4gICAgICAvLyBnZXQgd2lkdGggYW5kIGhlaWdodCBmcm9tIGZ1bGwgZGltZW5zaW9uc1xuICAgICAgY29uc3QgcmVjdCA9IG5vZGUuZ2V0Qm91bmRpbmdDbGllbnRSZWN0KCk7XG4gICAgICBoZWlnaHRSZWYuY3VycmVudCA9IHJlY3QuaGVpZ2h0O1xuICAgICAgd2lkdGhSZWYuY3VycmVudCA9IHJlY3Qud2lkdGg7XG5cbiAgICAgIC8vIGtpY2sgb2ZmIGFueSBhbmltYXRpb25zL3RyYW5zaXRpb25zIHRoYXQgd2VyZSBvcmlnaW5hbGx5IHNldCB1cCBpZiBpdCBpc24ndCB0aGUgaW5pdGlhbCBtb3VudFxuICAgICAgaWYgKCFpc01vdW50QW5pbWF0aW9uUHJldmVudGVkUmVmLmN1cnJlbnQpIHtcbiAgICAgICAgbm9kZS5zdHlsZS50cmFuc2l0aW9uRHVyYXRpb24gPSBvcmlnaW5hbFN0eWxlc1JlZi5jdXJyZW50LnRyYW5zaXRpb25EdXJhdGlvbjtcbiAgICAgICAgbm9kZS5zdHlsZS5hbmltYXRpb25OYW1lID0gb3JpZ2luYWxTdHlsZXNSZWYuY3VycmVudC5hbmltYXRpb25OYW1lO1xuICAgICAgfVxuXG4gICAgICBzZXRJc1ByZXNlbnQocHJlc2VudCk7XG4gICAgfVxuICAgIC8qKlxuICAgICAqIGRlcGVuZHMgb24gYGNvbnRleHQub3BlbmAgYmVjYXVzZSBpdCB3aWxsIGNoYW5nZSB0byBgZmFsc2VgXG4gICAgICogd2hlbiBhIGNsb3NlIGlzIHRyaWdnZXJlZCBidXQgYHByZXNlbnRgIHdpbGwgYmUgYGZhbHNlYCBvblxuICAgICAqIGFuaW1hdGlvbiBlbmQgKHNvIHdoZW4gY2xvc2UgZmluaXNoZXMpLiBUaGlzIGFsbG93cyB1cyB0b1xuICAgICAqIHJldHJpZXZlIHRoZSBkaW1lbnNpb25zICpiZWZvcmUqIGNsb3NpbmcuXG4gICAgICovXG4gIH0sIFtjb250ZXh0Lm9wZW4sIHByZXNlbnRdKTtcblxuICByZXR1cm4gKFxuICAgIDxQcmltaXRpdmUuZGl2XG4gICAgICBkYXRhLXN0YXRlPXtnZXRTdGF0ZShjb250ZXh0Lm9wZW4pfVxuICAgICAgZGF0YS1kaXNhYmxlZD17Y29udGV4dC5kaXNhYmxlZCA/ICcnIDogdW5kZWZpbmVkfVxuICAgICAgaWQ9e2NvbnRleHQuY29udGVudElkfVxuICAgICAgaGlkZGVuPXshaXNPcGVufVxuICAgICAgey4uLmNvbnRlbnRQcm9wc31cbiAgICAgIHJlZj17Y29tcG9zZWRSZWZzfVxuICAgICAgc3R5bGU9e3tcbiAgICAgICAgW2AtLXJhZGl4LWNvbGxhcHNpYmxlLWNvbnRlbnQtaGVpZ2h0YCBhcyBhbnldOiBoZWlnaHQgPyBgJHtoZWlnaHR9cHhgIDogdW5kZWZpbmVkLFxuICAgICAgICBbYC0tcmFkaXgtY29sbGFwc2libGUtY29udGVudC13aWR0aGAgYXMgYW55XTogd2lkdGggPyBgJHt3aWR0aH1weGAgOiB1bmRlZmluZWQsXG4gICAgICAgIC4uLnByb3BzLnN0eWxlLFxuICAgICAgfX1cbiAgICA+XG4gICAgICB7aXNPcGVuICYmIGNoaWxkcmVufVxuICAgIDwvUHJpbWl0aXZlLmRpdj5cbiAgKTtcbn0pO1xuXG4vKiAtLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLSovXG5cbmZ1bmN0aW9uIGdldFN0YXRlKG9wZW4/OiBib29sZWFuKSB7XG4gIHJldHVybiBvcGVuID8gJ29wZW4nIDogJ2Nsb3NlZCc7XG59XG5cbmNvbnN0IFJvb3QgPSBDb2xsYXBzaWJsZTtcbmNvbnN0IFRyaWdnZXIgPSBDb2xsYXBzaWJsZVRyaWdnZXI7XG5jb25zdCBDb250ZW50ID0gQ29sbGFwc2libGVDb250ZW50O1xuXG5leHBvcnQge1xuICBjcmVhdGVDb2xsYXBzaWJsZVNjb3BlLFxuICAvL1xuICBDb2xsYXBzaWJsZSxcbiAgQ29sbGFwc2libGVUcmlnZ2VyLFxuICBDb2xsYXBzaWJsZUNvbnRlbnQsXG4gIC8vXG4gIFJvb3QsXG4gIFRyaWdnZXIsXG4gIENvbnRlbnQsXG59O1xuZXhwb3J0IHR5cGUgeyBDb2xsYXBzaWJsZVByb3BzLCBDb2xsYXBzaWJsZVRyaWdnZXJQcm9wcywgQ29sbGFwc2libGVDb250ZW50UHJvcHMgfTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@radix-ui+react-collapsible_a3d2c8ef7628ba8684480101895a709d/node_modules/@radix-ui/react-collapsible/dist/index.mjs\n");

/***/ })

};
;