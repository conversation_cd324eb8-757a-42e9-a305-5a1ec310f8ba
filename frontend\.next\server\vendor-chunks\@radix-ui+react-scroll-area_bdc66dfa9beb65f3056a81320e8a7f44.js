"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@radix-ui+react-scroll-area_bdc66dfa9beb65f3056a81320e8a7f44";
exports.ids = ["vendor-chunks/@radix-ui+react-scroll-area_bdc66dfa9beb65f3056a81320e8a7f44"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@radix-ui+react-scroll-area_bdc66dfa9beb65f3056a81320e8a7f44/node_modules/@radix-ui/react-scroll-area/dist/index.mjs":
/*!*************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@radix-ui+react-scroll-area_bdc66dfa9beb65f3056a81320e8a7f44/node_modules/@radix-ui/react-scroll-area/dist/index.mjs ***!
  \*************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Corner: () => (/* binding */ Corner),\n/* harmony export */   Root: () => (/* binding */ Root),\n/* harmony export */   ScrollArea: () => (/* binding */ ScrollArea),\n/* harmony export */   ScrollAreaCorner: () => (/* binding */ ScrollAreaCorner),\n/* harmony export */   ScrollAreaScrollbar: () => (/* binding */ ScrollAreaScrollbar),\n/* harmony export */   ScrollAreaThumb: () => (/* binding */ ScrollAreaThumb),\n/* harmony export */   ScrollAreaViewport: () => (/* binding */ ScrollAreaViewport),\n/* harmony export */   Scrollbar: () => (/* binding */ Scrollbar),\n/* harmony export */   Thumb: () => (/* binding */ Thumb),\n/* harmony export */   Viewport: () => (/* binding */ Viewport),\n/* harmony export */   createScrollAreaScope: () => (/* binding */ createScrollAreaScope)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @radix-ui/react-primitive */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-primitive@2_997b35f2e2aa9d3174fc03a0f79e437b/node_modules/@radix-ui/react-primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @radix-ui/react-presence */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-presence@1._d340d9d39508cb250c25fc66451df4dd/node_modules/@radix-ui/react-presence/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_context__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-context */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-context@1.1_4fe40d510edca7ae4ca9c92afeb1ae6d/node_modules/@radix-ui/react-context/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-compose-refs */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-compose-ref_2a0e526a8f7e7aada080206d385bb572/node_modules/@radix-ui/react-compose-refs/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @radix-ui/react-use-callback-ref */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-use-callbac_2dc63ba6354ec7ef7d955ba47145829a/node_modules/@radix-ui/react-use-callback-ref/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_direction__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-direction */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-direction@1_033f2d9864c311ebc46e3d65a72ec4b7/node_modules/@radix-ui/react-direction/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @radix-ui/react-use-layout-effect */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-use-layout-_7d9c308966eafc0f645092b629b133a3/node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs\");\n/* harmony import */ var _radix_ui_number__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @radix-ui/number */ \"(ssr)/./node_modules/.pnpm/@radix-ui+number@1.1.0/node_modules/@radix-ui/number/dist/index.mjs\");\n/* harmony import */ var _radix_ui_primitive__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @radix-ui/primitive */ \"(ssr)/./node_modules/.pnpm/@radix-ui+primitive@1.1.1/node_modules/@radix-ui/primitive/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ Corner,Root,ScrollArea,ScrollAreaCorner,ScrollAreaScrollbar,ScrollAreaThumb,ScrollAreaViewport,Scrollbar,Thumb,Viewport,createScrollAreaScope auto */ // packages/react/scroll-area/src/ScrollArea.tsx\n\n\n\n\n\n\n\n\n\n\n// packages/react/scroll-area/src/useStateMachine.ts\n\nfunction useStateMachine(initialState, machine) {\n    return react__WEBPACK_IMPORTED_MODULE_0__.useReducer({\n        \"useStateMachine.useReducer\": (state, event)=>{\n            const nextState = machine[state][event];\n            return nextState ?? state;\n        }\n    }[\"useStateMachine.useReducer\"], initialState);\n}\n// packages/react/scroll-area/src/ScrollArea.tsx\n\nvar SCROLL_AREA_NAME = \"ScrollArea\";\nvar [createScrollAreaContext, createScrollAreaScope] = (0,_radix_ui_react_context__WEBPACK_IMPORTED_MODULE_2__.createContextScope)(SCROLL_AREA_NAME);\nvar [ScrollAreaProvider, useScrollAreaContext] = createScrollAreaContext(SCROLL_AREA_NAME);\nvar ScrollArea = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeScrollArea, type = \"hover\", dir, scrollHideDelay = 600, ...scrollAreaProps } = props;\n    const [scrollArea, setScrollArea] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const [viewport, setViewport] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const [content, setContent] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const [scrollbarX, setScrollbarX] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const [scrollbarY, setScrollbarY] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const [cornerWidth, setCornerWidth] = react__WEBPACK_IMPORTED_MODULE_0__.useState(0);\n    const [cornerHeight, setCornerHeight] = react__WEBPACK_IMPORTED_MODULE_0__.useState(0);\n    const [scrollbarXEnabled, setScrollbarXEnabled] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    const [scrollbarYEnabled, setScrollbarYEnabled] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_3__.useComposedRefs)(forwardedRef, {\n        \"ScrollArea.useComposedRefs[composedRefs]\": (node)=>setScrollArea(node)\n    }[\"ScrollArea.useComposedRefs[composedRefs]\"]);\n    const direction = (0,_radix_ui_react_direction__WEBPACK_IMPORTED_MODULE_4__.useDirection)(dir);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(ScrollAreaProvider, {\n        scope: __scopeScrollArea,\n        type,\n        dir: direction,\n        scrollHideDelay,\n        scrollArea,\n        viewport,\n        onViewportChange: setViewport,\n        content,\n        onContentChange: setContent,\n        scrollbarX,\n        onScrollbarXChange: setScrollbarX,\n        scrollbarXEnabled,\n        onScrollbarXEnabledChange: setScrollbarXEnabled,\n        scrollbarY,\n        onScrollbarYChange: setScrollbarY,\n        scrollbarYEnabled,\n        onScrollbarYEnabledChange: setScrollbarYEnabled,\n        onCornerWidthChange: setCornerWidth,\n        onCornerHeightChange: setCornerHeight,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_5__.Primitive.div, {\n            dir: direction,\n            ...scrollAreaProps,\n            ref: composedRefs,\n            style: {\n                position: \"relative\",\n                // Pass corner sizes as CSS vars to reduce re-renders of context consumers\n                [\"--radix-scroll-area-corner-width\"]: cornerWidth + \"px\",\n                [\"--radix-scroll-area-corner-height\"]: cornerHeight + \"px\",\n                ...props.style\n            }\n        })\n    });\n});\nScrollArea.displayName = SCROLL_AREA_NAME;\nvar VIEWPORT_NAME = \"ScrollAreaViewport\";\nvar ScrollAreaViewport = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeScrollArea, children, nonce, ...viewportProps } = props;\n    const context = useScrollAreaContext(VIEWPORT_NAME, __scopeScrollArea);\n    const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_3__.useComposedRefs)(forwardedRef, ref, context.onViewportChange);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.Fragment, {\n        children: [\n            /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\"style\", {\n                dangerouslySetInnerHTML: {\n                    __html: `[data-radix-scroll-area-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-scroll-area-viewport]::-webkit-scrollbar{display:none}`\n                },\n                nonce\n            }),\n            /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_5__.Primitive.div, {\n                \"data-radix-scroll-area-viewport\": \"\",\n                ...viewportProps,\n                ref: composedRefs,\n                style: {\n                    /**\n             * We don't support `visible` because the intention is to have at least one scrollbar\n             * if this component is used and `visible` will behave like `auto` in that case\n             * https://developer.mozilla.org/en-US/docs/Web/CSS/overflow#description\n             *\n             * We don't handle `auto` because the intention is for the native implementation\n             * to be hidden if using this component. We just want to ensure the node is scrollable\n             * so could have used either `scroll` or `auto` here. We picked `scroll` to prevent\n             * the browser from having to work out whether to render native scrollbars or not,\n             * we tell it to with the intention of hiding them in CSS.\n             */ overflowX: context.scrollbarXEnabled ? \"scroll\" : \"hidden\",\n                    overflowY: context.scrollbarYEnabled ? \"scroll\" : \"hidden\",\n                    ...props.style\n                },\n                children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\"div\", {\n                    ref: context.onContentChange,\n                    style: {\n                        minWidth: \"100%\",\n                        display: \"table\"\n                    },\n                    children\n                })\n            })\n        ]\n    });\n});\nScrollAreaViewport.displayName = VIEWPORT_NAME;\nvar SCROLLBAR_NAME = \"ScrollAreaScrollbar\";\nvar ScrollAreaScrollbar = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { forceMount, ...scrollbarProps } = props;\n    const context = useScrollAreaContext(SCROLLBAR_NAME, props.__scopeScrollArea);\n    const { onScrollbarXEnabledChange, onScrollbarYEnabledChange } = context;\n    const isHorizontal = props.orientation === \"horizontal\";\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"ScrollAreaScrollbar.useEffect\": ()=>{\n            isHorizontal ? onScrollbarXEnabledChange(true) : onScrollbarYEnabledChange(true);\n            return ({\n                \"ScrollAreaScrollbar.useEffect\": ()=>{\n                    isHorizontal ? onScrollbarXEnabledChange(false) : onScrollbarYEnabledChange(false);\n                }\n            })[\"ScrollAreaScrollbar.useEffect\"];\n        }\n    }[\"ScrollAreaScrollbar.useEffect\"], [\n        isHorizontal,\n        onScrollbarXEnabledChange,\n        onScrollbarYEnabledChange\n    ]);\n    return context.type === \"hover\" ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(ScrollAreaScrollbarHover, {\n        ...scrollbarProps,\n        ref: forwardedRef,\n        forceMount\n    }) : context.type === \"scroll\" ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(ScrollAreaScrollbarScroll, {\n        ...scrollbarProps,\n        ref: forwardedRef,\n        forceMount\n    }) : context.type === \"auto\" ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(ScrollAreaScrollbarAuto, {\n        ...scrollbarProps,\n        ref: forwardedRef,\n        forceMount\n    }) : context.type === \"always\" ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(ScrollAreaScrollbarVisible, {\n        ...scrollbarProps,\n        ref: forwardedRef\n    }) : null;\n});\nScrollAreaScrollbar.displayName = SCROLLBAR_NAME;\nvar ScrollAreaScrollbarHover = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { forceMount, ...scrollbarProps } = props;\n    const context = useScrollAreaContext(SCROLLBAR_NAME, props.__scopeScrollArea);\n    const [visible, setVisible] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"ScrollAreaScrollbarHover.useEffect\": ()=>{\n            const scrollArea = context.scrollArea;\n            let hideTimer = 0;\n            if (scrollArea) {\n                const handlePointerEnter = {\n                    \"ScrollAreaScrollbarHover.useEffect.handlePointerEnter\": ()=>{\n                        window.clearTimeout(hideTimer);\n                        setVisible(true);\n                    }\n                }[\"ScrollAreaScrollbarHover.useEffect.handlePointerEnter\"];\n                const handlePointerLeave = {\n                    \"ScrollAreaScrollbarHover.useEffect.handlePointerLeave\": ()=>{\n                        hideTimer = window.setTimeout({\n                            \"ScrollAreaScrollbarHover.useEffect.handlePointerLeave\": ()=>setVisible(false)\n                        }[\"ScrollAreaScrollbarHover.useEffect.handlePointerLeave\"], context.scrollHideDelay);\n                    }\n                }[\"ScrollAreaScrollbarHover.useEffect.handlePointerLeave\"];\n                scrollArea.addEventListener(\"pointerenter\", handlePointerEnter);\n                scrollArea.addEventListener(\"pointerleave\", handlePointerLeave);\n                return ({\n                    \"ScrollAreaScrollbarHover.useEffect\": ()=>{\n                        window.clearTimeout(hideTimer);\n                        scrollArea.removeEventListener(\"pointerenter\", handlePointerEnter);\n                        scrollArea.removeEventListener(\"pointerleave\", handlePointerLeave);\n                    }\n                })[\"ScrollAreaScrollbarHover.useEffect\"];\n            }\n        }\n    }[\"ScrollAreaScrollbarHover.useEffect\"], [\n        context.scrollArea,\n        context.scrollHideDelay\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_6__.Presence, {\n        present: forceMount || visible,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(ScrollAreaScrollbarAuto, {\n            \"data-state\": visible ? \"visible\" : \"hidden\",\n            ...scrollbarProps,\n            ref: forwardedRef\n        })\n    });\n});\nvar ScrollAreaScrollbarScroll = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { forceMount, ...scrollbarProps } = props;\n    const context = useScrollAreaContext(SCROLLBAR_NAME, props.__scopeScrollArea);\n    const isHorizontal = props.orientation === \"horizontal\";\n    const debounceScrollEnd = useDebounceCallback({\n        \"ScrollAreaScrollbarScroll.useDebounceCallback[debounceScrollEnd]\": ()=>send(\"SCROLL_END\")\n    }[\"ScrollAreaScrollbarScroll.useDebounceCallback[debounceScrollEnd]\"], 100);\n    const [state, send] = useStateMachine(\"hidden\", {\n        hidden: {\n            SCROLL: \"scrolling\"\n        },\n        scrolling: {\n            SCROLL_END: \"idle\",\n            POINTER_ENTER: \"interacting\"\n        },\n        interacting: {\n            SCROLL: \"interacting\",\n            POINTER_LEAVE: \"idle\"\n        },\n        idle: {\n            HIDE: \"hidden\",\n            SCROLL: \"scrolling\",\n            POINTER_ENTER: \"interacting\"\n        }\n    });\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"ScrollAreaScrollbarScroll.useEffect\": ()=>{\n            if (state === \"idle\") {\n                const hideTimer = window.setTimeout({\n                    \"ScrollAreaScrollbarScroll.useEffect.hideTimer\": ()=>send(\"HIDE\")\n                }[\"ScrollAreaScrollbarScroll.useEffect.hideTimer\"], context.scrollHideDelay);\n                return ({\n                    \"ScrollAreaScrollbarScroll.useEffect\": ()=>window.clearTimeout(hideTimer)\n                })[\"ScrollAreaScrollbarScroll.useEffect\"];\n            }\n        }\n    }[\"ScrollAreaScrollbarScroll.useEffect\"], [\n        state,\n        context.scrollHideDelay,\n        send\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"ScrollAreaScrollbarScroll.useEffect\": ()=>{\n            const viewport = context.viewport;\n            const scrollDirection = isHorizontal ? \"scrollLeft\" : \"scrollTop\";\n            if (viewport) {\n                let prevScrollPos = viewport[scrollDirection];\n                const handleScroll = {\n                    \"ScrollAreaScrollbarScroll.useEffect.handleScroll\": ()=>{\n                        const scrollPos = viewport[scrollDirection];\n                        const hasScrollInDirectionChanged = prevScrollPos !== scrollPos;\n                        if (hasScrollInDirectionChanged) {\n                            send(\"SCROLL\");\n                            debounceScrollEnd();\n                        }\n                        prevScrollPos = scrollPos;\n                    }\n                }[\"ScrollAreaScrollbarScroll.useEffect.handleScroll\"];\n                viewport.addEventListener(\"scroll\", handleScroll);\n                return ({\n                    \"ScrollAreaScrollbarScroll.useEffect\": ()=>viewport.removeEventListener(\"scroll\", handleScroll)\n                })[\"ScrollAreaScrollbarScroll.useEffect\"];\n            }\n        }\n    }[\"ScrollAreaScrollbarScroll.useEffect\"], [\n        context.viewport,\n        isHorizontal,\n        send,\n        debounceScrollEnd\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_6__.Presence, {\n        present: forceMount || state !== \"hidden\",\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(ScrollAreaScrollbarVisible, {\n            \"data-state\": state === \"hidden\" ? \"hidden\" : \"visible\",\n            ...scrollbarProps,\n            ref: forwardedRef,\n            onPointerEnter: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_7__.composeEventHandlers)(props.onPointerEnter, ()=>send(\"POINTER_ENTER\")),\n            onPointerLeave: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_7__.composeEventHandlers)(props.onPointerLeave, ()=>send(\"POINTER_LEAVE\"))\n        })\n    });\n});\nvar ScrollAreaScrollbarAuto = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const context = useScrollAreaContext(SCROLLBAR_NAME, props.__scopeScrollArea);\n    const { forceMount, ...scrollbarProps } = props;\n    const [visible, setVisible] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    const isHorizontal = props.orientation === \"horizontal\";\n    const handleResize = useDebounceCallback({\n        \"ScrollAreaScrollbarAuto.useDebounceCallback[handleResize]\": ()=>{\n            if (context.viewport) {\n                const isOverflowX = context.viewport.offsetWidth < context.viewport.scrollWidth;\n                const isOverflowY = context.viewport.offsetHeight < context.viewport.scrollHeight;\n                setVisible(isHorizontal ? isOverflowX : isOverflowY);\n            }\n        }\n    }[\"ScrollAreaScrollbarAuto.useDebounceCallback[handleResize]\"], 10);\n    useResizeObserver(context.viewport, handleResize);\n    useResizeObserver(context.content, handleResize);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_6__.Presence, {\n        present: forceMount || visible,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(ScrollAreaScrollbarVisible, {\n            \"data-state\": visible ? \"visible\" : \"hidden\",\n            ...scrollbarProps,\n            ref: forwardedRef\n        })\n    });\n});\nvar ScrollAreaScrollbarVisible = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { orientation = \"vertical\", ...scrollbarProps } = props;\n    const context = useScrollAreaContext(SCROLLBAR_NAME, props.__scopeScrollArea);\n    const thumbRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const pointerOffsetRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(0);\n    const [sizes, setSizes] = react__WEBPACK_IMPORTED_MODULE_0__.useState({\n        content: 0,\n        viewport: 0,\n        scrollbar: {\n            size: 0,\n            paddingStart: 0,\n            paddingEnd: 0\n        }\n    });\n    const thumbRatio = getThumbRatio(sizes.viewport, sizes.content);\n    const commonProps = {\n        ...scrollbarProps,\n        sizes,\n        onSizesChange: setSizes,\n        hasThumb: Boolean(thumbRatio > 0 && thumbRatio < 1),\n        onThumbChange: (thumb)=>thumbRef.current = thumb,\n        onThumbPointerUp: ()=>pointerOffsetRef.current = 0,\n        onThumbPointerDown: (pointerPos)=>pointerOffsetRef.current = pointerPos\n    };\n    function getScrollPosition(pointerPos, dir) {\n        return getScrollPositionFromPointer(pointerPos, pointerOffsetRef.current, sizes, dir);\n    }\n    if (orientation === \"horizontal\") {\n        return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(ScrollAreaScrollbarX, {\n            ...commonProps,\n            ref: forwardedRef,\n            onThumbPositionChange: ()=>{\n                if (context.viewport && thumbRef.current) {\n                    const scrollPos = context.viewport.scrollLeft;\n                    const offset = getThumbOffsetFromScroll(scrollPos, sizes, context.dir);\n                    thumbRef.current.style.transform = `translate3d(${offset}px, 0, 0)`;\n                }\n            },\n            onWheelScroll: (scrollPos)=>{\n                if (context.viewport) context.viewport.scrollLeft = scrollPos;\n            },\n            onDragScroll: (pointerPos)=>{\n                if (context.viewport) {\n                    context.viewport.scrollLeft = getScrollPosition(pointerPos, context.dir);\n                }\n            }\n        });\n    }\n    if (orientation === \"vertical\") {\n        return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(ScrollAreaScrollbarY, {\n            ...commonProps,\n            ref: forwardedRef,\n            onThumbPositionChange: ()=>{\n                if (context.viewport && thumbRef.current) {\n                    const scrollPos = context.viewport.scrollTop;\n                    const offset = getThumbOffsetFromScroll(scrollPos, sizes);\n                    thumbRef.current.style.transform = `translate3d(0, ${offset}px, 0)`;\n                }\n            },\n            onWheelScroll: (scrollPos)=>{\n                if (context.viewport) context.viewport.scrollTop = scrollPos;\n            },\n            onDragScroll: (pointerPos)=>{\n                if (context.viewport) context.viewport.scrollTop = getScrollPosition(pointerPos);\n            }\n        });\n    }\n    return null;\n});\nvar ScrollAreaScrollbarX = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { sizes, onSizesChange, ...scrollbarProps } = props;\n    const context = useScrollAreaContext(SCROLLBAR_NAME, props.__scopeScrollArea);\n    const [computedStyle, setComputedStyle] = react__WEBPACK_IMPORTED_MODULE_0__.useState();\n    const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const composeRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_3__.useComposedRefs)(forwardedRef, ref, context.onScrollbarXChange);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"ScrollAreaScrollbarX.useEffect\": ()=>{\n            if (ref.current) setComputedStyle(getComputedStyle(ref.current));\n        }\n    }[\"ScrollAreaScrollbarX.useEffect\"], [\n        ref\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(ScrollAreaScrollbarImpl, {\n        \"data-orientation\": \"horizontal\",\n        ...scrollbarProps,\n        ref: composeRefs,\n        sizes,\n        style: {\n            bottom: 0,\n            left: context.dir === \"rtl\" ? \"var(--radix-scroll-area-corner-width)\" : 0,\n            right: context.dir === \"ltr\" ? \"var(--radix-scroll-area-corner-width)\" : 0,\n            [\"--radix-scroll-area-thumb-width\"]: getThumbSize(sizes) + \"px\",\n            ...props.style\n        },\n        onThumbPointerDown: (pointerPos)=>props.onThumbPointerDown(pointerPos.x),\n        onDragScroll: (pointerPos)=>props.onDragScroll(pointerPos.x),\n        onWheelScroll: (event, maxScrollPos)=>{\n            if (context.viewport) {\n                const scrollPos = context.viewport.scrollLeft + event.deltaX;\n                props.onWheelScroll(scrollPos);\n                if (isScrollingWithinScrollbarBounds(scrollPos, maxScrollPos)) {\n                    event.preventDefault();\n                }\n            }\n        },\n        onResize: ()=>{\n            if (ref.current && context.viewport && computedStyle) {\n                onSizesChange({\n                    content: context.viewport.scrollWidth,\n                    viewport: context.viewport.offsetWidth,\n                    scrollbar: {\n                        size: ref.current.clientWidth,\n                        paddingStart: toInt(computedStyle.paddingLeft),\n                        paddingEnd: toInt(computedStyle.paddingRight)\n                    }\n                });\n            }\n        }\n    });\n});\nvar ScrollAreaScrollbarY = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { sizes, onSizesChange, ...scrollbarProps } = props;\n    const context = useScrollAreaContext(SCROLLBAR_NAME, props.__scopeScrollArea);\n    const [computedStyle, setComputedStyle] = react__WEBPACK_IMPORTED_MODULE_0__.useState();\n    const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const composeRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_3__.useComposedRefs)(forwardedRef, ref, context.onScrollbarYChange);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"ScrollAreaScrollbarY.useEffect\": ()=>{\n            if (ref.current) setComputedStyle(getComputedStyle(ref.current));\n        }\n    }[\"ScrollAreaScrollbarY.useEffect\"], [\n        ref\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(ScrollAreaScrollbarImpl, {\n        \"data-orientation\": \"vertical\",\n        ...scrollbarProps,\n        ref: composeRefs,\n        sizes,\n        style: {\n            top: 0,\n            right: context.dir === \"ltr\" ? 0 : void 0,\n            left: context.dir === \"rtl\" ? 0 : void 0,\n            bottom: \"var(--radix-scroll-area-corner-height)\",\n            [\"--radix-scroll-area-thumb-height\"]: getThumbSize(sizes) + \"px\",\n            ...props.style\n        },\n        onThumbPointerDown: (pointerPos)=>props.onThumbPointerDown(pointerPos.y),\n        onDragScroll: (pointerPos)=>props.onDragScroll(pointerPos.y),\n        onWheelScroll: (event, maxScrollPos)=>{\n            if (context.viewport) {\n                const scrollPos = context.viewport.scrollTop + event.deltaY;\n                props.onWheelScroll(scrollPos);\n                if (isScrollingWithinScrollbarBounds(scrollPos, maxScrollPos)) {\n                    event.preventDefault();\n                }\n            }\n        },\n        onResize: ()=>{\n            if (ref.current && context.viewport && computedStyle) {\n                onSizesChange({\n                    content: context.viewport.scrollHeight,\n                    viewport: context.viewport.offsetHeight,\n                    scrollbar: {\n                        size: ref.current.clientHeight,\n                        paddingStart: toInt(computedStyle.paddingTop),\n                        paddingEnd: toInt(computedStyle.paddingBottom)\n                    }\n                });\n            }\n        }\n    });\n});\nvar [ScrollbarProvider, useScrollbarContext] = createScrollAreaContext(SCROLLBAR_NAME);\nvar ScrollAreaScrollbarImpl = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeScrollArea, sizes, hasThumb, onThumbChange, onThumbPointerUp, onThumbPointerDown, onThumbPositionChange, onDragScroll, onWheelScroll, onResize, ...scrollbarProps } = props;\n    const context = useScrollAreaContext(SCROLLBAR_NAME, __scopeScrollArea);\n    const [scrollbar, setScrollbar] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const composeRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_3__.useComposedRefs)(forwardedRef, {\n        \"ScrollAreaScrollbarImpl.useComposedRefs[composeRefs]\": (node)=>setScrollbar(node)\n    }[\"ScrollAreaScrollbarImpl.useComposedRefs[composeRefs]\"]);\n    const rectRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const prevWebkitUserSelectRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(\"\");\n    const viewport = context.viewport;\n    const maxScrollPos = sizes.content - sizes.viewport;\n    const handleWheelScroll = (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_8__.useCallbackRef)(onWheelScroll);\n    const handleThumbPositionChange = (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_8__.useCallbackRef)(onThumbPositionChange);\n    const handleResize = useDebounceCallback(onResize, 10);\n    function handleDragScroll(event) {\n        if (rectRef.current) {\n            const x = event.clientX - rectRef.current.left;\n            const y = event.clientY - rectRef.current.top;\n            onDragScroll({\n                x,\n                y\n            });\n        }\n    }\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"ScrollAreaScrollbarImpl.useEffect\": ()=>{\n            const handleWheel = {\n                \"ScrollAreaScrollbarImpl.useEffect.handleWheel\": (event)=>{\n                    const element = event.target;\n                    const isScrollbarWheel = scrollbar?.contains(element);\n                    if (isScrollbarWheel) handleWheelScroll(event, maxScrollPos);\n                }\n            }[\"ScrollAreaScrollbarImpl.useEffect.handleWheel\"];\n            document.addEventListener(\"wheel\", handleWheel, {\n                passive: false\n            });\n            return ({\n                \"ScrollAreaScrollbarImpl.useEffect\": ()=>document.removeEventListener(\"wheel\", handleWheel, {\n                        passive: false\n                    })\n            })[\"ScrollAreaScrollbarImpl.useEffect\"];\n        }\n    }[\"ScrollAreaScrollbarImpl.useEffect\"], [\n        viewport,\n        scrollbar,\n        maxScrollPos,\n        handleWheelScroll\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(handleThumbPositionChange, [\n        sizes,\n        handleThumbPositionChange\n    ]);\n    useResizeObserver(scrollbar, handleResize);\n    useResizeObserver(context.content, handleResize);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(ScrollbarProvider, {\n        scope: __scopeScrollArea,\n        scrollbar,\n        hasThumb,\n        onThumbChange: (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_8__.useCallbackRef)(onThumbChange),\n        onThumbPointerUp: (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_8__.useCallbackRef)(onThumbPointerUp),\n        onThumbPositionChange: handleThumbPositionChange,\n        onThumbPointerDown: (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_8__.useCallbackRef)(onThumbPointerDown),\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_5__.Primitive.div, {\n            ...scrollbarProps,\n            ref: composeRefs,\n            style: {\n                position: \"absolute\",\n                ...scrollbarProps.style\n            },\n            onPointerDown: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_7__.composeEventHandlers)(props.onPointerDown, (event)=>{\n                const mainPointer = 0;\n                if (event.button === mainPointer) {\n                    const element = event.target;\n                    element.setPointerCapture(event.pointerId);\n                    rectRef.current = scrollbar.getBoundingClientRect();\n                    prevWebkitUserSelectRef.current = document.body.style.webkitUserSelect;\n                    document.body.style.webkitUserSelect = \"none\";\n                    if (context.viewport) context.viewport.style.scrollBehavior = \"auto\";\n                    handleDragScroll(event);\n                }\n            }),\n            onPointerMove: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_7__.composeEventHandlers)(props.onPointerMove, handleDragScroll),\n            onPointerUp: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_7__.composeEventHandlers)(props.onPointerUp, (event)=>{\n                const element = event.target;\n                if (element.hasPointerCapture(event.pointerId)) {\n                    element.releasePointerCapture(event.pointerId);\n                }\n                document.body.style.webkitUserSelect = prevWebkitUserSelectRef.current;\n                if (context.viewport) context.viewport.style.scrollBehavior = \"\";\n                rectRef.current = null;\n            })\n        })\n    });\n});\nvar THUMB_NAME = \"ScrollAreaThumb\";\nvar ScrollAreaThumb = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { forceMount, ...thumbProps } = props;\n    const scrollbarContext = useScrollbarContext(THUMB_NAME, props.__scopeScrollArea);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_6__.Presence, {\n        present: forceMount || scrollbarContext.hasThumb,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(ScrollAreaThumbImpl, {\n            ref: forwardedRef,\n            ...thumbProps\n        })\n    });\n});\nvar ScrollAreaThumbImpl = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeScrollArea, style, ...thumbProps } = props;\n    const scrollAreaContext = useScrollAreaContext(THUMB_NAME, __scopeScrollArea);\n    const scrollbarContext = useScrollbarContext(THUMB_NAME, __scopeScrollArea);\n    const { onThumbPositionChange } = scrollbarContext;\n    const composedRef = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_3__.useComposedRefs)(forwardedRef, {\n        \"ScrollAreaThumbImpl.useComposedRefs[composedRef]\": (node)=>scrollbarContext.onThumbChange(node)\n    }[\"ScrollAreaThumbImpl.useComposedRefs[composedRef]\"]);\n    const removeUnlinkedScrollListenerRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(void 0);\n    const debounceScrollEnd = useDebounceCallback({\n        \"ScrollAreaThumbImpl.useDebounceCallback[debounceScrollEnd]\": ()=>{\n            if (removeUnlinkedScrollListenerRef.current) {\n                removeUnlinkedScrollListenerRef.current();\n                removeUnlinkedScrollListenerRef.current = void 0;\n            }\n        }\n    }[\"ScrollAreaThumbImpl.useDebounceCallback[debounceScrollEnd]\"], 100);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"ScrollAreaThumbImpl.useEffect\": ()=>{\n            const viewport = scrollAreaContext.viewport;\n            if (viewport) {\n                const handleScroll = {\n                    \"ScrollAreaThumbImpl.useEffect.handleScroll\": ()=>{\n                        debounceScrollEnd();\n                        if (!removeUnlinkedScrollListenerRef.current) {\n                            const listener = addUnlinkedScrollListener(viewport, onThumbPositionChange);\n                            removeUnlinkedScrollListenerRef.current = listener;\n                            onThumbPositionChange();\n                        }\n                    }\n                }[\"ScrollAreaThumbImpl.useEffect.handleScroll\"];\n                onThumbPositionChange();\n                viewport.addEventListener(\"scroll\", handleScroll);\n                return ({\n                    \"ScrollAreaThumbImpl.useEffect\": ()=>viewport.removeEventListener(\"scroll\", handleScroll)\n                })[\"ScrollAreaThumbImpl.useEffect\"];\n            }\n        }\n    }[\"ScrollAreaThumbImpl.useEffect\"], [\n        scrollAreaContext.viewport,\n        debounceScrollEnd,\n        onThumbPositionChange\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_5__.Primitive.div, {\n        \"data-state\": scrollbarContext.hasThumb ? \"visible\" : \"hidden\",\n        ...thumbProps,\n        ref: composedRef,\n        style: {\n            width: \"var(--radix-scroll-area-thumb-width)\",\n            height: \"var(--radix-scroll-area-thumb-height)\",\n            ...style\n        },\n        onPointerDownCapture: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_7__.composeEventHandlers)(props.onPointerDownCapture, (event)=>{\n            const thumb = event.target;\n            const thumbRect = thumb.getBoundingClientRect();\n            const x = event.clientX - thumbRect.left;\n            const y = event.clientY - thumbRect.top;\n            scrollbarContext.onThumbPointerDown({\n                x,\n                y\n            });\n        }),\n        onPointerUp: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_7__.composeEventHandlers)(props.onPointerUp, scrollbarContext.onThumbPointerUp)\n    });\n});\nScrollAreaThumb.displayName = THUMB_NAME;\nvar CORNER_NAME = \"ScrollAreaCorner\";\nvar ScrollAreaCorner = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const context = useScrollAreaContext(CORNER_NAME, props.__scopeScrollArea);\n    const hasBothScrollbarsVisible = Boolean(context.scrollbarX && context.scrollbarY);\n    const hasCorner = context.type !== \"scroll\" && hasBothScrollbarsVisible;\n    return hasCorner ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(ScrollAreaCornerImpl, {\n        ...props,\n        ref: forwardedRef\n    }) : null;\n});\nScrollAreaCorner.displayName = CORNER_NAME;\nvar ScrollAreaCornerImpl = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeScrollArea, ...cornerProps } = props;\n    const context = useScrollAreaContext(CORNER_NAME, __scopeScrollArea);\n    const [width, setWidth] = react__WEBPACK_IMPORTED_MODULE_0__.useState(0);\n    const [height, setHeight] = react__WEBPACK_IMPORTED_MODULE_0__.useState(0);\n    const hasSize = Boolean(width && height);\n    useResizeObserver(context.scrollbarX, {\n        \"ScrollAreaCornerImpl.useResizeObserver\": ()=>{\n            const height2 = context.scrollbarX?.offsetHeight || 0;\n            context.onCornerHeightChange(height2);\n            setHeight(height2);\n        }\n    }[\"ScrollAreaCornerImpl.useResizeObserver\"]);\n    useResizeObserver(context.scrollbarY, {\n        \"ScrollAreaCornerImpl.useResizeObserver\": ()=>{\n            const width2 = context.scrollbarY?.offsetWidth || 0;\n            context.onCornerWidthChange(width2);\n            setWidth(width2);\n        }\n    }[\"ScrollAreaCornerImpl.useResizeObserver\"]);\n    return hasSize ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_5__.Primitive.div, {\n        ...cornerProps,\n        ref: forwardedRef,\n        style: {\n            width,\n            height,\n            position: \"absolute\",\n            right: context.dir === \"ltr\" ? 0 : void 0,\n            left: context.dir === \"rtl\" ? 0 : void 0,\n            bottom: 0,\n            ...props.style\n        }\n    }) : null;\n});\nfunction toInt(value) {\n    return value ? parseInt(value, 10) : 0;\n}\nfunction getThumbRatio(viewportSize, contentSize) {\n    const ratio = viewportSize / contentSize;\n    return isNaN(ratio) ? 0 : ratio;\n}\nfunction getThumbSize(sizes) {\n    const ratio = getThumbRatio(sizes.viewport, sizes.content);\n    const scrollbarPadding = sizes.scrollbar.paddingStart + sizes.scrollbar.paddingEnd;\n    const thumbSize = (sizes.scrollbar.size - scrollbarPadding) * ratio;\n    return Math.max(thumbSize, 18);\n}\nfunction getScrollPositionFromPointer(pointerPos, pointerOffset, sizes, dir = \"ltr\") {\n    const thumbSizePx = getThumbSize(sizes);\n    const thumbCenter = thumbSizePx / 2;\n    const offset = pointerOffset || thumbCenter;\n    const thumbOffsetFromEnd = thumbSizePx - offset;\n    const minPointerPos = sizes.scrollbar.paddingStart + offset;\n    const maxPointerPos = sizes.scrollbar.size - sizes.scrollbar.paddingEnd - thumbOffsetFromEnd;\n    const maxScrollPos = sizes.content - sizes.viewport;\n    const scrollRange = dir === \"ltr\" ? [\n        0,\n        maxScrollPos\n    ] : [\n        maxScrollPos * -1,\n        0\n    ];\n    const interpolate = linearScale([\n        minPointerPos,\n        maxPointerPos\n    ], scrollRange);\n    return interpolate(pointerPos);\n}\nfunction getThumbOffsetFromScroll(scrollPos, sizes, dir = \"ltr\") {\n    const thumbSizePx = getThumbSize(sizes);\n    const scrollbarPadding = sizes.scrollbar.paddingStart + sizes.scrollbar.paddingEnd;\n    const scrollbar = sizes.scrollbar.size - scrollbarPadding;\n    const maxScrollPos = sizes.content - sizes.viewport;\n    const maxThumbPos = scrollbar - thumbSizePx;\n    const scrollClampRange = dir === \"ltr\" ? [\n        0,\n        maxScrollPos\n    ] : [\n        maxScrollPos * -1,\n        0\n    ];\n    const scrollWithoutMomentum = (0,_radix_ui_number__WEBPACK_IMPORTED_MODULE_9__.clamp)(scrollPos, scrollClampRange);\n    const interpolate = linearScale([\n        0,\n        maxScrollPos\n    ], [\n        0,\n        maxThumbPos\n    ]);\n    return interpolate(scrollWithoutMomentum);\n}\nfunction linearScale(input, output) {\n    return (value)=>{\n        if (input[0] === input[1] || output[0] === output[1]) return output[0];\n        const ratio = (output[1] - output[0]) / (input[1] - input[0]);\n        return output[0] + ratio * (value - input[0]);\n    };\n}\nfunction isScrollingWithinScrollbarBounds(scrollPos, maxScrollPos) {\n    return scrollPos > 0 && scrollPos < maxScrollPos;\n}\nvar addUnlinkedScrollListener = (node, handler = ()=>{})=>{\n    let prevPosition = {\n        left: node.scrollLeft,\n        top: node.scrollTop\n    };\n    let rAF = 0;\n    (function loop() {\n        const position = {\n            left: node.scrollLeft,\n            top: node.scrollTop\n        };\n        const isHorizontalScroll = prevPosition.left !== position.left;\n        const isVerticalScroll = prevPosition.top !== position.top;\n        if (isHorizontalScroll || isVerticalScroll) handler();\n        prevPosition = position;\n        rAF = window.requestAnimationFrame(loop);\n    })();\n    return ()=>window.cancelAnimationFrame(rAF);\n};\nfunction useDebounceCallback(callback, delay) {\n    const handleCallback = (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_8__.useCallbackRef)(callback);\n    const debounceTimerRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(0);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"useDebounceCallback.useEffect\": ()=>({\n                \"useDebounceCallback.useEffect\": ()=>window.clearTimeout(debounceTimerRef.current)\n            })[\"useDebounceCallback.useEffect\"]\n    }[\"useDebounceCallback.useEffect\"], []);\n    return react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n        \"useDebounceCallback.useCallback\": ()=>{\n            window.clearTimeout(debounceTimerRef.current);\n            debounceTimerRef.current = window.setTimeout(handleCallback, delay);\n        }\n    }[\"useDebounceCallback.useCallback\"], [\n        handleCallback,\n        delay\n    ]);\n}\nfunction useResizeObserver(element, onResize) {\n    const handleResize = (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_8__.useCallbackRef)(onResize);\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_10__.useLayoutEffect)({\n        \"useResizeObserver.useLayoutEffect\": ()=>{\n            let rAF = 0;\n            if (element) {\n                const resizeObserver = new ResizeObserver({\n                    \"useResizeObserver.useLayoutEffect\": ()=>{\n                        cancelAnimationFrame(rAF);\n                        rAF = window.requestAnimationFrame(handleResize);\n                    }\n                }[\"useResizeObserver.useLayoutEffect\"]);\n                resizeObserver.observe(element);\n                return ({\n                    \"useResizeObserver.useLayoutEffect\": ()=>{\n                        window.cancelAnimationFrame(rAF);\n                        resizeObserver.unobserve(element);\n                    }\n                })[\"useResizeObserver.useLayoutEffect\"];\n            }\n        }\n    }[\"useResizeObserver.useLayoutEffect\"], [\n        element,\n        handleResize\n    ]);\n}\nvar Root = ScrollArea;\nvar Viewport = ScrollAreaViewport;\nvar Scrollbar = ScrollAreaScrollbar;\nvar Thumb = ScrollAreaThumb;\nvar Corner = ScrollAreaCorner;\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@radix-ui+react-scroll-area_bdc66dfa9beb65f3056a81320e8a7f44/node_modules/@radix-ui/react-scroll-area/dist/index.mjs\n");

/***/ })

};
;