"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/micromark-extension-gfm-footnote";
exports.ids = ["vendor-chunks/micromark-extension-gfm-footnote"];
exports.modules = {

/***/ "(ssr)/./node_modules/micromark-extension-gfm-footnote/dev/lib/html.js":
/*!***********************************************************************!*\
  !*** ./node_modules/micromark-extension-gfm-footnote/dev/lib/html.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   defaultBackLabel: () => (/* binding */ defaultBackLabel),\n/* harmony export */   gfmFootnoteHtml: () => (/* binding */ gfmFootnoteHtml)\n/* harmony export */ });\n/* harmony import */ var devlop__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! devlop */ \"(ssr)/./node_modules/devlop/lib/development.js\");\n/* harmony import */ var micromark_util_normalize_identifier__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! micromark-util-normalize-identifier */ \"(ssr)/./node_modules/micromark-util-normalize-identifier/dev/index.js\");\n/* harmony import */ var micromark_util_sanitize_uri__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! micromark-util-sanitize-uri */ \"(ssr)/./node_modules/micromark-util-sanitize-uri/dev/index.js\");\n/**\n * @import {HtmlOptions as Options} from 'micromark-extension-gfm-footnote'\n * @import {HtmlExtension} from 'micromark-util-types'\n */\n\n\n\n\n\nconst own = {}.hasOwnProperty\n\n/** @type {Options} */\nconst emptyOptions = {}\n\n/**\n * Generate the default label that GitHub uses on backreferences.\n *\n * @param {number} referenceIndex\n *   Index of the definition in the order that they are first referenced,\n *   0-indexed.\n * @param {number} rereferenceIndex\n *   Index of calls to the same definition, 0-indexed.\n * @returns {string}\n *   Default label.\n */\nfunction defaultBackLabel(referenceIndex, rereferenceIndex) {\n  return (\n    'Back to reference ' +\n    (referenceIndex + 1) +\n    (rereferenceIndex > 1 ? '-' + rereferenceIndex : '')\n  )\n}\n\n/**\n * Create an extension for `micromark` to support GFM footnotes when\n * serializing to HTML.\n *\n * @param {Options | null | undefined} [options={}]\n *   Configuration (optional).\n * @returns {HtmlExtension}\n *   Extension for `micromark` that can be passed in `htmlExtensions` to\n *   support GFM footnotes when serializing to HTML.\n */\nfunction gfmFootnoteHtml(options) {\n  const config = options || emptyOptions\n  const label = config.label || 'Footnotes'\n  const labelTagName = config.labelTagName || 'h2'\n  const labelAttributes =\n    config.labelAttributes === null || config.labelAttributes === undefined\n      ? 'class=\"sr-only\"'\n      : config.labelAttributes\n  const backLabel = config.backLabel || defaultBackLabel\n  const clobberPrefix =\n    config.clobberPrefix === null || config.clobberPrefix === undefined\n      ? 'user-content-'\n      : config.clobberPrefix\n  return {\n    enter: {\n      gfmFootnoteDefinition() {\n        const stack = this.getData('tightStack')\n        stack.push(false)\n      },\n      gfmFootnoteDefinitionLabelString() {\n        this.buffer()\n      },\n      gfmFootnoteCallString() {\n        this.buffer()\n      }\n    },\n    exit: {\n      gfmFootnoteDefinition() {\n        let definitions = this.getData('gfmFootnoteDefinitions')\n        const footnoteStack = this.getData('gfmFootnoteDefinitionStack')\n        ;(0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)(footnoteStack, 'expected `footnoteStack`')\n        const tightStack = this.getData('tightStack')\n        const current = footnoteStack.pop()\n        const value = this.resume()\n\n        ;(0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)(current, 'expected to be in a footnote')\n\n        if (!definitions) {\n          this.setData('gfmFootnoteDefinitions', (definitions = {}))\n        }\n\n        if (!own.call(definitions, current)) definitions[current] = value\n\n        tightStack.pop()\n        this.setData('slurpOneLineEnding', true)\n        // “Hack” to prevent a line ending from showing up if we’re in a definition in\n        // an empty list item.\n        this.setData('lastWasTag')\n      },\n      gfmFootnoteDefinitionLabelString(token) {\n        let footnoteStack = this.getData('gfmFootnoteDefinitionStack')\n\n        if (!footnoteStack) {\n          this.setData('gfmFootnoteDefinitionStack', (footnoteStack = []))\n        }\n\n        footnoteStack.push((0,micromark_util_normalize_identifier__WEBPACK_IMPORTED_MODULE_1__.normalizeIdentifier)(this.sliceSerialize(token)))\n        this.resume() // Drop the label.\n        this.buffer() // Get ready for a value.\n      },\n      gfmFootnoteCallString(token) {\n        let calls = this.getData('gfmFootnoteCallOrder')\n        let counts = this.getData('gfmFootnoteCallCounts')\n        const id = (0,micromark_util_normalize_identifier__WEBPACK_IMPORTED_MODULE_1__.normalizeIdentifier)(this.sliceSerialize(token))\n        /** @type {number} */\n        let counter\n\n        this.resume()\n\n        if (!calls) this.setData('gfmFootnoteCallOrder', (calls = []))\n        if (!counts) this.setData('gfmFootnoteCallCounts', (counts = {}))\n\n        const index = calls.indexOf(id)\n        const safeId = (0,micromark_util_sanitize_uri__WEBPACK_IMPORTED_MODULE_2__.sanitizeUri)(id.toLowerCase())\n\n        if (index === -1) {\n          calls.push(id)\n          counts[id] = 1\n          counter = calls.length\n        } else {\n          counts[id]++\n          counter = index + 1\n        }\n\n        const reuseCounter = counts[id]\n\n        this.tag(\n          '<sup><a href=\"#' +\n            clobberPrefix +\n            'fn-' +\n            safeId +\n            '\" id=\"' +\n            clobberPrefix +\n            'fnref-' +\n            safeId +\n            (reuseCounter > 1 ? '-' + reuseCounter : '') +\n            '\" data-footnote-ref=\"\" aria-describedby=\"footnote-label\">' +\n            String(counter) +\n            '</a></sup>'\n        )\n      },\n      null() {\n        const calls = this.getData('gfmFootnoteCallOrder') || []\n        const counts = this.getData('gfmFootnoteCallCounts') || {}\n        const definitions = this.getData('gfmFootnoteDefinitions') || {}\n        let index = -1\n\n        if (calls.length > 0) {\n          this.lineEndingIfNeeded()\n          this.tag(\n            '<section data-footnotes=\"\" class=\"footnotes\"><' +\n              labelTagName +\n              ' id=\"footnote-label\"' +\n              (labelAttributes ? ' ' + labelAttributes : '') +\n              '>'\n          )\n          this.raw(this.encode(label))\n          this.tag('</' + labelTagName + '>')\n          this.lineEndingIfNeeded()\n          this.tag('<ol>')\n        }\n\n        while (++index < calls.length) {\n          // Called definitions are always defined.\n          const id = calls[index]\n          const safeId = (0,micromark_util_sanitize_uri__WEBPACK_IMPORTED_MODULE_2__.sanitizeUri)(id.toLowerCase())\n          let referenceIndex = 0\n          /** @type {Array<string>} */\n          const references = []\n\n          while (++referenceIndex <= counts[id]) {\n            references.push(\n              '<a href=\"#' +\n                clobberPrefix +\n                'fnref-' +\n                safeId +\n                (referenceIndex > 1 ? '-' + referenceIndex : '') +\n                '\" data-footnote-backref=\"\" aria-label=\"' +\n                this.encode(\n                  typeof backLabel === 'string'\n                    ? backLabel\n                    : backLabel(index, referenceIndex)\n                ) +\n                '\" class=\"data-footnote-backref\">↩' +\n                (referenceIndex > 1\n                  ? '<sup>' + referenceIndex + '</sup>'\n                  : '') +\n                '</a>'\n            )\n          }\n\n          const reference = references.join(' ')\n          let injected = false\n\n          this.lineEndingIfNeeded()\n          this.tag('<li id=\"' + clobberPrefix + 'fn-' + safeId + '\">')\n          this.lineEndingIfNeeded()\n          this.tag(\n            definitions[id].replace(/<\\/p>(?:\\r?\\n|\\r)?$/, function ($0) {\n              injected = true\n              return ' ' + reference + $0\n            })\n          )\n\n          if (!injected) {\n            this.lineEndingIfNeeded()\n            this.tag(reference)\n          }\n\n          this.lineEndingIfNeeded()\n          this.tag('</li>')\n        }\n\n        if (calls.length > 0) {\n          this.lineEndingIfNeeded()\n          this.tag('</ol>')\n          this.lineEndingIfNeeded()\n          this.tag('</section>')\n        }\n      }\n    }\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/micromark-extension-gfm-footnote/dev/lib/html.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/micromark-extension-gfm-footnote/dev/lib/syntax.js":
/*!*************************************************************************!*\
  !*** ./node_modules/micromark-extension-gfm-footnote/dev/lib/syntax.js ***!
  \*************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   gfmFootnote: () => (/* binding */ gfmFootnote)\n/* harmony export */ });\n/* harmony import */ var devlop__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! devlop */ \"(ssr)/./node_modules/devlop/lib/development.js\");\n/* harmony import */ var micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! micromark-core-commonmark */ \"(ssr)/./node_modules/micromark-core-commonmark/dev/lib/blank-line.js\");\n/* harmony import */ var micromark_factory_space__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! micromark-factory-space */ \"(ssr)/./node_modules/micromark-factory-space/dev/index.js\");\n/* harmony import */ var micromark_util_character__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! micromark-util-character */ \"(ssr)/./node_modules/micromark-util-character/dev/index.js\");\n/* harmony import */ var micromark_util_normalize_identifier__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! micromark-util-normalize-identifier */ \"(ssr)/./node_modules/micromark-util-normalize-identifier/dev/index.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! micromark-util-symbol */ \"(ssr)/./node_modules/micromark-util-symbol/lib/codes.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! micromark-util-symbol */ \"(ssr)/./node_modules/micromark-util-symbol/lib/types.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! micromark-util-symbol */ \"(ssr)/./node_modules/micromark-util-symbol/lib/constants.js\");\n/**\n * @import {Event, Exiter, Extension, Resolver, State, Token, TokenizeContext, Tokenizer} from 'micromark-util-types'\n */\n\n\n\n\n\n\n\n\nconst indent = {tokenize: tokenizeIndent, partial: true}\n\n// To do: micromark should support a `_hiddenGfmFootnoteSupport`, which only\n// affects label start (image).\n// That will let us drop `tokenizePotentialGfmFootnote*`.\n// It currently has a `_hiddenFootnoteSupport`, which affects that and more.\n// That can be removed when `micromark-extension-footnote` is archived.\n\n/**\n * Create an extension for `micromark` to enable GFM footnote syntax.\n *\n * @returns {Extension}\n *   Extension for `micromark` that can be passed in `extensions` to\n *   enable GFM footnote syntax.\n */\nfunction gfmFootnote() {\n  /** @type {Extension} */\n  return {\n    document: {\n      [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.leftSquareBracket]: {\n        name: 'gfmFootnoteDefinition',\n        tokenize: tokenizeDefinitionStart,\n        continuation: {tokenize: tokenizeDefinitionContinuation},\n        exit: gfmFootnoteDefinitionEnd\n      }\n    },\n    text: {\n      [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.leftSquareBracket]: {\n        name: 'gfmFootnoteCall',\n        tokenize: tokenizeGfmFootnoteCall\n      },\n      [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.rightSquareBracket]: {\n        name: 'gfmPotentialFootnoteCall',\n        add: 'after',\n        tokenize: tokenizePotentialGfmFootnoteCall,\n        resolveTo: resolveToPotentialGfmFootnoteCall\n      }\n    }\n  }\n}\n\n// To do: remove after micromark update.\n/**\n * @this {TokenizeContext}\n * @type {Tokenizer}\n */\nfunction tokenizePotentialGfmFootnoteCall(effects, ok, nok) {\n  const self = this\n  let index = self.events.length\n  const defined = self.parser.gfmFootnotes || (self.parser.gfmFootnotes = [])\n  /** @type {Token} */\n  let labelStart\n\n  // Find an opening.\n  while (index--) {\n    const token = self.events[index][1]\n\n    if (token.type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.types.labelImage) {\n      labelStart = token\n      break\n    }\n\n    // Exit if we’ve walked far enough.\n    if (\n      token.type === 'gfmFootnoteCall' ||\n      token.type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.types.labelLink ||\n      token.type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.types.label ||\n      token.type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.types.image ||\n      token.type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.types.link\n    ) {\n      break\n    }\n  }\n\n  return start\n\n  /**\n   * @type {State}\n   */\n  function start(code) {\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_2__.ok)(code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.rightSquareBracket, 'expected `]`')\n\n    if (!labelStart || !labelStart._balanced) {\n      return nok(code)\n    }\n\n    const id = (0,micromark_util_normalize_identifier__WEBPACK_IMPORTED_MODULE_3__.normalizeIdentifier)(\n      self.sliceSerialize({start: labelStart.end, end: self.now()})\n    )\n\n    if (id.codePointAt(0) !== micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.caret || !defined.includes(id.slice(1))) {\n      return nok(code)\n    }\n\n    effects.enter('gfmFootnoteCallLabelMarker')\n    effects.consume(code)\n    effects.exit('gfmFootnoteCallLabelMarker')\n    return ok(code)\n  }\n}\n\n// To do: remove after micromark update.\n/** @type {Resolver} */\nfunction resolveToPotentialGfmFootnoteCall(events, context) {\n  let index = events.length\n  /** @type {Token | undefined} */\n  let labelStart\n\n  // Find an opening.\n  while (index--) {\n    if (\n      events[index][1].type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.types.labelImage &&\n      events[index][0] === 'enter'\n    ) {\n      labelStart = events[index][1]\n      break\n    }\n  }\n\n  (0,devlop__WEBPACK_IMPORTED_MODULE_2__.ok)(labelStart, 'expected `labelStart` to resolve')\n\n  // Change the `labelImageMarker` to a `data`.\n  events[index + 1][1].type = micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.types.data\n  events[index + 3][1].type = 'gfmFootnoteCallLabelMarker'\n\n  // The whole (without `!`):\n  /** @type {Token} */\n  const call = {\n    type: 'gfmFootnoteCall',\n    start: Object.assign({}, events[index + 3][1].start),\n    end: Object.assign({}, events[events.length - 1][1].end)\n  }\n  // The `^` marker\n  /** @type {Token} */\n  const marker = {\n    type: 'gfmFootnoteCallMarker',\n    start: Object.assign({}, events[index + 3][1].end),\n    end: Object.assign({}, events[index + 3][1].end)\n  }\n  // Increment the end 1 character.\n  marker.end.column++\n  marker.end.offset++\n  marker.end._bufferIndex++\n  /** @type {Token} */\n  const string = {\n    type: 'gfmFootnoteCallString',\n    start: Object.assign({}, marker.end),\n    end: Object.assign({}, events[events.length - 1][1].start)\n  }\n  /** @type {Token} */\n  const chunk = {\n    type: micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.types.chunkString,\n    contentType: 'string',\n    start: Object.assign({}, string.start),\n    end: Object.assign({}, string.end)\n  }\n\n  /** @type {Array<Event>} */\n  const replacement = [\n    // Take the `labelImageMarker` (now `data`, the `!`)\n    events[index + 1],\n    events[index + 2],\n    ['enter', call, context],\n    // The `[`\n    events[index + 3],\n    events[index + 4],\n    // The `^`.\n    ['enter', marker, context],\n    ['exit', marker, context],\n    // Everything in between.\n    ['enter', string, context],\n    ['enter', chunk, context],\n    ['exit', chunk, context],\n    ['exit', string, context],\n    // The ending (`]`, properly parsed and labelled).\n    events[events.length - 2],\n    events[events.length - 1],\n    ['exit', call, context]\n  ]\n\n  events.splice(index, events.length - index + 1, ...replacement)\n\n  return events\n}\n\n/**\n * @this {TokenizeContext}\n * @type {Tokenizer}\n */\nfunction tokenizeGfmFootnoteCall(effects, ok, nok) {\n  const self = this\n  const defined = self.parser.gfmFootnotes || (self.parser.gfmFootnotes = [])\n  let size = 0\n  /** @type {boolean} */\n  let data\n\n  // Note: the implementation of `markdown-rs` is different, because it houses\n  // core *and* extensions in one project.\n  // Therefore, it can include footnote logic inside `label-end`.\n  // We can’t do that, but luckily, we can parse footnotes in a simpler way than\n  // needed for labels.\n  return start\n\n  /**\n   * Start of footnote label.\n   *\n   * ```markdown\n   * > | a [^b] c\n   *       ^\n   * ```\n   *\n   * @type {State}\n   */\n  function start(code) {\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_2__.ok)(code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.leftSquareBracket, 'expected `[`')\n    effects.enter('gfmFootnoteCall')\n    effects.enter('gfmFootnoteCallLabelMarker')\n    effects.consume(code)\n    effects.exit('gfmFootnoteCallLabelMarker')\n    return callStart\n  }\n\n  /**\n   * After `[`, at `^`.\n   *\n   * ```markdown\n   * > | a [^b] c\n   *        ^\n   * ```\n   *\n   * @type {State}\n   */\n  function callStart(code) {\n    if (code !== micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.caret) return nok(code)\n\n    effects.enter('gfmFootnoteCallMarker')\n    effects.consume(code)\n    effects.exit('gfmFootnoteCallMarker')\n    effects.enter('gfmFootnoteCallString')\n    effects.enter('chunkString').contentType = 'string'\n    return callData\n  }\n\n  /**\n   * In label.\n   *\n   * ```markdown\n   * > | a [^b] c\n   *         ^\n   * ```\n   *\n   * @type {State}\n   */\n  function callData(code) {\n    if (\n      // Too long.\n      size > micromark_util_symbol__WEBPACK_IMPORTED_MODULE_4__.constants.linkReferenceSizeMax ||\n      // Closing brace with nothing.\n      (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.rightSquareBracket && !data) ||\n      // Space or tab is not supported by GFM for some reason.\n      // `\\n` and `[` not being supported makes sense.\n      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.eof ||\n      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.leftSquareBracket ||\n      (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_5__.markdownLineEndingOrSpace)(code)\n    ) {\n      return nok(code)\n    }\n\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.rightSquareBracket) {\n      effects.exit('chunkString')\n      const token = effects.exit('gfmFootnoteCallString')\n\n      if (!defined.includes((0,micromark_util_normalize_identifier__WEBPACK_IMPORTED_MODULE_3__.normalizeIdentifier)(self.sliceSerialize(token)))) {\n        return nok(code)\n      }\n\n      effects.enter('gfmFootnoteCallLabelMarker')\n      effects.consume(code)\n      effects.exit('gfmFootnoteCallLabelMarker')\n      effects.exit('gfmFootnoteCall')\n      return ok\n    }\n\n    if (!(0,micromark_util_character__WEBPACK_IMPORTED_MODULE_5__.markdownLineEndingOrSpace)(code)) {\n      data = true\n    }\n\n    size++\n    effects.consume(code)\n    return code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.backslash ? callEscape : callData\n  }\n\n  /**\n   * On character after escape.\n   *\n   * ```markdown\n   * > | a [^b\\c] d\n   *           ^\n   * ```\n   *\n   * @type {State}\n   */\n  function callEscape(code) {\n    if (\n      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.leftSquareBracket ||\n      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.backslash ||\n      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.rightSquareBracket\n    ) {\n      effects.consume(code)\n      size++\n      return callData\n    }\n\n    return callData(code)\n  }\n}\n\n/**\n * @this {TokenizeContext}\n * @type {Tokenizer}\n */\nfunction tokenizeDefinitionStart(effects, ok, nok) {\n  const self = this\n  const defined = self.parser.gfmFootnotes || (self.parser.gfmFootnotes = [])\n  /** @type {string} */\n  let identifier\n  let size = 0\n  /** @type {boolean | undefined} */\n  let data\n\n  return start\n\n  /**\n   * Start of GFM footnote definition.\n   *\n   * ```markdown\n   * > | [^a]: b\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function start(code) {\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_2__.ok)(code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.leftSquareBracket, 'expected `[`')\n    effects.enter('gfmFootnoteDefinition')._container = true\n    effects.enter('gfmFootnoteDefinitionLabel')\n    effects.enter('gfmFootnoteDefinitionLabelMarker')\n    effects.consume(code)\n    effects.exit('gfmFootnoteDefinitionLabelMarker')\n    return labelAtMarker\n  }\n\n  /**\n   * In label, at caret.\n   *\n   * ```markdown\n   * > | [^a]: b\n   *      ^\n   * ```\n   *\n   * @type {State}\n   */\n  function labelAtMarker(code) {\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.caret) {\n      effects.enter('gfmFootnoteDefinitionMarker')\n      effects.consume(code)\n      effects.exit('gfmFootnoteDefinitionMarker')\n      effects.enter('gfmFootnoteDefinitionLabelString')\n      effects.enter('chunkString').contentType = 'string'\n      return labelInside\n    }\n\n    return nok(code)\n  }\n\n  /**\n   * In label.\n   *\n   * > 👉 **Note**: `cmark-gfm` prevents whitespace from occurring in footnote\n   * > definition labels.\n   *\n   * ```markdown\n   * > | [^a]: b\n   *       ^\n   * ```\n   *\n   * @type {State}\n   */\n  function labelInside(code) {\n    if (\n      // Too long.\n      size > micromark_util_symbol__WEBPACK_IMPORTED_MODULE_4__.constants.linkReferenceSizeMax ||\n      // Closing brace with nothing.\n      (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.rightSquareBracket && !data) ||\n      // Space or tab is not supported by GFM for some reason.\n      // `\\n` and `[` not being supported makes sense.\n      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.eof ||\n      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.leftSquareBracket ||\n      (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_5__.markdownLineEndingOrSpace)(code)\n    ) {\n      return nok(code)\n    }\n\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.rightSquareBracket) {\n      effects.exit('chunkString')\n      const token = effects.exit('gfmFootnoteDefinitionLabelString')\n      identifier = (0,micromark_util_normalize_identifier__WEBPACK_IMPORTED_MODULE_3__.normalizeIdentifier)(self.sliceSerialize(token))\n      effects.enter('gfmFootnoteDefinitionLabelMarker')\n      effects.consume(code)\n      effects.exit('gfmFootnoteDefinitionLabelMarker')\n      effects.exit('gfmFootnoteDefinitionLabel')\n      return labelAfter\n    }\n\n    if (!(0,micromark_util_character__WEBPACK_IMPORTED_MODULE_5__.markdownLineEndingOrSpace)(code)) {\n      data = true\n    }\n\n    size++\n    effects.consume(code)\n    return code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.backslash ? labelEscape : labelInside\n  }\n\n  /**\n   * After `\\`, at a special character.\n   *\n   * > 👉 **Note**: `cmark-gfm` currently does not support escaped brackets:\n   * > <https://github.com/github/cmark-gfm/issues/240>\n   *\n   * ```markdown\n   * > | [^a\\*b]: c\n   *         ^\n   * ```\n   *\n   * @type {State}\n   */\n  function labelEscape(code) {\n    if (\n      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.leftSquareBracket ||\n      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.backslash ||\n      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.rightSquareBracket\n    ) {\n      effects.consume(code)\n      size++\n      return labelInside\n    }\n\n    return labelInside(code)\n  }\n\n  /**\n   * After definition label.\n   *\n   * ```markdown\n   * > | [^a]: b\n   *         ^\n   * ```\n   *\n   * @type {State}\n   */\n  function labelAfter(code) {\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.colon) {\n      effects.enter('definitionMarker')\n      effects.consume(code)\n      effects.exit('definitionMarker')\n\n      if (!defined.includes(identifier)) {\n        defined.push(identifier)\n      }\n\n      // Any whitespace after the marker is eaten, forming indented code\n      // is not possible.\n      // No space is also fine, just like a block quote marker.\n      return (0,micromark_factory_space__WEBPACK_IMPORTED_MODULE_6__.factorySpace)(\n        effects,\n        whitespaceAfter,\n        'gfmFootnoteDefinitionWhitespace'\n      )\n    }\n\n    return nok(code)\n  }\n\n  /**\n   * After definition prefix.\n   *\n   * ```markdown\n   * > | [^a]: b\n   *           ^\n   * ```\n   *\n   * @type {State}\n   */\n  function whitespaceAfter(code) {\n    // `markdown-rs` has a wrapping token for the prefix that is closed here.\n    return ok(code)\n  }\n}\n\n/**\n * @this {TokenizeContext}\n * @type {Tokenizer}\n */\nfunction tokenizeDefinitionContinuation(effects, ok, nok) {\n  /// Start of footnote definition continuation.\n  ///\n  /// ```markdown\n  ///   | [^a]: b\n  /// > |     c\n  ///     ^\n  /// ```\n  //\n  // Either a blank line, which is okay, or an indented thing.\n  return effects.check(micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_7__.blankLine, ok, effects.attempt(indent, ok, nok))\n}\n\n/** @type {Exiter} */\nfunction gfmFootnoteDefinitionEnd(effects) {\n  effects.exit('gfmFootnoteDefinition')\n}\n\n/**\n * @this {TokenizeContext}\n * @type {Tokenizer}\n */\nfunction tokenizeIndent(effects, ok, nok) {\n  const self = this\n\n  return (0,micromark_factory_space__WEBPACK_IMPORTED_MODULE_6__.factorySpace)(\n    effects,\n    afterPrefix,\n    'gfmFootnoteDefinitionIndent',\n    micromark_util_symbol__WEBPACK_IMPORTED_MODULE_4__.constants.tabSize + 1\n  )\n\n  /**\n   * @type {State}\n   */\n  function afterPrefix(code) {\n    const tail = self.events[self.events.length - 1]\n    return tail &&\n      tail[1].type === 'gfmFootnoteDefinitionIndent' &&\n      tail[2].sliceSerialize(tail[1], true).length === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_4__.constants.tabSize\n      ? ok(code)\n      : nok(code)\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/micromark-extension-gfm-footnote/dev/lib/syntax.js\n");

/***/ })

};
;