"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/micromark-extension-gfm-task-list-item@2.1.0";
exports.ids = ["vendor-chunks/micromark-extension-gfm-task-list-item@2.1.0"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/micromark-extension-gfm-task-list-item@2.1.0/node_modules/micromark-extension-gfm-task-list-item/dev/lib/html.js":
/*!*********************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/micromark-extension-gfm-task-list-item@2.1.0/node_modules/micromark-extension-gfm-task-list-item/dev/lib/html.js ***!
  \*********************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   gfmTaskListItemHtml: () => (/* binding */ gfmTaskListItemHtml)\n/* harmony export */ });\n/**\n * @import {HtmlExtension} from 'micromark-util-types'\n */\n\n/**\n * Create an HTML extension for `micromark` to support GFM task list items when\n * serializing to HTML.\n *\n * @returns {HtmlExtension}\n *   Extension for `micromark` that can be passed in `htmlExtensions` to\n *   support GFM task list items when serializing to HTML.\n */\nfunction gfmTaskListItemHtml() {\n  return {\n    enter: {\n      taskListCheck() {\n        this.tag('<input type=\"checkbox\" disabled=\"\" ')\n      }\n    },\n    exit: {\n      taskListCheck() {\n        this.tag('/>')\n      },\n      taskListCheckValueChecked() {\n        this.tag('checked=\"\" ')\n      }\n    }\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vbWljcm9tYXJrLWV4dGVuc2lvbi1nZm0tdGFzay1saXN0LWl0ZW1AMi4xLjAvbm9kZV9tb2R1bGVzL21pY3JvbWFyay1leHRlbnNpb24tZ2ZtLXRhc2stbGlzdC1pdGVtL2Rldi9saWIvaHRtbC5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQSxZQUFZLGVBQWU7QUFDM0I7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQSxhQUFhO0FBQ2I7QUFDQTtBQUNBO0FBQ087QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBLE9BQU87QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcR3VlcnJcXERvd25sb2Fkc1xcYWktZGV2LWVjb3N5c3RlbVxcbm9kZV9tb2R1bGVzXFwucG5wbVxcbWljcm9tYXJrLWV4dGVuc2lvbi1nZm0tdGFzay1saXN0LWl0ZW1AMi4xLjBcXG5vZGVfbW9kdWxlc1xcbWljcm9tYXJrLWV4dGVuc2lvbi1nZm0tdGFzay1saXN0LWl0ZW1cXGRldlxcbGliXFxodG1sLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQGltcG9ydCB7SHRtbEV4dGVuc2lvbn0gZnJvbSAnbWljcm9tYXJrLXV0aWwtdHlwZXMnXG4gKi9cblxuLyoqXG4gKiBDcmVhdGUgYW4gSFRNTCBleHRlbnNpb24gZm9yIGBtaWNyb21hcmtgIHRvIHN1cHBvcnQgR0ZNIHRhc2sgbGlzdCBpdGVtcyB3aGVuXG4gKiBzZXJpYWxpemluZyB0byBIVE1MLlxuICpcbiAqIEByZXR1cm5zIHtIdG1sRXh0ZW5zaW9ufVxuICogICBFeHRlbnNpb24gZm9yIGBtaWNyb21hcmtgIHRoYXQgY2FuIGJlIHBhc3NlZCBpbiBgaHRtbEV4dGVuc2lvbnNgIHRvXG4gKiAgIHN1cHBvcnQgR0ZNIHRhc2sgbGlzdCBpdGVtcyB3aGVuIHNlcmlhbGl6aW5nIHRvIEhUTUwuXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBnZm1UYXNrTGlzdEl0ZW1IdG1sKCkge1xuICByZXR1cm4ge1xuICAgIGVudGVyOiB7XG4gICAgICB0YXNrTGlzdENoZWNrKCkge1xuICAgICAgICB0aGlzLnRhZygnPGlucHV0IHR5cGU9XCJjaGVja2JveFwiIGRpc2FibGVkPVwiXCIgJylcbiAgICAgIH1cbiAgICB9LFxuICAgIGV4aXQ6IHtcbiAgICAgIHRhc2tMaXN0Q2hlY2soKSB7XG4gICAgICAgIHRoaXMudGFnKCcvPicpXG4gICAgICB9LFxuICAgICAgdGFza0xpc3RDaGVja1ZhbHVlQ2hlY2tlZCgpIHtcbiAgICAgICAgdGhpcy50YWcoJ2NoZWNrZWQ9XCJcIiAnKVxuICAgICAgfVxuICAgIH1cbiAgfVxufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/micromark-extension-gfm-task-list-item@2.1.0/node_modules/micromark-extension-gfm-task-list-item/dev/lib/html.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/micromark-extension-gfm-task-list-item@2.1.0/node_modules/micromark-extension-gfm-task-list-item/dev/lib/syntax.js":
/*!***********************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/micromark-extension-gfm-task-list-item@2.1.0/node_modules/micromark-extension-gfm-task-list-item/dev/lib/syntax.js ***!
  \***********************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   gfmTaskListItem: () => (/* binding */ gfmTaskListItem)\n/* harmony export */ });\n/* harmony import */ var devlop__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! devlop */ \"(ssr)/./node_modules/.pnpm/devlop@1.1.0/node_modules/devlop/lib/development.js\");\n/* harmony import */ var micromark_factory_space__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! micromark-factory-space */ \"(ssr)/./node_modules/.pnpm/micromark-factory-space@2.0.1/node_modules/micromark-factory-space/dev/index.js\");\n/* harmony import */ var micromark_util_character__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! micromark-util-character */ \"(ssr)/./node_modules/.pnpm/micromark-util-character@2.1.1/node_modules/micromark-util-character/dev/index.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! micromark-util-symbol */ \"(ssr)/./node_modules/.pnpm/micromark-util-symbol@2.0.1/node_modules/micromark-util-symbol/lib/codes.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! micromark-util-symbol */ \"(ssr)/./node_modules/.pnpm/micromark-util-symbol@2.0.1/node_modules/micromark-util-symbol/lib/types.js\");\n/**\n * @import {Extension, State, TokenizeContext, Tokenizer} from 'micromark-util-types'\n */\n\n\n\n\n\n\nconst tasklistCheck = {name: 'tasklistCheck', tokenize: tokenizeTasklistCheck}\n\n/**\n * Create an HTML extension for `micromark` to support GFM task list items\n * syntax.\n *\n * @returns {Extension}\n *   Extension for `micromark` that can be passed in `htmlExtensions` to\n *   support GFM task list items when serializing to HTML.\n */\nfunction gfmTaskListItem() {\n  return {\n    text: {[micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.leftSquareBracket]: tasklistCheck}\n  }\n}\n\n/**\n * @this {TokenizeContext}\n * @type {Tokenizer}\n */\nfunction tokenizeTasklistCheck(effects, ok, nok) {\n  const self = this\n\n  return open\n\n  /**\n   * At start of task list item check.\n   *\n   * ```markdown\n   * > | * [x] y.\n   *       ^\n   * ```\n   *\n   * @type {State}\n   */\n  function open(code) {\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_1__.ok)(code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.leftSquareBracket, 'expected `[`')\n\n    if (\n      // Exit if there’s stuff before.\n      self.previous !== micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.eof ||\n      // Exit if not in the first content that is the first child of a list\n      // item.\n      !self._gfmTasklistFirstContentOfListItem\n    ) {\n      return nok(code)\n    }\n\n    effects.enter('taskListCheck')\n    effects.enter('taskListCheckMarker')\n    effects.consume(code)\n    effects.exit('taskListCheckMarker')\n    return inside\n  }\n\n  /**\n   * In task list item check.\n   *\n   * ```markdown\n   * > | * [x] y.\n   *        ^\n   * ```\n   *\n   * @type {State}\n   */\n  function inside(code) {\n    // Currently we match how GH works in files.\n    // To match how GH works in comments, use `markdownSpace` (`[\\t ]`) instead\n    // of `markdownLineEndingOrSpace` (`[\\t\\n\\r ]`).\n    if ((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_2__.markdownLineEndingOrSpace)(code)) {\n      effects.enter('taskListCheckValueUnchecked')\n      effects.consume(code)\n      effects.exit('taskListCheckValueUnchecked')\n      return close\n    }\n\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.uppercaseX || code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.lowercaseX) {\n      effects.enter('taskListCheckValueChecked')\n      effects.consume(code)\n      effects.exit('taskListCheckValueChecked')\n      return close\n    }\n\n    return nok(code)\n  }\n\n  /**\n   * At close of task list item check.\n   *\n   * ```markdown\n   * > | * [x] y.\n   *         ^\n   * ```\n   *\n   * @type {State}\n   */\n  function close(code) {\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.rightSquareBracket) {\n      effects.enter('taskListCheckMarker')\n      effects.consume(code)\n      effects.exit('taskListCheckMarker')\n      effects.exit('taskListCheck')\n      return after\n    }\n\n    return nok(code)\n  }\n\n  /**\n   * @type {State}\n   */\n  function after(code) {\n    // EOL in paragraph means there must be something else after it.\n    if ((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_2__.markdownLineEnding)(code)) {\n      return ok(code)\n    }\n\n    // Space or tab?\n    // Check what comes after.\n    if ((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_2__.markdownSpace)(code)) {\n      return effects.check({tokenize: spaceThenNonSpace}, ok, nok)(code)\n    }\n\n    // EOF, or non-whitespace, both wrong.\n    return nok(code)\n  }\n}\n\n/**\n * @this {TokenizeContext}\n * @type {Tokenizer}\n */\nfunction spaceThenNonSpace(effects, ok, nok) {\n  return (0,micromark_factory_space__WEBPACK_IMPORTED_MODULE_3__.factorySpace)(effects, after, micromark_util_symbol__WEBPACK_IMPORTED_MODULE_4__.types.whitespace)\n\n  /**\n   * After whitespace, after task list item check.\n   *\n   * ```markdown\n   * > | * [x] y.\n   *           ^\n   * ```\n   *\n   * @type {State}\n   */\n  function after(code) {\n    // EOF means there was nothing, so bad.\n    // EOL means there’s content after it, so good.\n    // Impossible to have more spaces.\n    // Anything else is good.\n    return code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.eof ? nok(code) : ok(code)\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/micromark-extension-gfm-task-list-item@2.1.0/node_modules/micromark-extension-gfm-task-list-item/dev/lib/syntax.js\n");

/***/ })

};
;