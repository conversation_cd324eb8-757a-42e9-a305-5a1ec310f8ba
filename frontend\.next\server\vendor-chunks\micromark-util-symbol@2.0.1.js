"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/micromark-util-symbol@2.0.1";
exports.ids = ["vendor-chunks/micromark-util-symbol@2.0.1"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/micromark-util-symbol@2.0.1/node_modules/micromark-util-symbol/lib/codes.js":
/*!********************************************************************************************************!*\
  !*** ./node_modules/.pnpm/micromark-util-symbol@2.0.1/node_modules/micromark-util-symbol/lib/codes.js ***!
  \********************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   codes: () => (/* binding */ codes)\n/* harmony export */ });\n/**\n * Character codes.\n *\n * This module is compiled away!\n *\n * micromark works based on character codes.\n * This module contains constants for the ASCII block and the replacement\n * character.\n * A couple of them are handled in a special way, such as the line endings\n * (CR, LF, and CR+LF, commonly known as end-of-line: EOLs), the tab (horizontal\n * tab) and its expansion based on what column it’s at (virtual space),\n * and the end-of-file (eof) character.\n * As values are preprocessed before handling them, the actual characters LF,\n * CR, HT, and NUL (which is present as the replacement character), are\n * guaranteed to not exist.\n *\n * Unicode basic latin block.\n */\nconst codes = /** @type {const} */ ({\n  carriageReturn: -5,\n  lineFeed: -4,\n  carriageReturnLineFeed: -3,\n  horizontalTab: -2,\n  virtualSpace: -1,\n  eof: null,\n  nul: 0,\n  soh: 1,\n  stx: 2,\n  etx: 3,\n  eot: 4,\n  enq: 5,\n  ack: 6,\n  bel: 7,\n  bs: 8,\n  ht: 9, // `\\t`\n  lf: 10, // `\\n`\n  vt: 11, // `\\v`\n  ff: 12, // `\\f`\n  cr: 13, // `\\r`\n  so: 14,\n  si: 15,\n  dle: 16,\n  dc1: 17,\n  dc2: 18,\n  dc3: 19,\n  dc4: 20,\n  nak: 21,\n  syn: 22,\n  etb: 23,\n  can: 24,\n  em: 25,\n  sub: 26,\n  esc: 27,\n  fs: 28,\n  gs: 29,\n  rs: 30,\n  us: 31,\n  space: 32,\n  exclamationMark: 33, // `!`\n  quotationMark: 34, // `\"`\n  numberSign: 35, // `#`\n  dollarSign: 36, // `$`\n  percentSign: 37, // `%`\n  ampersand: 38, // `&`\n  apostrophe: 39, // `'`\n  leftParenthesis: 40, // `(`\n  rightParenthesis: 41, // `)`\n  asterisk: 42, // `*`\n  plusSign: 43, // `+`\n  comma: 44, // `,`\n  dash: 45, // `-`\n  dot: 46, // `.`\n  slash: 47, // `/`\n  digit0: 48, // `0`\n  digit1: 49, // `1`\n  digit2: 50, // `2`\n  digit3: 51, // `3`\n  digit4: 52, // `4`\n  digit5: 53, // `5`\n  digit6: 54, // `6`\n  digit7: 55, // `7`\n  digit8: 56, // `8`\n  digit9: 57, // `9`\n  colon: 58, // `:`\n  semicolon: 59, // `;`\n  lessThan: 60, // `<`\n  equalsTo: 61, // `=`\n  greaterThan: 62, // `>`\n  questionMark: 63, // `?`\n  atSign: 64, // `@`\n  uppercaseA: 65, // `A`\n  uppercaseB: 66, // `B`\n  uppercaseC: 67, // `C`\n  uppercaseD: 68, // `D`\n  uppercaseE: 69, // `E`\n  uppercaseF: 70, // `F`\n  uppercaseG: 71, // `G`\n  uppercaseH: 72, // `H`\n  uppercaseI: 73, // `I`\n  uppercaseJ: 74, // `J`\n  uppercaseK: 75, // `K`\n  uppercaseL: 76, // `L`\n  uppercaseM: 77, // `M`\n  uppercaseN: 78, // `N`\n  uppercaseO: 79, // `O`\n  uppercaseP: 80, // `P`\n  uppercaseQ: 81, // `Q`\n  uppercaseR: 82, // `R`\n  uppercaseS: 83, // `S`\n  uppercaseT: 84, // `T`\n  uppercaseU: 85, // `U`\n  uppercaseV: 86, // `V`\n  uppercaseW: 87, // `W`\n  uppercaseX: 88, // `X`\n  uppercaseY: 89, // `Y`\n  uppercaseZ: 90, // `Z`\n  leftSquareBracket: 91, // `[`\n  backslash: 92, // `\\`\n  rightSquareBracket: 93, // `]`\n  caret: 94, // `^`\n  underscore: 95, // `_`\n  graveAccent: 96, // `` ` ``\n  lowercaseA: 97, // `a`\n  lowercaseB: 98, // `b`\n  lowercaseC: 99, // `c`\n  lowercaseD: 100, // `d`\n  lowercaseE: 101, // `e`\n  lowercaseF: 102, // `f`\n  lowercaseG: 103, // `g`\n  lowercaseH: 104, // `h`\n  lowercaseI: 105, // `i`\n  lowercaseJ: 106, // `j`\n  lowercaseK: 107, // `k`\n  lowercaseL: 108, // `l`\n  lowercaseM: 109, // `m`\n  lowercaseN: 110, // `n`\n  lowercaseO: 111, // `o`\n  lowercaseP: 112, // `p`\n  lowercaseQ: 113, // `q`\n  lowercaseR: 114, // `r`\n  lowercaseS: 115, // `s`\n  lowercaseT: 116, // `t`\n  lowercaseU: 117, // `u`\n  lowercaseV: 118, // `v`\n  lowercaseW: 119, // `w`\n  lowercaseX: 120, // `x`\n  lowercaseY: 121, // `y`\n  lowercaseZ: 122, // `z`\n  leftCurlyBrace: 123, // `{`\n  verticalBar: 124, // `|`\n  rightCurlyBrace: 125, // `}`\n  tilde: 126, // `~`\n  del: 127,\n  // Unicode Specials block.\n  byteOrderMarker: 65_279,\n  // Unicode Specials block.\n  replacementCharacter: 65_533 // `�`\n})\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/micromark-util-symbol@2.0.1/node_modules/micromark-util-symbol/lib/codes.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/micromark-util-symbol@2.0.1/node_modules/micromark-util-symbol/lib/constants.js":
/*!************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/micromark-util-symbol@2.0.1/node_modules/micromark-util-symbol/lib/constants.js ***!
  \************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   constants: () => (/* binding */ constants)\n/* harmony export */ });\n/**\n * This module is compiled away!\n *\n * Parsing markdown comes with a couple of constants, such as minimum or maximum\n * sizes of certain sequences.\n * Additionally, there are a couple symbols used inside micromark.\n * These are all defined here, but compiled away by scripts.\n */\nconst constants = /** @type {const} */ ({\n  attentionSideAfter: 2, // Symbol to mark an attention sequence as after content: `a*`\n  attentionSideBefore: 1, // Symbol to mark an attention sequence as before content: `*a`\n  atxHeadingOpeningFenceSizeMax: 6, // 6 number signs is fine, 7 isn’t.\n  autolinkDomainSizeMax: 63, // 63 characters is fine, 64 is too many.\n  autolinkSchemeSizeMax: 32, // 32 characters is fine, 33 is too many.\n  cdataOpeningString: 'CDATA[', // And preceded by `<![`.\n  characterGroupPunctuation: 2, // Symbol used to indicate a character is punctuation\n  characterGroupWhitespace: 1, // Symbol used to indicate a character is whitespace\n  characterReferenceDecimalSizeMax: 7, // `&#9999999;`.\n  characterReferenceHexadecimalSizeMax: 6, // `&#xff9999;`.\n  characterReferenceNamedSizeMax: 31, // `&CounterClockwiseContourIntegral;`.\n  codeFencedSequenceSizeMin: 3, // At least 3 ticks or tildes are needed.\n  contentTypeContent: 'content',\n  contentTypeDocument: 'document',\n  contentTypeFlow: 'flow',\n  contentTypeString: 'string',\n  contentTypeText: 'text',\n  hardBreakPrefixSizeMin: 2, // At least 2 trailing spaces are needed.\n  htmlBasic: 6, // Symbol for `<div`\n  htmlCdata: 5, // Symbol for `<![CDATA[]]>`\n  htmlComment: 2, // Symbol for `<!---->`\n  htmlComplete: 7, // Symbol for `<x>`\n  htmlDeclaration: 4, // Symbol for `<!doctype>`\n  htmlInstruction: 3, // Symbol for `<?php?>`\n  htmlRawSizeMax: 8, // Length of `textarea`.\n  htmlRaw: 1, // Symbol for `<script>`\n  linkResourceDestinationBalanceMax: 32, // See: <https://spec.commonmark.org/0.30/#link-destination>, <https://github.com/remarkjs/react-markdown/issues/658#issuecomment-984345577>\n  linkReferenceSizeMax: 999, // See: <https://spec.commonmark.org/0.30/#link-label>\n  listItemValueSizeMax: 10, // See: <https://spec.commonmark.org/0.30/#ordered-list-marker>\n  numericBaseDecimal: 10,\n  numericBaseHexadecimal: 0x10,\n  tabSize: 4, // Tabs have a hard-coded size of 4, per CommonMark.\n  thematicBreakMarkerCountMin: 3, // At least 3 asterisks, dashes, or underscores are needed.\n  v8MaxSafeChunkSize: 10_000 // V8 (and potentially others) have problems injecting giant arrays into other arrays, hence we operate in chunks.\n})\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/micromark-util-symbol@2.0.1/node_modules/micromark-util-symbol/lib/constants.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/micromark-util-symbol@2.0.1/node_modules/micromark-util-symbol/lib/types.js":
/*!********************************************************************************************************!*\
  !*** ./node_modules/.pnpm/micromark-util-symbol@2.0.1/node_modules/micromark-util-symbol/lib/types.js ***!
  \********************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   types: () => (/* binding */ types)\n/* harmony export */ });\n/**\n * This module is compiled away!\n *\n * Here is the list of all types of tokens exposed by micromark, with a short\n * explanation of what they include and where they are found.\n * In picking names, generally, the rule is to be as explicit as possible\n * instead of reusing names.\n * For example, there is a `definitionDestination` and a `resourceDestination`,\n * instead of one shared name.\n */\n\n// Note: when changing the next record, you must also change `TokenTypeMap`\n// in `micromark-util-types/index.d.ts`.\nconst types = /** @type {const} */ ({\n  // Generic type for data, such as in a title, a destination, etc.\n  data: 'data',\n\n  // Generic type for syntactic whitespace (tabs, virtual spaces, spaces).\n  // Such as, between a fenced code fence and an info string.\n  whitespace: 'whitespace',\n\n  // Generic type for line endings (line feed, carriage return, carriage return +\n  // line feed).\n  lineEnding: 'lineEnding',\n\n  // A line ending, but ending a blank line.\n  lineEndingBlank: 'lineEndingBlank',\n\n  // Generic type for whitespace (tabs, virtual spaces, spaces) at the start of a\n  // line.\n  linePrefix: 'linePrefix',\n\n  // Generic type for whitespace (tabs, virtual spaces, spaces) at the end of a\n  // line.\n  lineSuffix: 'lineSuffix',\n\n  // Whole ATX heading:\n  //\n  // ```markdown\n  // #\n  // ## Alpha\n  // ### Bravo ###\n  // ```\n  //\n  // Includes `atxHeadingSequence`, `whitespace`, `atxHeadingText`.\n  atxHeading: 'atxHeading',\n\n  // Sequence of number signs in an ATX heading (`###`).\n  atxHeadingSequence: 'atxHeadingSequence',\n\n  // Content in an ATX heading (`alpha`).\n  // Includes text.\n  atxHeadingText: 'atxHeadingText',\n\n  // Whole autolink (`<https://example.com>` or `<<EMAIL>>`)\n  // Includes `autolinkMarker` and `autolinkProtocol` or `autolinkEmail`.\n  autolink: 'autolink',\n\n  // Email autolink w/o markers (`<EMAIL>`)\n  autolinkEmail: 'autolinkEmail',\n\n  // Marker around an `autolinkProtocol` or `autolinkEmail` (`<` or `>`).\n  autolinkMarker: 'autolinkMarker',\n\n  // Protocol autolink w/o markers (`https://example.com`)\n  autolinkProtocol: 'autolinkProtocol',\n\n  // A whole character escape (`\\-`).\n  // Includes `escapeMarker` and `characterEscapeValue`.\n  characterEscape: 'characterEscape',\n\n  // The escaped character (`-`).\n  characterEscapeValue: 'characterEscapeValue',\n\n  // A whole character reference (`&amp;`, `&#8800;`, or `&#x1D306;`).\n  // Includes `characterReferenceMarker`, an optional\n  // `characterReferenceMarkerNumeric`, in which case an optional\n  // `characterReferenceMarkerHexadecimal`, and a `characterReferenceValue`.\n  characterReference: 'characterReference',\n\n  // The start or end marker (`&` or `;`).\n  characterReferenceMarker: 'characterReferenceMarker',\n\n  // Mark reference as numeric (`#`).\n  characterReferenceMarkerNumeric: 'characterReferenceMarkerNumeric',\n\n  // Mark reference as numeric (`x` or `X`).\n  characterReferenceMarkerHexadecimal: 'characterReferenceMarkerHexadecimal',\n\n  // Value of character reference w/o markers (`amp`, `8800`, or `1D306`).\n  characterReferenceValue: 'characterReferenceValue',\n\n  // Whole fenced code:\n  //\n  // ````markdown\n  // ```js\n  // alert(1)\n  // ```\n  // ````\n  codeFenced: 'codeFenced',\n\n  // A fenced code fence, including whitespace, sequence, info, and meta\n  // (` ```js `).\n  codeFencedFence: 'codeFencedFence',\n\n  // Sequence of grave accent or tilde characters (` ``` `) in a fence.\n  codeFencedFenceSequence: 'codeFencedFenceSequence',\n\n  // Info word (`js`) in a fence.\n  // Includes string.\n  codeFencedFenceInfo: 'codeFencedFenceInfo',\n\n  // Meta words (`highlight=\"1\"`) in a fence.\n  // Includes string.\n  codeFencedFenceMeta: 'codeFencedFenceMeta',\n\n  // A line of code.\n  codeFlowValue: 'codeFlowValue',\n\n  // Whole indented code:\n  //\n  // ```markdown\n  //     alert(1)\n  // ```\n  //\n  // Includes `lineEnding`, `linePrefix`, and `codeFlowValue`.\n  codeIndented: 'codeIndented',\n\n  // A text code (``` `alpha` ```).\n  // Includes `codeTextSequence`, `codeTextData`, `lineEnding`, and can include\n  // `codeTextPadding`.\n  codeText: 'codeText',\n\n  codeTextData: 'codeTextData',\n\n  // A space or line ending right after or before a tick.\n  codeTextPadding: 'codeTextPadding',\n\n  // A text code fence (` `` `).\n  codeTextSequence: 'codeTextSequence',\n\n  // Whole content:\n  //\n  // ```markdown\n  // [a]: b\n  // c\n  // =\n  // d\n  // ```\n  //\n  // Includes `paragraph` and `definition`.\n  content: 'content',\n  // Whole definition:\n  //\n  // ```markdown\n  // [micromark]: https://github.com/micromark/micromark\n  // ```\n  //\n  // Includes `definitionLabel`, `definitionMarker`, `whitespace`,\n  // `definitionDestination`, and optionally `lineEnding` and `definitionTitle`.\n  definition: 'definition',\n\n  // Destination of a definition (`https://github.com/micromark/micromark` or\n  // `<https://github.com/micromark/micromark>`).\n  // Includes `definitionDestinationLiteral` or `definitionDestinationRaw`.\n  definitionDestination: 'definitionDestination',\n\n  // Enclosed destination of a definition\n  // (`<https://github.com/micromark/micromark>`).\n  // Includes `definitionDestinationLiteralMarker` and optionally\n  // `definitionDestinationString`.\n  definitionDestinationLiteral: 'definitionDestinationLiteral',\n\n  // Markers of an enclosed definition destination (`<` or `>`).\n  definitionDestinationLiteralMarker: 'definitionDestinationLiteralMarker',\n\n  // Unenclosed destination of a definition\n  // (`https://github.com/micromark/micromark`).\n  // Includes `definitionDestinationString`.\n  definitionDestinationRaw: 'definitionDestinationRaw',\n\n  // Text in an destination (`https://github.com/micromark/micromark`).\n  // Includes string.\n  definitionDestinationString: 'definitionDestinationString',\n\n  // Label of a definition (`[micromark]`).\n  // Includes `definitionLabelMarker` and `definitionLabelString`.\n  definitionLabel: 'definitionLabel',\n\n  // Markers of a definition label (`[` or `]`).\n  definitionLabelMarker: 'definitionLabelMarker',\n\n  // Value of a definition label (`micromark`).\n  // Includes string.\n  definitionLabelString: 'definitionLabelString',\n\n  // Marker between a label and a destination (`:`).\n  definitionMarker: 'definitionMarker',\n\n  // Title of a definition (`\"x\"`, `'y'`, or `(z)`).\n  // Includes `definitionTitleMarker` and optionally `definitionTitleString`.\n  definitionTitle: 'definitionTitle',\n\n  // Marker around a title of a definition (`\"`, `'`, `(`, or `)`).\n  definitionTitleMarker: 'definitionTitleMarker',\n\n  // Data without markers in a title (`z`).\n  // Includes string.\n  definitionTitleString: 'definitionTitleString',\n\n  // Emphasis (`*alpha*`).\n  // Includes `emphasisSequence` and `emphasisText`.\n  emphasis: 'emphasis',\n\n  // Sequence of emphasis markers (`*` or `_`).\n  emphasisSequence: 'emphasisSequence',\n\n  // Emphasis text (`alpha`).\n  // Includes text.\n  emphasisText: 'emphasisText',\n\n  // The character escape marker (`\\`).\n  escapeMarker: 'escapeMarker',\n\n  // A hard break created with a backslash (`\\\\n`).\n  // Note: does not include the line ending.\n  hardBreakEscape: 'hardBreakEscape',\n\n  // A hard break created with trailing spaces (`  \\n`).\n  // Does not include the line ending.\n  hardBreakTrailing: 'hardBreakTrailing',\n\n  // Flow HTML:\n  //\n  // ```markdown\n  // <div\n  // ```\n  //\n  // Inlcudes `lineEnding`, `htmlFlowData`.\n  htmlFlow: 'htmlFlow',\n\n  htmlFlowData: 'htmlFlowData',\n\n  // HTML in text (the tag in `a <i> b`).\n  // Includes `lineEnding`, `htmlTextData`.\n  htmlText: 'htmlText',\n\n  htmlTextData: 'htmlTextData',\n\n  // Whole image (`![alpha](bravo)`, `![alpha][bravo]`, `![alpha][]`, or\n  // `![alpha]`).\n  // Includes `label` and an optional `resource` or `reference`.\n  image: 'image',\n\n  // Whole link label (`[*alpha*]`).\n  // Includes `labelLink` or `labelImage`, `labelText`, and `labelEnd`.\n  label: 'label',\n\n  // Text in an label (`*alpha*`).\n  // Includes text.\n  labelText: 'labelText',\n\n  // Start a link label (`[`).\n  // Includes a `labelMarker`.\n  labelLink: 'labelLink',\n\n  // Start an image label (`![`).\n  // Includes `labelImageMarker` and `labelMarker`.\n  labelImage: 'labelImage',\n\n  // Marker of a label (`[` or `]`).\n  labelMarker: 'labelMarker',\n\n  // Marker to start an image (`!`).\n  labelImageMarker: 'labelImageMarker',\n\n  // End a label (`]`).\n  // Includes `labelMarker`.\n  labelEnd: 'labelEnd',\n\n  // Whole link (`[alpha](bravo)`, `[alpha][bravo]`, `[alpha][]`, or `[alpha]`).\n  // Includes `label` and an optional `resource` or `reference`.\n  link: 'link',\n\n  // Whole paragraph:\n  //\n  // ```markdown\n  // alpha\n  // bravo.\n  // ```\n  //\n  // Includes text.\n  paragraph: 'paragraph',\n\n  // A reference (`[alpha]` or `[]`).\n  // Includes `referenceMarker` and an optional `referenceString`.\n  reference: 'reference',\n\n  // A reference marker (`[` or `]`).\n  referenceMarker: 'referenceMarker',\n\n  // Reference text (`alpha`).\n  // Includes string.\n  referenceString: 'referenceString',\n\n  // A resource (`(https://example.com \"alpha\")`).\n  // Includes `resourceMarker`, an optional `resourceDestination` with an optional\n  // `whitespace` and `resourceTitle`.\n  resource: 'resource',\n\n  // A resource destination (`https://example.com`).\n  // Includes `resourceDestinationLiteral` or `resourceDestinationRaw`.\n  resourceDestination: 'resourceDestination',\n\n  // A literal resource destination (`<https://example.com>`).\n  // Includes `resourceDestinationLiteralMarker` and optionally\n  // `resourceDestinationString`.\n  resourceDestinationLiteral: 'resourceDestinationLiteral',\n\n  // A resource destination marker (`<` or `>`).\n  resourceDestinationLiteralMarker: 'resourceDestinationLiteralMarker',\n\n  // A raw resource destination (`https://example.com`).\n  // Includes `resourceDestinationString`.\n  resourceDestinationRaw: 'resourceDestinationRaw',\n\n  // Resource destination text (`https://example.com`).\n  // Includes string.\n  resourceDestinationString: 'resourceDestinationString',\n\n  // A resource marker (`(` or `)`).\n  resourceMarker: 'resourceMarker',\n\n  // A resource title (`\"alpha\"`, `'alpha'`, or `(alpha)`).\n  // Includes `resourceTitleMarker` and optionally `resourceTitleString`.\n  resourceTitle: 'resourceTitle',\n\n  // A resource title marker (`\"`, `'`, `(`, or `)`).\n  resourceTitleMarker: 'resourceTitleMarker',\n\n  // Resource destination title (`alpha`).\n  // Includes string.\n  resourceTitleString: 'resourceTitleString',\n\n  // Whole setext heading:\n  //\n  // ```markdown\n  // alpha\n  // bravo\n  // =====\n  // ```\n  //\n  // Includes `setextHeadingText`, `lineEnding`, `linePrefix`, and\n  // `setextHeadingLine`.\n  setextHeading: 'setextHeading',\n\n  // Content in a setext heading (`alpha\\nbravo`).\n  // Includes text.\n  setextHeadingText: 'setextHeadingText',\n\n  // Underline in a setext heading, including whitespace suffix (`==`).\n  // Includes `setextHeadingLineSequence`.\n  setextHeadingLine: 'setextHeadingLine',\n\n  // Sequence of equals or dash characters in underline in a setext heading (`-`).\n  setextHeadingLineSequence: 'setextHeadingLineSequence',\n\n  // Strong (`**alpha**`).\n  // Includes `strongSequence` and `strongText`.\n  strong: 'strong',\n\n  // Sequence of strong markers (`**` or `__`).\n  strongSequence: 'strongSequence',\n\n  // Strong text (`alpha`).\n  // Includes text.\n  strongText: 'strongText',\n\n  // Whole thematic break:\n  //\n  // ```markdown\n  // * * *\n  // ```\n  //\n  // Includes `thematicBreakSequence` and `whitespace`.\n  thematicBreak: 'thematicBreak',\n\n  // A sequence of one or more thematic break markers (`***`).\n  thematicBreakSequence: 'thematicBreakSequence',\n\n  // Whole block quote:\n  //\n  // ```markdown\n  // > a\n  // >\n  // > b\n  // ```\n  //\n  // Includes `blockQuotePrefix` and flow.\n  blockQuote: 'blockQuote',\n  // The `>` or `> ` of a block quote.\n  blockQuotePrefix: 'blockQuotePrefix',\n  // The `>` of a block quote prefix.\n  blockQuoteMarker: 'blockQuoteMarker',\n  // The optional ` ` of a block quote prefix.\n  blockQuotePrefixWhitespace: 'blockQuotePrefixWhitespace',\n\n  // Whole ordered list:\n  //\n  // ```markdown\n  // 1. a\n  //    b\n  // ```\n  //\n  // Includes `listItemPrefix`, flow, and optionally  `listItemIndent` on further\n  // lines.\n  listOrdered: 'listOrdered',\n\n  // Whole unordered list:\n  //\n  // ```markdown\n  // - a\n  //   b\n  // ```\n  //\n  // Includes `listItemPrefix`, flow, and optionally  `listItemIndent` on further\n  // lines.\n  listUnordered: 'listUnordered',\n\n  // The indent of further list item lines.\n  listItemIndent: 'listItemIndent',\n\n  // A marker, as in, `*`, `+`, `-`, `.`, or `)`.\n  listItemMarker: 'listItemMarker',\n\n  // The thing that starts a list item, such as `1. `.\n  // Includes `listItemValue` if ordered, `listItemMarker`, and\n  // `listItemPrefixWhitespace` (unless followed by a line ending).\n  listItemPrefix: 'listItemPrefix',\n\n  // The whitespace after a marker.\n  listItemPrefixWhitespace: 'listItemPrefixWhitespace',\n\n  // The numerical value of an ordered item.\n  listItemValue: 'listItemValue',\n\n  // Internal types used for subtokenizers, compiled away\n  chunkDocument: 'chunkDocument',\n  chunkContent: 'chunkContent',\n  chunkFlow: 'chunkFlow',\n  chunkText: 'chunkText',\n  chunkString: 'chunkString'\n})\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/micromark-util-symbol@2.0.1/node_modules/micromark-util-symbol/lib/types.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/micromark-util-symbol@2.0.1/node_modules/micromark-util-symbol/lib/values.js":
/*!*********************************************************************************************************!*\
  !*** ./node_modules/.pnpm/micromark-util-symbol@2.0.1/node_modules/micromark-util-symbol/lib/values.js ***!
  \*********************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   values: () => (/* binding */ values)\n/* harmony export */ });\n/**\n * This module is compiled away!\n *\n * While micromark works based on character codes, this module includes the\n * string versions of ’em.\n * The C0 block, except for LF, CR, HT, and w/ the replacement character added,\n * are available here.\n */\nconst values = /** @type {const} */ ({\n  ht: '\\t',\n  lf: '\\n',\n  cr: '\\r',\n  space: ' ',\n  exclamationMark: '!',\n  quotationMark: '\"',\n  numberSign: '#',\n  dollarSign: '$',\n  percentSign: '%',\n  ampersand: '&',\n  apostrophe: \"'\",\n  leftParenthesis: '(',\n  rightParenthesis: ')',\n  asterisk: '*',\n  plusSign: '+',\n  comma: ',',\n  dash: '-',\n  dot: '.',\n  slash: '/',\n  digit0: '0',\n  digit1: '1',\n  digit2: '2',\n  digit3: '3',\n  digit4: '4',\n  digit5: '5',\n  digit6: '6',\n  digit7: '7',\n  digit8: '8',\n  digit9: '9',\n  colon: ':',\n  semicolon: ';',\n  lessThan: '<',\n  equalsTo: '=',\n  greaterThan: '>',\n  questionMark: '?',\n  atSign: '@',\n  uppercaseA: 'A',\n  uppercaseB: 'B',\n  uppercaseC: 'C',\n  uppercaseD: 'D',\n  uppercaseE: 'E',\n  uppercaseF: 'F',\n  uppercaseG: 'G',\n  uppercaseH: 'H',\n  uppercaseI: 'I',\n  uppercaseJ: 'J',\n  uppercaseK: 'K',\n  uppercaseL: 'L',\n  uppercaseM: 'M',\n  uppercaseN: 'N',\n  uppercaseO: 'O',\n  uppercaseP: 'P',\n  uppercaseQ: 'Q',\n  uppercaseR: 'R',\n  uppercaseS: 'S',\n  uppercaseT: 'T',\n  uppercaseU: 'U',\n  uppercaseV: 'V',\n  uppercaseW: 'W',\n  uppercaseX: 'X',\n  uppercaseY: 'Y',\n  uppercaseZ: 'Z',\n  leftSquareBracket: '[',\n  backslash: '\\\\',\n  rightSquareBracket: ']',\n  caret: '^',\n  underscore: '_',\n  graveAccent: '`',\n  lowercaseA: 'a',\n  lowercaseB: 'b',\n  lowercaseC: 'c',\n  lowercaseD: 'd',\n  lowercaseE: 'e',\n  lowercaseF: 'f',\n  lowercaseG: 'g',\n  lowercaseH: 'h',\n  lowercaseI: 'i',\n  lowercaseJ: 'j',\n  lowercaseK: 'k',\n  lowercaseL: 'l',\n  lowercaseM: 'm',\n  lowercaseN: 'n',\n  lowercaseO: 'o',\n  lowercaseP: 'p',\n  lowercaseQ: 'q',\n  lowercaseR: 'r',\n  lowercaseS: 's',\n  lowercaseT: 't',\n  lowercaseU: 'u',\n  lowercaseV: 'v',\n  lowercaseW: 'w',\n  lowercaseX: 'x',\n  lowercaseY: 'y',\n  lowercaseZ: 'z',\n  leftCurlyBrace: '{',\n  verticalBar: '|',\n  rightCurlyBrace: '}',\n  tilde: '~',\n  replacementCharacter: '�'\n})\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/micromark-util-symbol@2.0.1/node_modules/micromark-util-symbol/lib/values.js\n");

/***/ })

};
;