"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/unified";
exports.ids = ["vendor-chunks/unified"];
exports.modules = {

/***/ "(ssr)/./node_modules/unified/lib/callable-instance.js":
/*!*******************************************************!*\
  !*** ./node_modules/unified/lib/callable-instance.js ***!
  \*******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CallableInstance: () => (/* binding */ CallableInstance)\n/* harmony export */ });\nconst CallableInstance =\n  /**\n   * @type {new <Parameters extends Array<unknown>, Result>(property: string | symbol) => (...parameters: Parameters) => Result}\n   */\n  (\n    /** @type {unknown} */\n    (\n      /**\n       * @this {Function}\n       * @param {string | symbol} property\n       * @returns {(...parameters: Array<unknown>) => unknown}\n       */\n      function (property) {\n        const self = this\n        const constr = self.constructor\n        const proto = /** @type {Record<string | symbol, Function>} */ (\n          // Prototypes do exist.\n          // type-coverage:ignore-next-line\n          constr.prototype\n        )\n        const value = proto[property]\n        /** @type {(...parameters: Array<unknown>) => unknown} */\n        const apply = function () {\n          return value.apply(apply, arguments)\n        }\n\n        Object.setPrototypeOf(apply, proto)\n\n        // Not needed for us in `unified`: we only call this on the `copy`\n        // function,\n        // and we don't need to add its fields (`length`, `name`)\n        // over.\n        // See also: GH-246.\n        // const names = Object.getOwnPropertyNames(value)\n        //\n        // for (const p of names) {\n        //   const descriptor = Object.getOwnPropertyDescriptor(value, p)\n        //   if (descriptor) Object.defineProperty(apply, p, descriptor)\n        // }\n\n        return apply\n      }\n    )\n  )\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvdW5pZmllZC9saWIvY2FsbGFibGUtaW5zdGFuY2UuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFPO0FBQ1A7QUFDQSxZQUFZO0FBQ1o7QUFDQTtBQUNBLGVBQWUsU0FBUztBQUN4QjtBQUNBO0FBQ0EsZ0JBQWdCO0FBQ2hCLGlCQUFpQixpQkFBaUI7QUFDbEMsbUJBQW1CO0FBQ25CO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsaUNBQWlDLG1DQUFtQztBQUNwRTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsbUJBQW1CLDRDQUE0QztBQUMvRDtBQUNBO0FBQ0E7O0FBRUE7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxHdWVyclxcRGVza3RvcFxcQUczTlRcXGZyb250ZW5kXFxub2RlX21vZHVsZXNcXHVuaWZpZWRcXGxpYlxcY2FsbGFibGUtaW5zdGFuY2UuanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGNvbnN0IENhbGxhYmxlSW5zdGFuY2UgPVxuICAvKipcbiAgICogQHR5cGUge25ldyA8UGFyYW1ldGVycyBleHRlbmRzIEFycmF5PHVua25vd24+LCBSZXN1bHQ+KHByb3BlcnR5OiBzdHJpbmcgfCBzeW1ib2wpID0+ICguLi5wYXJhbWV0ZXJzOiBQYXJhbWV0ZXJzKSA9PiBSZXN1bHR9XG4gICAqL1xuICAoXG4gICAgLyoqIEB0eXBlIHt1bmtub3dufSAqL1xuICAgIChcbiAgICAgIC8qKlxuICAgICAgICogQHRoaXMge0Z1bmN0aW9ufVxuICAgICAgICogQHBhcmFtIHtzdHJpbmcgfCBzeW1ib2x9IHByb3BlcnR5XG4gICAgICAgKiBAcmV0dXJucyB7KC4uLnBhcmFtZXRlcnM6IEFycmF5PHVua25vd24+KSA9PiB1bmtub3dufVxuICAgICAgICovXG4gICAgICBmdW5jdGlvbiAocHJvcGVydHkpIHtcbiAgICAgICAgY29uc3Qgc2VsZiA9IHRoaXNcbiAgICAgICAgY29uc3QgY29uc3RyID0gc2VsZi5jb25zdHJ1Y3RvclxuICAgICAgICBjb25zdCBwcm90byA9IC8qKiBAdHlwZSB7UmVjb3JkPHN0cmluZyB8IHN5bWJvbCwgRnVuY3Rpb24+fSAqLyAoXG4gICAgICAgICAgLy8gUHJvdG90eXBlcyBkbyBleGlzdC5cbiAgICAgICAgICAvLyB0eXBlLWNvdmVyYWdlOmlnbm9yZS1uZXh0LWxpbmVcbiAgICAgICAgICBjb25zdHIucHJvdG90eXBlXG4gICAgICAgIClcbiAgICAgICAgY29uc3QgdmFsdWUgPSBwcm90b1twcm9wZXJ0eV1cbiAgICAgICAgLyoqIEB0eXBlIHsoLi4ucGFyYW1ldGVyczogQXJyYXk8dW5rbm93bj4pID0+IHVua25vd259ICovXG4gICAgICAgIGNvbnN0IGFwcGx5ID0gZnVuY3Rpb24gKCkge1xuICAgICAgICAgIHJldHVybiB2YWx1ZS5hcHBseShhcHBseSwgYXJndW1lbnRzKVxuICAgICAgICB9XG5cbiAgICAgICAgT2JqZWN0LnNldFByb3RvdHlwZU9mKGFwcGx5LCBwcm90bylcblxuICAgICAgICAvLyBOb3QgbmVlZGVkIGZvciB1cyBpbiBgdW5pZmllZGA6IHdlIG9ubHkgY2FsbCB0aGlzIG9uIHRoZSBgY29weWBcbiAgICAgICAgLy8gZnVuY3Rpb24sXG4gICAgICAgIC8vIGFuZCB3ZSBkb24ndCBuZWVkIHRvIGFkZCBpdHMgZmllbGRzIChgbGVuZ3RoYCwgYG5hbWVgKVxuICAgICAgICAvLyBvdmVyLlxuICAgICAgICAvLyBTZWUgYWxzbzogR0gtMjQ2LlxuICAgICAgICAvLyBjb25zdCBuYW1lcyA9IE9iamVjdC5nZXRPd25Qcm9wZXJ0eU5hbWVzKHZhbHVlKVxuICAgICAgICAvL1xuICAgICAgICAvLyBmb3IgKGNvbnN0IHAgb2YgbmFtZXMpIHtcbiAgICAgICAgLy8gICBjb25zdCBkZXNjcmlwdG9yID0gT2JqZWN0LmdldE93blByb3BlcnR5RGVzY3JpcHRvcih2YWx1ZSwgcClcbiAgICAgICAgLy8gICBpZiAoZGVzY3JpcHRvcikgT2JqZWN0LmRlZmluZVByb3BlcnR5KGFwcGx5LCBwLCBkZXNjcmlwdG9yKVxuICAgICAgICAvLyB9XG5cbiAgICAgICAgcmV0dXJuIGFwcGx5XG4gICAgICB9XG4gICAgKVxuICApXG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/unified/lib/callable-instance.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/unified/lib/index.js":
/*!*******************************************!*\
  !*** ./node_modules/unified/lib/index.js ***!
  \*******************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Processor: () => (/* binding */ Processor),\n/* harmony export */   unified: () => (/* binding */ unified)\n/* harmony export */ });\n/* harmony import */ var bail__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! bail */ \"(ssr)/./node_modules/bail/index.js\");\n/* harmony import */ var extend__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! extend */ \"(ssr)/./node_modules/extend/index.js\");\n/* harmony import */ var devlop__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! devlop */ \"(ssr)/./node_modules/devlop/lib/development.js\");\n/* harmony import */ var is_plain_obj__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! is-plain-obj */ \"(ssr)/./node_modules/is-plain-obj/index.js\");\n/* harmony import */ var trough__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! trough */ \"(ssr)/./node_modules/trough/lib/index.js\");\n/* harmony import */ var vfile__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! vfile */ \"(ssr)/./node_modules/vfile/lib/index.js\");\n/* harmony import */ var _callable_instance_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./callable-instance.js */ \"(ssr)/./node_modules/unified/lib/callable-instance.js\");\n/**\n * @typedef {import('trough').Pipeline} Pipeline\n *\n * @typedef {import('unist').Node} Node\n *\n * @typedef {import('vfile').Compatible} Compatible\n * @typedef {import('vfile').Value} Value\n *\n * @typedef {import('../index.js').CompileResultMap} CompileResultMap\n * @typedef {import('../index.js').Data} Data\n * @typedef {import('../index.js').Settings} Settings\n */\n\n/**\n * @typedef {CompileResultMap[keyof CompileResultMap]} CompileResults\n *   Acceptable results from compilers.\n *\n *   To register custom results, add them to\n *   {@linkcode CompileResultMap}.\n */\n\n/**\n * @template {Node} [Tree=Node]\n *   The node that the compiler receives (default: `Node`).\n * @template {CompileResults} [Result=CompileResults]\n *   The thing that the compiler yields (default: `CompileResults`).\n * @callback Compiler\n *   A **compiler** handles the compiling of a syntax tree to something else\n *   (in most cases, text) (TypeScript type).\n *\n *   It is used in the stringify phase and called with a {@linkcode Node}\n *   and {@linkcode VFile} representation of the document to compile.\n *   It should return the textual representation of the given tree (typically\n *   `string`).\n *\n *   > **Note**: unified typically compiles by serializing: most compilers\n *   > return `string` (or `Uint8Array`).\n *   > Some compilers, such as the one configured with\n *   > [`rehype-react`][rehype-react], return other values (in this case, a\n *   > React tree).\n *   > If you’re using a compiler that doesn’t serialize, expect different\n *   > result values.\n *   >\n *   > To register custom results in TypeScript, add them to\n *   > {@linkcode CompileResultMap}.\n *\n *   [rehype-react]: https://github.com/rehypejs/rehype-react\n * @param {Tree} tree\n *   Tree to compile.\n * @param {VFile} file\n *   File associated with `tree`.\n * @returns {Result}\n *   New content: compiled text (`string` or `Uint8Array`, for `file.value`) or\n *   something else (for `file.result`).\n */\n\n/**\n * @template {Node} [Tree=Node]\n *   The node that the parser yields (default: `Node`)\n * @callback Parser\n *   A **parser** handles the parsing of text to a syntax tree.\n *\n *   It is used in the parse phase and is called with a `string` and\n *   {@linkcode VFile} of the document to parse.\n *   It must return the syntax tree representation of the given file\n *   ({@linkcode Node}).\n * @param {string} document\n *   Document to parse.\n * @param {VFile} file\n *   File associated with `document`.\n * @returns {Tree}\n *   Node representing the given file.\n */\n\n/**\n * @typedef {(\n *   Plugin<Array<any>, any, any> |\n *   PluginTuple<Array<any>, any, any> |\n *   Preset\n * )} Pluggable\n *   Union of the different ways to add plugins and settings.\n */\n\n/**\n * @typedef {Array<Pluggable>} PluggableList\n *   List of plugins and presets.\n */\n\n// Note: we can’t use `callback` yet as it messes up `this`:\n//  <https://github.com/microsoft/TypeScript/issues/55197>.\n/**\n * @template {Array<unknown>} [PluginParameters=[]]\n *   Arguments passed to the plugin (default: `[]`, the empty tuple).\n * @template {Node | string | undefined} [Input=Node]\n *   Value that is expected as input (default: `Node`).\n *\n *   *   If the plugin returns a {@linkcode Transformer}, this\n *       should be the node it expects.\n *   *   If the plugin sets a {@linkcode Parser}, this should be\n *       `string`.\n *   *   If the plugin sets a {@linkcode Compiler}, this should be the\n *       node it expects.\n * @template [Output=Input]\n *   Value that is yielded as output (default: `Input`).\n *\n *   *   If the plugin returns a {@linkcode Transformer}, this\n *       should be the node that that yields.\n *   *   If the plugin sets a {@linkcode Parser}, this should be the\n *       node that it yields.\n *   *   If the plugin sets a {@linkcode Compiler}, this should be\n *       result it yields.\n * @typedef {(\n *   (this: Processor, ...parameters: PluginParameters) =>\n *     Input extends string ? // Parser.\n *        Output extends Node | undefined ? undefined | void : never :\n *     Output extends CompileResults ? // Compiler.\n *        Input extends Node | undefined ? undefined | void : never :\n *     Transformer<\n *       Input extends Node ? Input : Node,\n *       Output extends Node ? Output : Node\n *     > | undefined | void\n * )} Plugin\n *   Single plugin.\n *\n *   Plugins configure the processors they are applied on in the following\n *   ways:\n *\n *   *   they change the processor, such as the parser, the compiler, or by\n *       configuring data\n *   *   they specify how to handle trees and files\n *\n *   In practice, they are functions that can receive options and configure the\n *   processor (`this`).\n *\n *   > **Note**: plugins are called when the processor is *frozen*, not when\n *   > they are applied.\n */\n\n/**\n * Tuple of a plugin and its configuration.\n *\n * The first item is a plugin, the rest are its parameters.\n *\n * @template {Array<unknown>} [TupleParameters=[]]\n *   Arguments passed to the plugin (default: `[]`, the empty tuple).\n * @template {Node | string | undefined} [Input=undefined]\n *   Value that is expected as input (optional).\n *\n *   *   If the plugin returns a {@linkcode Transformer}, this\n *       should be the node it expects.\n *   *   If the plugin sets a {@linkcode Parser}, this should be\n *       `string`.\n *   *   If the plugin sets a {@linkcode Compiler}, this should be the\n *       node it expects.\n * @template [Output=undefined] (optional).\n *   Value that is yielded as output.\n *\n *   *   If the plugin returns a {@linkcode Transformer}, this\n *       should be the node that that yields.\n *   *   If the plugin sets a {@linkcode Parser}, this should be the\n *       node that it yields.\n *   *   If the plugin sets a {@linkcode Compiler}, this should be\n *       result it yields.\n * @typedef {(\n *   [\n *     plugin: Plugin<TupleParameters, Input, Output>,\n *     ...parameters: TupleParameters\n *   ]\n * )} PluginTuple\n */\n\n/**\n * @typedef Preset\n *   Sharable configuration.\n *\n *   They can contain plugins and settings.\n * @property {PluggableList | undefined} [plugins]\n *   List of plugins and presets (optional).\n * @property {Settings | undefined} [settings]\n *   Shared settings for parsers and compilers (optional).\n */\n\n/**\n * @template {VFile} [File=VFile]\n *   The file that the callback receives (default: `VFile`).\n * @callback ProcessCallback\n *   Callback called when the process is done.\n *\n *   Called with either an error or a result.\n * @param {Error | undefined} [error]\n *   Fatal error (optional).\n * @param {File | undefined} [file]\n *   Processed file (optional).\n * @returns {undefined}\n *   Nothing.\n */\n\n/**\n * @template {Node} [Tree=Node]\n *   The tree that the callback receives (default: `Node`).\n * @callback RunCallback\n *   Callback called when transformers are done.\n *\n *   Called with either an error or results.\n * @param {Error | undefined} [error]\n *   Fatal error (optional).\n * @param {Tree | undefined} [tree]\n *   Transformed tree (optional).\n * @param {VFile | undefined} [file]\n *   File (optional).\n * @returns {undefined}\n *   Nothing.\n */\n\n/**\n * @template {Node} [Output=Node]\n *   Node type that the transformer yields (default: `Node`).\n * @callback TransformCallback\n *   Callback passed to transforms.\n *\n *   If the signature of a `transformer` accepts a third argument, the\n *   transformer may perform asynchronous operations, and must call it.\n * @param {Error | undefined} [error]\n *   Fatal error to stop the process (optional).\n * @param {Output | undefined} [tree]\n *   New, changed, tree (optional).\n * @param {VFile | undefined} [file]\n *   New, changed, file (optional).\n * @returns {undefined}\n *   Nothing.\n */\n\n/**\n * @template {Node} [Input=Node]\n *   Node type that the transformer expects (default: `Node`).\n * @template {Node} [Output=Input]\n *   Node type that the transformer yields (default: `Input`).\n * @callback Transformer\n *   Transformers handle syntax trees and files.\n *\n *   They are functions that are called each time a syntax tree and file are\n *   passed through the run phase.\n *   When an error occurs in them (either because it’s thrown, returned,\n *   rejected, or passed to `next`), the process stops.\n *\n *   The run phase is handled by [`trough`][trough], see its documentation for\n *   the exact semantics of these functions.\n *\n *   > **Note**: you should likely ignore `next`: don’t accept it.\n *   > it supports callback-style async work.\n *   > But promises are likely easier to reason about.\n *\n *   [trough]: https://github.com/wooorm/trough#function-fninput-next\n * @param {Input} tree\n *   Tree to handle.\n * @param {VFile} file\n *   File to handle.\n * @param {TransformCallback<Output>} next\n *   Callback.\n * @returns {(\n *   Promise<Output | undefined | void> |\n *   Promise<never> | // For some reason this is needed separately.\n *   Output |\n *   Error |\n *   undefined |\n *   void\n * )}\n *   If you accept `next`, nothing.\n *   Otherwise:\n *\n *   *   `Error` — fatal error to stop the process\n *   *   `Promise<undefined>` or `undefined` — the next transformer keeps using\n *       same tree\n *   *   `Promise<Node>` or `Node` — new, changed, tree\n */\n\n/**\n * @template {Node | undefined} ParseTree\n *   Output of `parse`.\n * @template {Node | undefined} HeadTree\n *   Input for `run`.\n * @template {Node | undefined} TailTree\n *   Output for `run`.\n * @template {Node | undefined} CompileTree\n *   Input of `stringify`.\n * @template {CompileResults | undefined} CompileResult\n *   Output of `stringify`.\n * @template {Node | string | undefined} Input\n *   Input of plugin.\n * @template Output\n *   Output of plugin (optional).\n * @typedef {(\n *   Input extends string\n *     ? Output extends Node | undefined\n *       ? // Parser.\n *         Processor<\n *           Output extends undefined ? ParseTree : Output,\n *           HeadTree,\n *           TailTree,\n *           CompileTree,\n *           CompileResult\n *         >\n *       : // Unknown.\n *         Processor<ParseTree, HeadTree, TailTree, CompileTree, CompileResult>\n *     : Output extends CompileResults\n *     ? Input extends Node | undefined\n *       ? // Compiler.\n *         Processor<\n *           ParseTree,\n *           HeadTree,\n *           TailTree,\n *           Input extends undefined ? CompileTree : Input,\n *           Output extends undefined ? CompileResult : Output\n *         >\n *       : // Unknown.\n *         Processor<ParseTree, HeadTree, TailTree, CompileTree, CompileResult>\n *     : Input extends Node | undefined\n *     ? Output extends Node | undefined\n *       ? // Transform.\n *         Processor<\n *           ParseTree,\n *           HeadTree extends undefined ? Input : HeadTree,\n *           Output extends undefined ? TailTree : Output,\n *           CompileTree,\n *           CompileResult\n *         >\n *       : // Unknown.\n *         Processor<ParseTree, HeadTree, TailTree, CompileTree, CompileResult>\n *     : // Unknown.\n *       Processor<ParseTree, HeadTree, TailTree, CompileTree, CompileResult>\n * )} UsePlugin\n *   Create a processor based on the input/output of a {@link Plugin plugin}.\n */\n\n/**\n * @template {CompileResults | undefined} Result\n *   Node type that the transformer yields.\n * @typedef {(\n *   Result extends Value | undefined ?\n *     VFile :\n *     VFile & {result: Result}\n *   )} VFileWithOutput\n *   Type to generate a {@linkcode VFile} corresponding to a compiler result.\n *\n *   If a result that is not acceptable on a `VFile` is used, that will\n *   be stored on the `result` field of {@linkcode VFile}.\n */\n\n\n\n\n\n\n\n\n\n// To do: next major: drop `Compiler`, `Parser`: prefer lowercase.\n\n// To do: we could start yielding `never` in TS when a parser is missing and\n// `parse` is called.\n// Currently, we allow directly setting `processor.parser`, which is untyped.\n\nconst own = {}.hasOwnProperty\n\n/**\n * @template {Node | undefined} [ParseTree=undefined]\n *   Output of `parse` (optional).\n * @template {Node | undefined} [HeadTree=undefined]\n *   Input for `run` (optional).\n * @template {Node | undefined} [TailTree=undefined]\n *   Output for `run` (optional).\n * @template {Node | undefined} [CompileTree=undefined]\n *   Input of `stringify` (optional).\n * @template {CompileResults | undefined} [CompileResult=undefined]\n *   Output of `stringify` (optional).\n * @extends {CallableInstance<[], Processor<ParseTree, HeadTree, TailTree, CompileTree, CompileResult>>}\n */\nclass Processor extends _callable_instance_js__WEBPACK_IMPORTED_MODULE_2__.CallableInstance {\n  /**\n   * Create a processor.\n   */\n  constructor() {\n    // If `Processor()` is called (w/o new), `copy` is called instead.\n    super('copy')\n\n    /**\n     * Compiler to use (deprecated).\n     *\n     * @deprecated\n     *   Use `compiler` instead.\n     * @type {(\n     *   Compiler<\n     *     CompileTree extends undefined ? Node : CompileTree,\n     *     CompileResult extends undefined ? CompileResults : CompileResult\n     *   > |\n     *   undefined\n     * )}\n     */\n    this.Compiler = undefined\n\n    /**\n     * Parser to use (deprecated).\n     *\n     * @deprecated\n     *   Use `parser` instead.\n     * @type {(\n     *   Parser<ParseTree extends undefined ? Node : ParseTree> |\n     *   undefined\n     * )}\n     */\n    this.Parser = undefined\n\n    // Note: the following fields are considered private.\n    // However, they are needed for tests, and TSC generates an untyped\n    // `private freezeIndex` field for, which trips `type-coverage` up.\n    // Instead, we use `@deprecated` to visualize that they shouldn’t be used.\n    /**\n     * Internal list of configured plugins.\n     *\n     * @deprecated\n     *   This is a private internal property and should not be used.\n     * @type {Array<PluginTuple<Array<unknown>>>}\n     */\n    this.attachers = []\n\n    /**\n     * Compiler to use.\n     *\n     * @type {(\n     *   Compiler<\n     *     CompileTree extends undefined ? Node : CompileTree,\n     *     CompileResult extends undefined ? CompileResults : CompileResult\n     *   > |\n     *   undefined\n     * )}\n     */\n    this.compiler = undefined\n\n    /**\n     * Internal state to track where we are while freezing.\n     *\n     * @deprecated\n     *   This is a private internal property and should not be used.\n     * @type {number}\n     */\n    this.freezeIndex = -1\n\n    /**\n     * Internal state to track whether we’re frozen.\n     *\n     * @deprecated\n     *   This is a private internal property and should not be used.\n     * @type {boolean | undefined}\n     */\n    this.frozen = undefined\n\n    /**\n     * Internal state.\n     *\n     * @deprecated\n     *   This is a private internal property and should not be used.\n     * @type {Data}\n     */\n    this.namespace = {}\n\n    /**\n     * Parser to use.\n     *\n     * @type {(\n     *   Parser<ParseTree extends undefined ? Node : ParseTree> |\n     *   undefined\n     * )}\n     */\n    this.parser = undefined\n\n    /**\n     * Internal list of configured transformers.\n     *\n     * @deprecated\n     *   This is a private internal property and should not be used.\n     * @type {Pipeline}\n     */\n    this.transformers = (0,trough__WEBPACK_IMPORTED_MODULE_3__.trough)()\n  }\n\n  /**\n   * Copy a processor.\n   *\n   * @deprecated\n   *   This is a private internal method and should not be used.\n   * @returns {Processor<ParseTree, HeadTree, TailTree, CompileTree, CompileResult>}\n   *   New *unfrozen* processor ({@linkcode Processor}) that is\n   *   configured to work the same as its ancestor.\n   *   When the descendant processor is configured in the future it does not\n   *   affect the ancestral processor.\n   */\n  copy() {\n    // Cast as the type parameters will be the same after attaching.\n    const destination =\n      /** @type {Processor<ParseTree, HeadTree, TailTree, CompileTree, CompileResult>} */ (\n        new Processor()\n      )\n    let index = -1\n\n    while (++index < this.attachers.length) {\n      const attacher = this.attachers[index]\n      destination.use(...attacher)\n    }\n\n    destination.data(extend__WEBPACK_IMPORTED_MODULE_0__(true, {}, this.namespace))\n\n    return destination\n  }\n\n  /**\n   * Configure the processor with info available to all plugins.\n   * Information is stored in an object.\n   *\n   * Typically, options can be given to a specific plugin, but sometimes it\n   * makes sense to have information shared with several plugins.\n   * For example, a list of HTML elements that are self-closing, which is\n   * needed during all phases.\n   *\n   * > **Note**: setting information cannot occur on *frozen* processors.\n   * > Call the processor first to create a new unfrozen processor.\n   *\n   * > **Note**: to register custom data in TypeScript, augment the\n   * > {@linkcode Data} interface.\n   *\n   * @example\n   *   This example show how to get and set info:\n   *\n   *   ```js\n   *   import {unified} from 'unified'\n   *\n   *   const processor = unified().data('alpha', 'bravo')\n   *\n   *   processor.data('alpha') // => 'bravo'\n   *\n   *   processor.data() // => {alpha: 'bravo'}\n   *\n   *   processor.data({charlie: 'delta'})\n   *\n   *   processor.data() // => {charlie: 'delta'}\n   *   ```\n   *\n   * @template {keyof Data} Key\n   *\n   * @overload\n   * @returns {Data}\n   *\n   * @overload\n   * @param {Data} dataset\n   * @returns {Processor<ParseTree, HeadTree, TailTree, CompileTree, CompileResult>}\n   *\n   * @overload\n   * @param {Key} key\n   * @returns {Data[Key]}\n   *\n   * @overload\n   * @param {Key} key\n   * @param {Data[Key]} value\n   * @returns {Processor<ParseTree, HeadTree, TailTree, CompileTree, CompileResult>}\n   *\n   * @param {Data | Key} [key]\n   *   Key to get or set, or entire dataset to set, or nothing to get the\n   *   entire dataset (optional).\n   * @param {Data[Key]} [value]\n   *   Value to set (optional).\n   * @returns {unknown}\n   *   The current processor when setting, the value at `key` when getting, or\n   *   the entire dataset when getting without key.\n   */\n  data(key, value) {\n    if (typeof key === 'string') {\n      // Set `key`.\n      if (arguments.length === 2) {\n        assertUnfrozen('data', this.frozen)\n        this.namespace[key] = value\n        return this\n      }\n\n      // Get `key`.\n      return (own.call(this.namespace, key) && this.namespace[key]) || undefined\n    }\n\n    // Set space.\n    if (key) {\n      assertUnfrozen('data', this.frozen)\n      this.namespace = key\n      return this\n    }\n\n    // Get space.\n    return this.namespace\n  }\n\n  /**\n   * Freeze a processor.\n   *\n   * Frozen processors are meant to be extended and not to be configured\n   * directly.\n   *\n   * When a processor is frozen it cannot be unfrozen.\n   * New processors working the same way can be created by calling the\n   * processor.\n   *\n   * It’s possible to freeze processors explicitly by calling `.freeze()`.\n   * Processors freeze automatically when `.parse()`, `.run()`, `.runSync()`,\n   * `.stringify()`, `.process()`, or `.processSync()` are called.\n   *\n   * @returns {Processor<ParseTree, HeadTree, TailTree, CompileTree, CompileResult>}\n   *   The current processor.\n   */\n  freeze() {\n    if (this.frozen) {\n      return this\n    }\n\n    // Cast so that we can type plugins easier.\n    // Plugins are supposed to be usable on different processors, not just on\n    // this exact processor.\n    const self = /** @type {Processor} */ (/** @type {unknown} */ (this))\n\n    while (++this.freezeIndex < this.attachers.length) {\n      const [attacher, ...options] = this.attachers[this.freezeIndex]\n\n      if (options[0] === false) {\n        continue\n      }\n\n      if (options[0] === true) {\n        options[0] = undefined\n      }\n\n      const transformer = attacher.call(self, ...options)\n\n      if (typeof transformer === 'function') {\n        this.transformers.use(transformer)\n      }\n    }\n\n    this.frozen = true\n    this.freezeIndex = Number.POSITIVE_INFINITY\n\n    return this\n  }\n\n  /**\n   * Parse text to a syntax tree.\n   *\n   * > **Note**: `parse` freezes the processor if not already *frozen*.\n   *\n   * > **Note**: `parse` performs the parse phase, not the run phase or other\n   * > phases.\n   *\n   * @param {Compatible | undefined} [file]\n   *   file to parse (optional); typically `string` or `VFile`; any value\n   *   accepted as `x` in `new VFile(x)`.\n   * @returns {ParseTree extends undefined ? Node : ParseTree}\n   *   Syntax tree representing `file`.\n   */\n  parse(file) {\n    this.freeze()\n    const realFile = vfile(file)\n    const parser = this.parser || this.Parser\n    assertParser('parse', parser)\n    return parser(String(realFile), realFile)\n  }\n\n  /**\n   * Process the given file as configured on the processor.\n   *\n   * > **Note**: `process` freezes the processor if not already *frozen*.\n   *\n   * > **Note**: `process` performs the parse, run, and stringify phases.\n   *\n   * @overload\n   * @param {Compatible | undefined} file\n   * @param {ProcessCallback<VFileWithOutput<CompileResult>>} done\n   * @returns {undefined}\n   *\n   * @overload\n   * @param {Compatible | undefined} [file]\n   * @returns {Promise<VFileWithOutput<CompileResult>>}\n   *\n   * @param {Compatible | undefined} [file]\n   *   File (optional); typically `string` or `VFile`]; any value accepted as\n   *   `x` in `new VFile(x)`.\n   * @param {ProcessCallback<VFileWithOutput<CompileResult>> | undefined} [done]\n   *   Callback (optional).\n   * @returns {Promise<VFile> | undefined}\n   *   Nothing if `done` is given.\n   *   Otherwise a promise, rejected with a fatal error or resolved with the\n   *   processed file.\n   *\n   *   The parsed, transformed, and compiled value is available at\n   *   `file.value` (see note).\n   *\n   *   > **Note**: unified typically compiles by serializing: most\n   *   > compilers return `string` (or `Uint8Array`).\n   *   > Some compilers, such as the one configured with\n   *   > [`rehype-react`][rehype-react], return other values (in this case, a\n   *   > React tree).\n   *   > If you’re using a compiler that doesn’t serialize, expect different\n   *   > result values.\n   *   >\n   *   > To register custom results in TypeScript, add them to\n   *   > {@linkcode CompileResultMap}.\n   *\n   *   [rehype-react]: https://github.com/rehypejs/rehype-react\n   */\n  process(file, done) {\n    const self = this\n\n    this.freeze()\n    assertParser('process', this.parser || this.Parser)\n    assertCompiler('process', this.compiler || this.Compiler)\n\n    return done ? executor(undefined, done) : new Promise(executor)\n\n    // Note: `void`s needed for TS.\n    /**\n     * @param {((file: VFileWithOutput<CompileResult>) => undefined | void) | undefined} resolve\n     * @param {(error: Error | undefined) => undefined | void} reject\n     * @returns {undefined}\n     */\n    function executor(resolve, reject) {\n      const realFile = vfile(file)\n      // Assume `ParseTree` (the result of the parser) matches `HeadTree` (the\n      // input of the first transform).\n      const parseTree =\n        /** @type {HeadTree extends undefined ? Node : HeadTree} */ (\n          /** @type {unknown} */ (self.parse(realFile))\n        )\n\n      self.run(parseTree, realFile, function (error, tree, file) {\n        if (error || !tree || !file) {\n          return realDone(error)\n        }\n\n        // Assume `TailTree` (the output of the last transform) matches\n        // `CompileTree` (the input of the compiler).\n        const compileTree =\n          /** @type {CompileTree extends undefined ? Node : CompileTree} */ (\n            /** @type {unknown} */ (tree)\n          )\n\n        const compileResult = self.stringify(compileTree, file)\n\n        if (looksLikeAValue(compileResult)) {\n          file.value = compileResult\n        } else {\n          file.result = compileResult\n        }\n\n        realDone(error, /** @type {VFileWithOutput<CompileResult>} */ (file))\n      })\n\n      /**\n       * @param {Error | undefined} error\n       * @param {VFileWithOutput<CompileResult> | undefined} [file]\n       * @returns {undefined}\n       */\n      function realDone(error, file) {\n        if (error || !file) {\n          reject(error)\n        } else if (resolve) {\n          resolve(file)\n        } else {\n          (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(done, '`done` is defined if `resolve` is not')\n          done(undefined, file)\n        }\n      }\n    }\n  }\n\n  /**\n   * Process the given file as configured on the processor.\n   *\n   * An error is thrown if asynchronous transforms are configured.\n   *\n   * > **Note**: `processSync` freezes the processor if not already *frozen*.\n   *\n   * > **Note**: `processSync` performs the parse, run, and stringify phases.\n   *\n   * @param {Compatible | undefined} [file]\n   *   File (optional); typically `string` or `VFile`; any value accepted as\n   *   `x` in `new VFile(x)`.\n   * @returns {VFileWithOutput<CompileResult>}\n   *   The processed file.\n   *\n   *   The parsed, transformed, and compiled value is available at\n   *   `file.value` (see note).\n   *\n   *   > **Note**: unified typically compiles by serializing: most\n   *   > compilers return `string` (or `Uint8Array`).\n   *   > Some compilers, such as the one configured with\n   *   > [`rehype-react`][rehype-react], return other values (in this case, a\n   *   > React tree).\n   *   > If you’re using a compiler that doesn’t serialize, expect different\n   *   > result values.\n   *   >\n   *   > To register custom results in TypeScript, add them to\n   *   > {@linkcode CompileResultMap}.\n   *\n   *   [rehype-react]: https://github.com/rehypejs/rehype-react\n   */\n  processSync(file) {\n    /** @type {boolean} */\n    let complete = false\n    /** @type {VFileWithOutput<CompileResult> | undefined} */\n    let result\n\n    this.freeze()\n    assertParser('processSync', this.parser || this.Parser)\n    assertCompiler('processSync', this.compiler || this.Compiler)\n\n    this.process(file, realDone)\n    assertDone('processSync', 'process', complete)\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(result, 'we either bailed on an error or have a tree')\n\n    return result\n\n    /**\n     * @type {ProcessCallback<VFileWithOutput<CompileResult>>}\n     */\n    function realDone(error, file) {\n      complete = true\n      ;(0,bail__WEBPACK_IMPORTED_MODULE_5__.bail)(error)\n      result = file\n    }\n  }\n\n  /**\n   * Run *transformers* on a syntax tree.\n   *\n   * > **Note**: `run` freezes the processor if not already *frozen*.\n   *\n   * > **Note**: `run` performs the run phase, not other phases.\n   *\n   * @overload\n   * @param {HeadTree extends undefined ? Node : HeadTree} tree\n   * @param {RunCallback<TailTree extends undefined ? Node : TailTree>} done\n   * @returns {undefined}\n   *\n   * @overload\n   * @param {HeadTree extends undefined ? Node : HeadTree} tree\n   * @param {Compatible | undefined} file\n   * @param {RunCallback<TailTree extends undefined ? Node : TailTree>} done\n   * @returns {undefined}\n   *\n   * @overload\n   * @param {HeadTree extends undefined ? Node : HeadTree} tree\n   * @param {Compatible | undefined} [file]\n   * @returns {Promise<TailTree extends undefined ? Node : TailTree>}\n   *\n   * @param {HeadTree extends undefined ? Node : HeadTree} tree\n   *   Tree to transform and inspect.\n   * @param {(\n   *   RunCallback<TailTree extends undefined ? Node : TailTree> |\n   *   Compatible\n   * )} [file]\n   *   File associated with `node` (optional); any value accepted as `x` in\n   *   `new VFile(x)`.\n   * @param {RunCallback<TailTree extends undefined ? Node : TailTree>} [done]\n   *   Callback (optional).\n   * @returns {Promise<TailTree extends undefined ? Node : TailTree> | undefined}\n   *   Nothing if `done` is given.\n   *   Otherwise, a promise rejected with a fatal error or resolved with the\n   *   transformed tree.\n   */\n  run(tree, file, done) {\n    assertNode(tree)\n    this.freeze()\n\n    const transformers = this.transformers\n\n    if (!done && typeof file === 'function') {\n      done = file\n      file = undefined\n    }\n\n    return done ? executor(undefined, done) : new Promise(executor)\n\n    // Note: `void`s needed for TS.\n    /**\n     * @param {(\n     *   ((tree: TailTree extends undefined ? Node : TailTree) => undefined | void) |\n     *   undefined\n     * )} resolve\n     * @param {(error: Error) => undefined | void} reject\n     * @returns {undefined}\n     */\n    function executor(resolve, reject) {\n      ;(0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(\n        typeof file !== 'function',\n        '`file` can’t be a `done` anymore, we checked'\n      )\n      const realFile = vfile(file)\n      transformers.run(tree, realFile, realDone)\n\n      /**\n       * @param {Error | undefined} error\n       * @param {Node} outputTree\n       * @param {VFile} file\n       * @returns {undefined}\n       */\n      function realDone(error, outputTree, file) {\n        const resultingTree =\n          /** @type {TailTree extends undefined ? Node : TailTree} */ (\n            outputTree || tree\n          )\n\n        if (error) {\n          reject(error)\n        } else if (resolve) {\n          resolve(resultingTree)\n        } else {\n          (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(done, '`done` is defined if `resolve` is not')\n          done(undefined, resultingTree, file)\n        }\n      }\n    }\n  }\n\n  /**\n   * Run *transformers* on a syntax tree.\n   *\n   * An error is thrown if asynchronous transforms are configured.\n   *\n   * > **Note**: `runSync` freezes the processor if not already *frozen*.\n   *\n   * > **Note**: `runSync` performs the run phase, not other phases.\n   *\n   * @param {HeadTree extends undefined ? Node : HeadTree} tree\n   *   Tree to transform and inspect.\n   * @param {Compatible | undefined} [file]\n   *   File associated with `node` (optional); any value accepted as `x` in\n   *   `new VFile(x)`.\n   * @returns {TailTree extends undefined ? Node : TailTree}\n   *   Transformed tree.\n   */\n  runSync(tree, file) {\n    /** @type {boolean} */\n    let complete = false\n    /** @type {(TailTree extends undefined ? Node : TailTree) | undefined} */\n    let result\n\n    this.run(tree, file, realDone)\n\n    assertDone('runSync', 'run', complete)\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(result, 'we either bailed on an error or have a tree')\n    return result\n\n    /**\n     * @type {RunCallback<TailTree extends undefined ? Node : TailTree>}\n     */\n    function realDone(error, tree) {\n      ;(0,bail__WEBPACK_IMPORTED_MODULE_5__.bail)(error)\n      result = tree\n      complete = true\n    }\n  }\n\n  /**\n   * Compile a syntax tree.\n   *\n   * > **Note**: `stringify` freezes the processor if not already *frozen*.\n   *\n   * > **Note**: `stringify` performs the stringify phase, not the run phase\n   * > or other phases.\n   *\n   * @param {CompileTree extends undefined ? Node : CompileTree} tree\n   *   Tree to compile.\n   * @param {Compatible | undefined} [file]\n   *   File associated with `node` (optional); any value accepted as `x` in\n   *   `new VFile(x)`.\n   * @returns {CompileResult extends undefined ? Value : CompileResult}\n   *   Textual representation of the tree (see note).\n   *\n   *   > **Note**: unified typically compiles by serializing: most compilers\n   *   > return `string` (or `Uint8Array`).\n   *   > Some compilers, such as the one configured with\n   *   > [`rehype-react`][rehype-react], return other values (in this case, a\n   *   > React tree).\n   *   > If you’re using a compiler that doesn’t serialize, expect different\n   *   > result values.\n   *   >\n   *   > To register custom results in TypeScript, add them to\n   *   > {@linkcode CompileResultMap}.\n   *\n   *   [rehype-react]: https://github.com/rehypejs/rehype-react\n   */\n  stringify(tree, file) {\n    this.freeze()\n    const realFile = vfile(file)\n    const compiler = this.compiler || this.Compiler\n    assertCompiler('stringify', compiler)\n    assertNode(tree)\n\n    return compiler(tree, realFile)\n  }\n\n  /**\n   * Configure the processor to use a plugin, a list of usable values, or a\n   * preset.\n   *\n   * If the processor is already using a plugin, the previous plugin\n   * configuration is changed based on the options that are passed in.\n   * In other words, the plugin is not added a second time.\n   *\n   * > **Note**: `use` cannot be called on *frozen* processors.\n   * > Call the processor first to create a new unfrozen processor.\n   *\n   * @example\n   *   There are many ways to pass plugins to `.use()`.\n   *   This example gives an overview:\n   *\n   *   ```js\n   *   import {unified} from 'unified'\n   *\n   *   unified()\n   *     // Plugin with options:\n   *     .use(pluginA, {x: true, y: true})\n   *     // Passing the same plugin again merges configuration (to `{x: true, y: false, z: true}`):\n   *     .use(pluginA, {y: false, z: true})\n   *     // Plugins:\n   *     .use([pluginB, pluginC])\n   *     // Two plugins, the second with options:\n   *     .use([pluginD, [pluginE, {}]])\n   *     // Preset with plugins and settings:\n   *     .use({plugins: [pluginF, [pluginG, {}]], settings: {position: false}})\n   *     // Settings only:\n   *     .use({settings: {position: false}})\n   *   ```\n   *\n   * @template {Array<unknown>} [Parameters=[]]\n   * @template {Node | string | undefined} [Input=undefined]\n   * @template [Output=Input]\n   *\n   * @overload\n   * @param {Preset | null | undefined} [preset]\n   * @returns {Processor<ParseTree, HeadTree, TailTree, CompileTree, CompileResult>}\n   *\n   * @overload\n   * @param {PluggableList} list\n   * @returns {Processor<ParseTree, HeadTree, TailTree, CompileTree, CompileResult>}\n   *\n   * @overload\n   * @param {Plugin<Parameters, Input, Output>} plugin\n   * @param {...(Parameters | [boolean])} parameters\n   * @returns {UsePlugin<ParseTree, HeadTree, TailTree, CompileTree, CompileResult, Input, Output>}\n   *\n   * @param {PluggableList | Plugin | Preset | null | undefined} value\n   *   Usable value.\n   * @param {...unknown} parameters\n   *   Parameters, when a plugin is given as a usable value.\n   * @returns {Processor<ParseTree, HeadTree, TailTree, CompileTree, CompileResult>}\n   *   Current processor.\n   */\n  use(value, ...parameters) {\n    const attachers = this.attachers\n    const namespace = this.namespace\n\n    assertUnfrozen('use', this.frozen)\n\n    if (value === null || value === undefined) {\n      // Empty.\n    } else if (typeof value === 'function') {\n      addPlugin(value, parameters)\n    } else if (typeof value === 'object') {\n      if (Array.isArray(value)) {\n        addList(value)\n      } else {\n        addPreset(value)\n      }\n    } else {\n      throw new TypeError('Expected usable value, not `' + value + '`')\n    }\n\n    return this\n\n    /**\n     * @param {Pluggable} value\n     * @returns {undefined}\n     */\n    function add(value) {\n      if (typeof value === 'function') {\n        addPlugin(value, [])\n      } else if (typeof value === 'object') {\n        if (Array.isArray(value)) {\n          const [plugin, ...parameters] =\n            /** @type {PluginTuple<Array<unknown>>} */ (value)\n          addPlugin(plugin, parameters)\n        } else {\n          addPreset(value)\n        }\n      } else {\n        throw new TypeError('Expected usable value, not `' + value + '`')\n      }\n    }\n\n    /**\n     * @param {Preset} result\n     * @returns {undefined}\n     */\n    function addPreset(result) {\n      if (!('plugins' in result) && !('settings' in result)) {\n        throw new Error(\n          'Expected usable value but received an empty preset, which is probably a mistake: presets typically come with `plugins` and sometimes with `settings`, but this has neither'\n        )\n      }\n\n      addList(result.plugins)\n\n      if (result.settings) {\n        namespace.settings = extend__WEBPACK_IMPORTED_MODULE_0__(true, namespace.settings, result.settings)\n      }\n    }\n\n    /**\n     * @param {PluggableList | null | undefined} plugins\n     * @returns {undefined}\n     */\n    function addList(plugins) {\n      let index = -1\n\n      if (plugins === null || plugins === undefined) {\n        // Empty.\n      } else if (Array.isArray(plugins)) {\n        while (++index < plugins.length) {\n          const thing = plugins[index]\n          add(thing)\n        }\n      } else {\n        throw new TypeError('Expected a list of plugins, not `' + plugins + '`')\n      }\n    }\n\n    /**\n     * @param {Plugin} plugin\n     * @param {Array<unknown>} parameters\n     * @returns {undefined}\n     */\n    function addPlugin(plugin, parameters) {\n      let index = -1\n      let entryIndex = -1\n\n      while (++index < attachers.length) {\n        if (attachers[index][0] === plugin) {\n          entryIndex = index\n          break\n        }\n      }\n\n      if (entryIndex === -1) {\n        attachers.push([plugin, ...parameters])\n      }\n      // Only set if there was at least a `primary` value, otherwise we’d change\n      // `arguments.length`.\n      else if (parameters.length > 0) {\n        let [primary, ...rest] = parameters\n        const currentPrimary = attachers[entryIndex][1]\n        if ((0,is_plain_obj__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(currentPrimary) && (0,is_plain_obj__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(primary)) {\n          primary = extend__WEBPACK_IMPORTED_MODULE_0__(true, currentPrimary, primary)\n        }\n\n        attachers[entryIndex] = [plugin, primary, ...rest]\n      }\n    }\n  }\n}\n\n// Note: this returns a *callable* instance.\n// That’s why it’s documented as a function.\n/**\n * Create a new processor.\n *\n * @example\n *   This example shows how a new processor can be created (from `remark`) and linked\n *   to **stdin**(4) and **stdout**(4).\n *\n *   ```js\n *   import process from 'node:process'\n *   import concatStream from 'concat-stream'\n *   import {remark} from 'remark'\n *\n *   process.stdin.pipe(\n *     concatStream(function (buf) {\n *       process.stdout.write(String(remark().processSync(buf)))\n *     })\n *   )\n *   ```\n *\n * @returns\n *   New *unfrozen* processor (`processor`).\n *\n *   This processor is configured to work the same as its ancestor.\n *   When the descendant processor is configured in the future it does not\n *   affect the ancestral processor.\n */\nconst unified = new Processor().freeze()\n\n/**\n * Assert a parser is available.\n *\n * @param {string} name\n * @param {unknown} value\n * @returns {asserts value is Parser}\n */\nfunction assertParser(name, value) {\n  if (typeof value !== 'function') {\n    throw new TypeError('Cannot `' + name + '` without `parser`')\n  }\n}\n\n/**\n * Assert a compiler is available.\n *\n * @param {string} name\n * @param {unknown} value\n * @returns {asserts value is Compiler}\n */\nfunction assertCompiler(name, value) {\n  if (typeof value !== 'function') {\n    throw new TypeError('Cannot `' + name + '` without `compiler`')\n  }\n}\n\n/**\n * Assert the processor is not frozen.\n *\n * @param {string} name\n * @param {unknown} frozen\n * @returns {asserts frozen is false}\n */\nfunction assertUnfrozen(name, frozen) {\n  if (frozen) {\n    throw new Error(\n      'Cannot call `' +\n        name +\n        '` on a frozen processor.\\nCreate a new processor first, by calling it: use `processor()` instead of `processor`.'\n    )\n  }\n}\n\n/**\n * Assert `node` is a unist node.\n *\n * @param {unknown} node\n * @returns {asserts node is Node}\n */\nfunction assertNode(node) {\n  // `isPlainObj` unfortunately uses `any` instead of `unknown`.\n  // type-coverage:ignore-next-line\n  if (!(0,is_plain_obj__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(node) || typeof node.type !== 'string') {\n    throw new TypeError('Expected node, got `' + node + '`')\n    // Fine.\n  }\n}\n\n/**\n * Assert that `complete` is `true`.\n *\n * @param {string} name\n * @param {string} asyncName\n * @param {unknown} complete\n * @returns {asserts complete is true}\n */\nfunction assertDone(name, asyncName, complete) {\n  if (!complete) {\n    throw new Error(\n      '`' + name + '` finished async. Use `' + asyncName + '` instead'\n    )\n  }\n}\n\n/**\n * @param {Compatible | undefined} [value]\n * @returns {VFile}\n */\nfunction vfile(value) {\n  return looksLikeAVFile(value) ? value : new vfile__WEBPACK_IMPORTED_MODULE_6__.VFile(value)\n}\n\n/**\n * @param {Compatible | undefined} [value]\n * @returns {value is VFile}\n */\nfunction looksLikeAVFile(value) {\n  return Boolean(\n    value &&\n      typeof value === 'object' &&\n      'message' in value &&\n      'messages' in value\n  )\n}\n\n/**\n * @param {unknown} [value]\n * @returns {value is Value}\n */\nfunction looksLikeAValue(value) {\n  return typeof value === 'string' || isUint8Array(value)\n}\n\n/**\n * Assert `value` is an `Uint8Array`.\n *\n * @param {unknown} value\n *   thing.\n * @returns {value is Uint8Array}\n *   Whether `value` is an `Uint8Array`.\n */\nfunction isUint8Array(value) {\n  return Boolean(\n    value &&\n      typeof value === 'object' &&\n      'byteLength' in value &&\n      'byteOffset' in value\n  )\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/unified/lib/index.js\n");

/***/ })

};
;