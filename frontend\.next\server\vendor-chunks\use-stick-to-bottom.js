"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/use-stick-to-bottom";
exports.ids = ["vendor-chunks/use-stick-to-bottom"];
exports.modules = {

/***/ "(ssr)/./node_modules/use-stick-to-bottom/dist/StickToBottom.js":
/*!****************************************************************!*\
  !*** ./node_modules/use-stick-to-bottom/dist/StickToBottom.js ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   StickToBottom: () => (/* binding */ StickToBottom),\n/* harmony export */   useStickToBottomContext: () => (/* binding */ useStickToBottomContext)\n/* harmony export */ });\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _useStickToBottom_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./useStickToBottom.js */ \"(ssr)/./node_modules/use-stick-to-bottom/dist/useStickToBottom.js\");\n\n/*!---------------------------------------------------------------------------------------------\n *  Copyright (c) StackBlitz. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\n\n\n\nconst StickToBottomContext = (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(null);\nconst useIsomorphicLayoutEffect = typeof window !== \"undefined\" ? react__WEBPACK_IMPORTED_MODULE_1__.useLayoutEffect : react__WEBPACK_IMPORTED_MODULE_1__.useEffect;\nfunction StickToBottom({ instance, children, resize, initial, mass, damping, stiffness, targetScrollTop: currentTargetScrollTop, contextRef, ...props }) {\n    const customTargetScrollTop = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const targetScrollTop = react__WEBPACK_IMPORTED_MODULE_1__.useCallback((target, elements) => {\n        const get = context?.targetScrollTop ?? currentTargetScrollTop;\n        return get?.(target, elements) ?? target;\n    }, [currentTargetScrollTop]);\n    const defaultInstance = (0,_useStickToBottom_js__WEBPACK_IMPORTED_MODULE_2__.useStickToBottom)({\n        mass,\n        damping,\n        stiffness,\n        resize,\n        initial,\n        targetScrollTop,\n    });\n    const { scrollRef, contentRef, scrollToBottom, stopScroll, isAtBottom, escapedFromLock, state, } = instance ?? defaultInstance;\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(() => ({\n        scrollToBottom,\n        stopScroll,\n        scrollRef,\n        isAtBottom,\n        escapedFromLock,\n        contentRef,\n        state,\n        get targetScrollTop() {\n            return customTargetScrollTop.current;\n        },\n        set targetScrollTop(targetScrollTop) {\n            customTargetScrollTop.current = targetScrollTop;\n        },\n    }), [\n        scrollToBottom,\n        isAtBottom,\n        contentRef,\n        scrollRef,\n        stopScroll,\n        escapedFromLock,\n        state,\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useImperativeHandle)(contextRef, () => context, [context]);\n    useIsomorphicLayoutEffect(() => {\n        if (!scrollRef.current) {\n            return;\n        }\n        if (getComputedStyle(scrollRef.current).overflow === \"visible\") {\n            scrollRef.current.style.overflow = \"auto\";\n        }\n    }, []);\n    return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(StickToBottomContext.Provider, { value: context, children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"div\", { ...props, children: typeof children === \"function\" ? children(context) : children }) }));\n}\n(function (StickToBottom) {\n    function Content({ children, ...props }) {\n        const context = useStickToBottomContext();\n        return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"div\", { ref: context.scrollRef, style: {\n                height: \"100%\",\n                width: \"100%\",\n            }, children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"div\", { ...props, ref: context.contentRef, children: typeof children === \"function\" ? children(context) : children }) }));\n    }\n    StickToBottom.Content = Content;\n})(StickToBottom || (StickToBottom = {}));\n/**\n * Use this hook inside a <StickToBottom> component to gain access to whether the component is at the bottom of the scrollable area.\n */\nfunction useStickToBottomContext() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(StickToBottomContext);\n    if (!context) {\n        throw new Error(\"use-stick-to-bottom component context must be used within a StickToBottom component\");\n    }\n    return context;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/use-stick-to-bottom/dist/StickToBottom.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/use-stick-to-bottom/dist/useStickToBottom.js":
/*!*******************************************************************!*\
  !*** ./node_modules/use-stick-to-bottom/dist/useStickToBottom.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useStickToBottom: () => (/* binding */ useStickToBottom)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/*!---------------------------------------------------------------------------------------------\n *  Copyright (c) StackBlitz. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\n\nconst DEFAULT_SPRING_ANIMATION = {\n    /**\n     * A value from 0 to 1, on how much to damp the animation.\n     * 0 means no damping, 1 means full damping.\n     *\n     * @default 0.7\n     */\n    damping: 0.7,\n    /**\n     * The stiffness of how fast/slow the animation gets up to speed.\n     *\n     * @default 0.05\n     */\n    stiffness: 0.05,\n    /**\n     * The inertial mass associated with the animation.\n     * Higher numbers make the animation slower.\n     *\n     * @default 1.25\n     */\n    mass: 1.25,\n};\nconst STICK_TO_BOTTOM_OFFSET_PX = 70;\nconst SIXTY_FPS_INTERVAL_MS = 1000 / 60;\nconst RETAIN_ANIMATION_DURATION_MS = 350;\nlet mouseDown = false;\nglobalThis.document?.addEventListener(\"mousedown\", () => {\n    mouseDown = true;\n});\nglobalThis.document?.addEventListener(\"mouseup\", () => {\n    mouseDown = false;\n});\nglobalThis.document?.addEventListener(\"click\", () => {\n    mouseDown = false;\n});\nconst useStickToBottom = (options = {}) => {\n    const [escapedFromLock, updateEscapedFromLock] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [isAtBottom, updateIsAtBottom] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(options.initial !== false);\n    const [isNearBottom, setIsNearBottom] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const optionsRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    optionsRef.current = options;\n    const isSelecting = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(() => {\n        if (!mouseDown) {\n            return false;\n        }\n        const selection = window.getSelection();\n        if (!selection || !selection.rangeCount) {\n            return false;\n        }\n        const range = selection.getRangeAt(0);\n        return (range.commonAncestorContainer.contains(scrollRef.current) ||\n            scrollRef.current?.contains(range.commonAncestorContainer));\n    }, []);\n    const setIsAtBottom = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((isAtBottom) => {\n        state.isAtBottom = isAtBottom;\n        updateIsAtBottom(isAtBottom);\n    }, []);\n    const setEscapedFromLock = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((escapedFromLock) => {\n        state.escapedFromLock = escapedFromLock;\n        updateEscapedFromLock(escapedFromLock);\n    }, []);\n    // biome-ignore lint/correctness/useExhaustiveDependencies: not needed\n    const state = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => {\n        let lastCalculation;\n        return {\n            escapedFromLock,\n            isAtBottom,\n            resizeDifference: 0,\n            accumulated: 0,\n            velocity: 0,\n            listeners: new Set(),\n            get scrollTop() {\n                return scrollRef.current?.scrollTop ?? 0;\n            },\n            set scrollTop(scrollTop) {\n                if (scrollRef.current) {\n                    scrollRef.current.scrollTop = scrollTop;\n                    state.ignoreScrollToTop = scrollRef.current.scrollTop;\n                }\n            },\n            get targetScrollTop() {\n                if (!scrollRef.current || !contentRef.current) {\n                    return 0;\n                }\n                return (scrollRef.current.scrollHeight - 1 - scrollRef.current.clientHeight);\n            },\n            get calculatedTargetScrollTop() {\n                if (!scrollRef.current || !contentRef.current) {\n                    return 0;\n                }\n                const { targetScrollTop } = this;\n                if (!options.targetScrollTop) {\n                    return targetScrollTop;\n                }\n                if (lastCalculation?.targetScrollTop === targetScrollTop) {\n                    return lastCalculation.calculatedScrollTop;\n                }\n                const calculatedScrollTop = Math.max(Math.min(options.targetScrollTop(targetScrollTop, {\n                    scrollElement: scrollRef.current,\n                    contentElement: contentRef.current,\n                }), targetScrollTop), 0);\n                lastCalculation = { targetScrollTop, calculatedScrollTop };\n                requestAnimationFrame(() => {\n                    lastCalculation = undefined;\n                });\n                return calculatedScrollTop;\n            },\n            get scrollDifference() {\n                return this.calculatedTargetScrollTop - this.scrollTop;\n            },\n            get isNearBottom() {\n                return this.scrollDifference <= STICK_TO_BOTTOM_OFFSET_PX;\n            },\n        };\n    }, []);\n    const scrollToBottom = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((scrollOptions = {}) => {\n        if (typeof scrollOptions === \"string\") {\n            scrollOptions = { animation: scrollOptions };\n        }\n        if (!scrollOptions.preserveScrollPosition) {\n            setIsAtBottom(true);\n        }\n        const waitElapsed = Date.now() + (Number(scrollOptions.wait) || 0);\n        const behavior = mergeAnimations(optionsRef.current, scrollOptions.animation);\n        const { ignoreEscapes = false } = scrollOptions;\n        let durationElapsed;\n        let startTarget = state.calculatedTargetScrollTop;\n        if (scrollOptions.duration instanceof Promise) {\n            scrollOptions.duration.finally(() => {\n                durationElapsed = Date.now();\n            });\n        }\n        else {\n            durationElapsed = waitElapsed + (scrollOptions.duration ?? 0);\n        }\n        const next = async () => {\n            const promise = new Promise(requestAnimationFrame).then(() => {\n                if (!state.isAtBottom) {\n                    state.animation = undefined;\n                    return false;\n                }\n                const { scrollTop } = state;\n                const tick = performance.now();\n                const tickDelta = (tick - (state.lastTick ?? tick)) / SIXTY_FPS_INTERVAL_MS;\n                state.animation || (state.animation = { behavior, promise, ignoreEscapes });\n                if (state.animation.behavior === behavior) {\n                    state.lastTick = tick;\n                }\n                if (isSelecting()) {\n                    return next();\n                }\n                if (waitElapsed > Date.now()) {\n                    return next();\n                }\n                if (scrollTop < Math.min(startTarget, state.calculatedTargetScrollTop)) {\n                    if (state.animation?.behavior === behavior) {\n                        if (behavior === \"instant\") {\n                            state.scrollTop = state.calculatedTargetScrollTop;\n                            return next();\n                        }\n                        state.velocity =\n                            (behavior.damping * state.velocity +\n                                behavior.stiffness * state.scrollDifference) /\n                                behavior.mass;\n                        state.accumulated += state.velocity * tickDelta;\n                        state.scrollTop += state.accumulated;\n                        if (state.scrollTop !== scrollTop) {\n                            state.accumulated = 0;\n                        }\n                    }\n                    return next();\n                }\n                if (durationElapsed > Date.now()) {\n                    startTarget = state.calculatedTargetScrollTop;\n                    return next();\n                }\n                state.animation = undefined;\n                /**\n                 * If we're still below the target, then queue\n                 * up another scroll to the bottom with the last\n                 * requested animatino.\n                 */\n                if (state.scrollTop < state.calculatedTargetScrollTop) {\n                    return scrollToBottom({\n                        animation: mergeAnimations(optionsRef.current, optionsRef.current.resize),\n                        ignoreEscapes,\n                        duration: Math.max(0, durationElapsed - Date.now()) || undefined,\n                    });\n                }\n                return state.isAtBottom;\n            });\n            return promise.then((isAtBottom) => {\n                requestAnimationFrame(() => {\n                    if (!state.animation) {\n                        state.lastTick = undefined;\n                        state.velocity = 0;\n                    }\n                });\n                return isAtBottom;\n            });\n        };\n        if (scrollOptions.wait !== true) {\n            state.animation = undefined;\n        }\n        if (state.animation?.behavior === behavior) {\n            return state.animation.promise;\n        }\n        return next();\n    }, [setIsAtBottom, isSelecting, state]);\n    const stopScroll = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(() => {\n        setEscapedFromLock(true);\n        setIsAtBottom(false);\n    }, [setEscapedFromLock, setIsAtBottom]);\n    const handleScroll = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(({ target }) => {\n        if (target !== scrollRef.current) {\n            return;\n        }\n        const { scrollTop, ignoreScrollToTop } = state;\n        let { lastScrollTop = scrollTop } = state;\n        state.lastScrollTop = scrollTop;\n        state.ignoreScrollToTop = undefined;\n        if (ignoreScrollToTop && ignoreScrollToTop > scrollTop) {\n            /**\n             * When the user scrolls up while the animation plays, the `scrollTop` may\n             * not come in separate events; if this happens, to make sure `isScrollingUp`\n             * is correct, set the lastScrollTop to the ignored event.\n             */\n            lastScrollTop = ignoreScrollToTop;\n        }\n        setIsNearBottom(state.isNearBottom);\n        /**\n         * Scroll events may come before a ResizeObserver event,\n         * so in order to ignore resize events correctly we use a\n         * timeout.\n         *\n         * @see https://github.com/WICG/resize-observer/issues/25#issuecomment-248757228\n         */\n        setTimeout(() => {\n            /**\n             * When theres a resize difference ignore the resize event.\n             */\n            if (state.resizeDifference || scrollTop === ignoreScrollToTop) {\n                return;\n            }\n            if (isSelecting()) {\n                setEscapedFromLock(true);\n                setIsAtBottom(false);\n                return;\n            }\n            const isScrollingDown = scrollTop > lastScrollTop;\n            const isScrollingUp = scrollTop < lastScrollTop;\n            if (state.animation?.ignoreEscapes) {\n                state.scrollTop = lastScrollTop;\n                return;\n            }\n            if (isScrollingUp) {\n                setEscapedFromLock(true);\n                setIsAtBottom(false);\n            }\n            if (isScrollingDown) {\n                setEscapedFromLock(false);\n            }\n            if (!state.escapedFromLock && state.isNearBottom) {\n                setIsAtBottom(true);\n            }\n        }, 1);\n    }, [setEscapedFromLock, setIsAtBottom, isSelecting, state]);\n    const handleWheel = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(({ target, deltaY }) => {\n        let element = target;\n        while (![\"scroll\", \"auto\"].includes(getComputedStyle(element).overflow)) {\n            if (!element.parentElement) {\n                return;\n            }\n            element = element.parentElement;\n        }\n        /**\n         * The browser may cancel the scrolling from the mouse wheel\n         * if we update it from the animation in meantime.\n         * To prevent this, always escape when the wheel is scrolled up.\n         */\n        if (element === scrollRef.current &&\n            deltaY < 0 &&\n            scrollRef.current.scrollHeight > scrollRef.current.clientHeight &&\n            !state.animation?.ignoreEscapes) {\n            setEscapedFromLock(true);\n            setIsAtBottom(false);\n        }\n    }, [setEscapedFromLock, setIsAtBottom, state]);\n    const scrollRef = useRefCallback((scroll) => {\n        scrollRef.current?.removeEventListener(\"scroll\", handleScroll);\n        scrollRef.current?.removeEventListener(\"wheel\", handleWheel);\n        scroll?.addEventListener(\"scroll\", handleScroll, { passive: true });\n        scroll?.addEventListener(\"wheel\", handleWheel, { passive: true });\n    }, []);\n    const contentRef = useRefCallback((content) => {\n        state.resizeObserver?.disconnect();\n        if (!content) {\n            return;\n        }\n        let previousHeight;\n        state.resizeObserver = new ResizeObserver(([entry]) => {\n            const { height } = entry.contentRect;\n            const difference = height - (previousHeight ?? height);\n            state.resizeDifference = difference;\n            /**\n             * Sometimes the browser can overscroll past the target,\n             * so check for this and adjust appropriately.\n             */\n            if (state.scrollTop > state.targetScrollTop) {\n                state.scrollTop = state.targetScrollTop;\n            }\n            setIsNearBottom(state.isNearBottom);\n            if (difference >= 0) {\n                /**\n                 * If it's a positive resize, scroll to the bottom when\n                 * we're already at the bottom.\n                 */\n                const animation = mergeAnimations(optionsRef.current, previousHeight\n                    ? optionsRef.current.resize\n                    : optionsRef.current.initial);\n                scrollToBottom({\n                    animation,\n                    wait: true,\n                    preserveScrollPosition: true,\n                    duration: animation === \"instant\" ? undefined : RETAIN_ANIMATION_DURATION_MS,\n                });\n            }\n            else {\n                /**\n                 * Else if it's a negative resize, check if we're near the bottom\n                 * if we are want to un-escape from the lock, because the resize\n                 * could have caused the container to be at the bottom.\n                 */\n                if (state.isNearBottom) {\n                    setEscapedFromLock(false);\n                    setIsAtBottom(true);\n                }\n            }\n            previousHeight = height;\n            /**\n             * Reset the resize difference after the scroll event\n             * has fired. Requires a rAF to wait for the scroll event,\n             * and a setTimeout to wait for the other timeout we have in\n             * resizeObserver in case the scroll event happens after the\n             * resize event.\n             */\n            requestAnimationFrame(() => {\n                setTimeout(() => {\n                    if (state.resizeDifference === difference) {\n                        state.resizeDifference = 0;\n                    }\n                }, 1);\n            });\n        });\n        state.resizeObserver?.observe(content);\n    }, []);\n    return {\n        contentRef,\n        scrollRef,\n        scrollToBottom,\n        stopScroll,\n        isAtBottom: isAtBottom || isNearBottom,\n        isNearBottom,\n        escapedFromLock,\n        state,\n    };\n};\nfunction useRefCallback(callback, deps) {\n    // biome-ignore lint/correctness/useExhaustiveDependencies: not needed\n    const result = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((ref) => {\n        result.current = ref;\n        return callback(ref);\n    }, deps);\n    return result;\n}\nconst animationCache = new Map();\nfunction mergeAnimations(...animations) {\n    const result = { ...DEFAULT_SPRING_ANIMATION };\n    let instant = false;\n    for (const animation of animations) {\n        if (animation === \"instant\") {\n            instant = true;\n            continue;\n        }\n        if (typeof animation !== \"object\") {\n            continue;\n        }\n        instant = false;\n        result.damping = animation.damping ?? result.damping;\n        result.stiffness = animation.stiffness ?? result.stiffness;\n        result.mass = animation.mass ?? result.mass;\n    }\n    const key = JSON.stringify(result);\n    if (!animationCache.has(key)) {\n        animationCache.set(key, Object.freeze(result));\n    }\n    return instant ? \"instant\" : animationCache.get(key);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/use-stick-to-bottom/dist/useStickToBottom.js\n");

/***/ })

};
;