"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./lib/ap3x-client.ts":
/*!****************************!*\
  !*** ./lib/ap3x-client.ts ***!
  \****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ap3xClient: () => (/* binding */ ap3xClient),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/**\n * AP3X Platform API Client\n * \n * Connects the frontend to the AP3X backend platform\n */ class AP3XClient {\n    /**\n   * Health check endpoint\n   */ async getHealth() {\n        const response = await fetch(\"\".concat(this.baseUrl, \"/api/health\"));\n        if (!response.ok) {\n            throw new Error(\"Health check failed: \".concat(response.statusText));\n        }\n        return response.json();\n    }\n    /**\n   * Get platform information\n   */ async getPlatformInfo() {\n        const response = await fetch(\"\".concat(this.baseUrl, \"/api/info\"));\n        if (!response.ok) {\n            throw new Error(\"Failed to get platform info: \".concat(response.statusText));\n        }\n        return response.json();\n    }\n    /**\n   * Get available agents\n   */ async getAgents() {\n        const response = await fetch(\"\".concat(this.baseUrl, \"/api/agents\"));\n        if (!response.ok) {\n            throw new Error(\"Failed to get agents: \".concat(response.statusText));\n        }\n        return response.json();\n    }\n    /**\n   * Get active sessions\n   */ async getSessions() {\n        const response = await fetch(\"\".concat(this.baseUrl, \"/api/sessions\"));\n        if (!response.ok) {\n            throw new Error(\"Failed to get sessions: \".concat(response.statusText));\n        }\n        return response.json();\n    }\n    /**\n   * Submit code editing request\n   */ async editCode(request) {\n        const response = await fetch(\"\".concat(this.baseUrl, \"/api/code/edit\"), {\n            method: 'POST',\n            headers: {\n                'Content-Type': 'application/json'\n            },\n            body: JSON.stringify(request)\n        });\n        if (!response.ok) {\n            throw new Error(\"Code edit failed: \".concat(response.statusText));\n        }\n        return response.json();\n    }\n    /**\n   * Connect to WebSocket for real-time updates\n   */ connectWebSocket(callbacks) {\n        if (false) {}\n        // Dynamically import socket.io-client for client-side only\n        __webpack_require__.e(/*! import() */ \"_app-pages-browser_node_modules_socket_io-client_build_esm_index_js\").then(__webpack_require__.bind(__webpack_require__, /*! socket.io-client */ \"(app-pages-browser)/./node_modules/socket.io-client/build/esm/index.js\")).then((param)=>{\n            let { io } = param;\n            this.socket = io(this.wsUrl);\n            this.socket.on('connect', ()=>{\n                var _callbacks_onConnect;\n                console.log('🔌 Connected to AP3X Platform');\n                (_callbacks_onConnect = callbacks.onConnect) === null || _callbacks_onConnect === void 0 ? void 0 : _callbacks_onConnect.call(callbacks);\n            });\n            this.socket.on('platform_status', (data)=>{\n                var _callbacks_onPlatformStatus;\n                console.log('📊 Platform Status:', data);\n                (_callbacks_onPlatformStatus = callbacks.onPlatformStatus) === null || _callbacks_onPlatformStatus === void 0 ? void 0 : _callbacks_onPlatformStatus.call(callbacks, data);\n            });\n            this.socket.on('agent_status_update', (data)=>{\n                var _callbacks_onAgentUpdate;\n                console.log('🤖 Agent Update:', data);\n                (_callbacks_onAgentUpdate = callbacks.onAgentUpdate) === null || _callbacks_onAgentUpdate === void 0 ? void 0 : _callbacks_onAgentUpdate.call(callbacks, data);\n            });\n            this.socket.on('session_completed', (data)=>{\n                var _callbacks_onSessionCompleted;\n                console.log('✅ Session Completed:', data);\n                (_callbacks_onSessionCompleted = callbacks.onSessionCompleted) === null || _callbacks_onSessionCompleted === void 0 ? void 0 : _callbacks_onSessionCompleted.call(callbacks, data);\n            });\n            this.socket.on('disconnect', ()=>{\n                var _callbacks_onDisconnect;\n                console.log('🔌 Disconnected from AP3X Platform');\n                (_callbacks_onDisconnect = callbacks.onDisconnect) === null || _callbacks_onDisconnect === void 0 ? void 0 : _callbacks_onDisconnect.call(callbacks);\n            });\n        });\n    }\n    /**\n   * Start a coding session\n   */ startCodingSession(sessionData) {\n        if (this.socket) {\n            this.socket.emit('start_coding_session', sessionData);\n        }\n    }\n    /**\n   * Disconnect WebSocket\n   */ disconnectWebSocket() {\n        if (this.socket) {\n            this.socket.disconnect();\n            this.socket = null;\n        }\n    }\n    /**\n   * Check if backend is available\n   */ async isBackendAvailable() {\n        try {\n            await this.getHealth();\n            return true;\n        } catch (error) {\n            console.warn('AP3X Backend not available:', error);\n            return false;\n        }\n    }\n    constructor(baseUrl){\n        this.socket = null;\n        // Use proxy route in browser, direct connection in Node.js\n        if (true) {\n            this.baseUrl = '/api/ap3x';\n            this.wsUrl = 'http://localhost:3000';\n        } else {}\n    }\n}\n// Export singleton instance\nconst ap3xClient = new AP3XClient();\n// Export class for custom instances\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (AP3XClient);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/ap3x-client.ts\n"));

/***/ })

});