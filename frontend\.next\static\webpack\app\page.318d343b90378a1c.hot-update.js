"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AIDevInterface)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _barrel_optimize_names_ArrowUp_Code_MessageSquare_Settings_User_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowUp,Code,MessageSquare,Settings,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowUp_Code_MessageSquare_Settings_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowUp,Code,MessageSquare,Settings,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowUp_Code_MessageSquare_Settings_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowUp,Code,MessageSquare,Settings,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/message-square.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowUp_Code_MessageSquare_Settings_User_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowUp,Code,MessageSquare,Settings,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/arrow-up.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowUp_Code_MessageSquare_Settings_User_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowUp,Code,MessageSquare,Settings,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/code.js\");\n/* harmony import */ var _components_admin_dashboard__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/admin-dashboard */ \"(app-pages-browser)/./components/admin-dashboard.tsx\");\n/* harmony import */ var _components_task_list__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/task-list */ \"(app-pages-browser)/./components/task-list.tsx\");\n/* harmony import */ var _components_streaming_preview__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/streaming-preview */ \"(app-pages-browser)/./components/streaming-preview.tsx\");\n/* harmony import */ var _components_conversation_threads__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/conversation-threads */ \"(app-pages-browser)/./components/conversation-threads.tsx\");\n/* harmony import */ var _components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/scroll-area */ \"(app-pages-browser)/./components/ui/scroll-area.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nfunction AIDevInterface() {\n    _s();\n    const [currentView, setCurrentView] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"main\");\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"preview\");\n    const [chatInput, setChatInput] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isStreaming, setIsStreaming] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedModel, setSelectedModel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"elite-fullstack\");\n    const [sidebarWidth, setSidebarWidth] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(400) // Default width for SSR\n    ;\n    const [isResizing, setIsResizing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Set proper sidebar width after hydration\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AIDevInterface.useEffect\": ()=>{\n            if (true) {\n                const calculatedWidth = Math.floor((window.innerWidth - 48) / 3);\n                setSidebarWidth(calculatedWidth);\n            }\n        }\n    }[\"AIDevInterface.useEffect\"], []);\n    const resizeRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        {\n            id: \"1\",\n            type: \"ai\",\n            content: \"Hello! I'm your AI coding assistant. I can help you build applications, write code, debug issues, and much more. What would you like to work on today?\",\n            timestamp: new Date(\"2024-01-01T10:00:00Z\")\n        },\n        {\n            id: \"2\",\n            type: \"user\",\n            content: \"I want to create a modern e-commerce platform with React and Node.js\",\n            timestamp: new Date(\"2024-01-01T10:02:00Z\")\n        },\n        {\n            id: \"3\",\n            type: \"ai\",\n            content: \"Perfect! I'll help you create a comprehensive e-commerce platform. Let me start by setting up the project structure and implementing the core components.\",\n            timestamp: new Date(\"2024-01-01T10:03:00Z\"),\n            tasks: [\n                {\n                    id: \"1\",\n                    title: \"Setting up React project structure\",\n                    status: \"completed\",\n                    type: \"component\"\n                },\n                {\n                    id: \"2\",\n                    title: \"Creating product catalog components\",\n                    status: \"running\",\n                    type: \"component\",\n                    progress: 75\n                },\n                {\n                    id: \"3\",\n                    title: \"Implementing shopping cart functionality\",\n                    status: \"pending\",\n                    type: \"component\"\n                },\n                {\n                    id: \"4\",\n                    title: \"Setting up Node.js API endpoints\",\n                    status: \"pending\",\n                    type: \"api\"\n                }\n            ]\n        }\n    ]);\n    const formatTime = (date)=>{\n        return date.toLocaleTimeString([], {\n            hour: \"2-digit\",\n            minute: \"2-digit\"\n        });\n    };\n    const handleSendMessage = ()=>{\n        if (!chatInput.trim()) return;\n        const newMessage = {\n            id: Date.now().toString(),\n            type: \"user\",\n            content: chatInput,\n            timestamp: new Date()\n        };\n        setMessages((prev)=>[\n                ...prev,\n                newMessage\n            ]);\n        setChatInput(\"\");\n        setIsStreaming(true);\n        // Simulate AI response\n        setTimeout(()=>{\n            const aiResponse = {\n                id: (Date.now() + 1).toString(),\n                type: \"ai\",\n                content: \"I'll help you with that! Let me start working on your request.\",\n                timestamp: new Date(),\n                tasks: [\n                    {\n                        id: Date.now().toString(),\n                        title: \"Processing your request\",\n                        status: \"running\",\n                        type: \"component\",\n                        progress: 30\n                    }\n                ]\n            };\n            setMessages((prev)=>[\n                    ...prev,\n                    aiResponse\n                ]);\n            setIsStreaming(false);\n        }, 2000);\n    };\n    const handleMouseDown = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"AIDevInterface.useCallback[handleMouseDown]\": (e)=>{\n            setIsResizing(true);\n            e.preventDefault();\n        }\n    }[\"AIDevInterface.useCallback[handleMouseDown]\"], []);\n    const handleMouseMove = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"AIDevInterface.useCallback[handleMouseMove]\": (e)=>{\n            if (!isResizing) return;\n            const newWidth = e.clientX - 16 // Account for padding\n            ;\n            setSidebarWidth(Math.max(300, Math.min(800, newWidth)));\n        }\n    }[\"AIDevInterface.useCallback[handleMouseMove]\"], [\n        isResizing\n    ]);\n    const handleMouseUp = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"AIDevInterface.useCallback[handleMouseUp]\": ()=>{\n            setIsResizing(false);\n        }\n    }[\"AIDevInterface.useCallback[handleMouseUp]\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AIDevInterface.useEffect\": ()=>{\n            if (isResizing) {\n                document.addEventListener(\"mousemove\", handleMouseMove);\n                document.addEventListener(\"mouseup\", handleMouseUp);\n                return ({\n                    \"AIDevInterface.useEffect\": ()=>{\n                        document.removeEventListener(\"mousemove\", handleMouseMove);\n                        document.removeEventListener(\"mouseup\", handleMouseUp);\n                    }\n                })[\"AIDevInterface.useEffect\"];\n            }\n        }\n    }[\"AIDevInterface.useEffect\"], [\n        isResizing,\n        handleMouseMove,\n        handleMouseUp\n    ]);\n    if (currentView === \"admin\") {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_admin_dashboard__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n            onBack: ()=>setCurrentView(\"main\")\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ai-dev-ecosystem\\\\app\\\\page.tsx\",\n            lineNumber: 183,\n            columnNumber: 12\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-screen w-full bg-[#000000] text-[#e5e5e5] font-sans text-sm flex flex-col overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_conversation_threads__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                isVisible: false,\n                onSelectThread: ()=>{}\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ai-dev-ecosystem\\\\app\\\\page.tsx\",\n                lineNumber: 188,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"h-14 bg-[#0a0a0a] border-b border-[#1a1a1a] flex items-center justify-between px-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-3\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-14 h-14 rounded-lg overflow-hidden flex items-center justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                        src: \"/logo.png\",\n                                        alt: \"AI Dev Ecosystem\",\n                                        className: \"w-full h-full object-contain\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ai-dev-ecosystem\\\\app\\\\page.tsx\",\n                                        lineNumber: 197,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ai-dev-ecosystem\\\\app\\\\page.tsx\",\n                                    lineNumber: 196,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ai-dev-ecosystem\\\\app\\\\page.tsx\",\n                                lineNumber: 195,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-1 text-xs text-[#666]\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Personal\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ai-dev-ecosystem\\\\app\\\\page.tsx\",\n                                        lineNumber: 206,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"/\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ai-dev-ecosystem\\\\app\\\\page.tsx\",\n                                        lineNumber: 207,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"The Orb\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ai-dev-ecosystem\\\\app\\\\page.tsx\",\n                                        lineNumber: 208,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ai-dev-ecosystem\\\\app\\\\page.tsx\",\n                                lineNumber: 205,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ai-dev-ecosystem\\\\app\\\\page.tsx\",\n                        lineNumber: 194,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                variant: \"ghost\",\n                                size: \"sm\",\n                                className: \"h-8 px-3 text-xs text-[#888] hover:text-white hover:bg-[#1a1a1a] rounded-md\",\n                                onClick: ()=>setCurrentView(\"admin\"),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowUp_Code_MessageSquare_Settings_User_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        className: \"w-3 h-3 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ai-dev-ecosystem\\\\app\\\\page.tsx\",\n                                        lineNumber: 218,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"Settings\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ai-dev-ecosystem\\\\app\\\\page.tsx\",\n                                lineNumber: 212,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                size: \"sm\",\n                                className: \"h-8 px-4 bg-white text-black hover:bg-gray-100 text-xs font-medium rounded-md\",\n                                children: \"Publish\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ai-dev-ecosystem\\\\app\\\\page.tsx\",\n                                lineNumber: 221,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-7 h-7 bg-[#1a1a1a] rounded-full flex items-center justify-center ml-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowUp_Code_MessageSquare_Settings_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    className: \"w-4 h-4 text-[#888]\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ai-dev-ecosystem\\\\app\\\\page.tsx\",\n                                    lineNumber: 228,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ai-dev-ecosystem\\\\app\\\\page.tsx\",\n                                lineNumber: 227,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ai-dev-ecosystem\\\\app\\\\page.tsx\",\n                        lineNumber: 211,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ai-dev-ecosystem\\\\app\\\\page.tsx\",\n                lineNumber: 193,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-1 overflow-hidden p-4 gap-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"aside\", {\n                        style: {\n                            width: sidebarWidth\n                        },\n                        className: \"bg-[#0a0a0a] rounded-xl flex flex-col overflow-hidden relative h-full shadow-xl\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"px-6 py-4 border-b border-[#1a1a1a] flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-2 h-2 bg-green-500 rounded-full\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ai-dev-ecosystem\\\\app\\\\page.tsx\",\n                                                lineNumber: 242,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"font-medium text-white text-sm\",\n                                                children: \"Chat\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ai-dev-ecosystem\\\\app\\\\page.tsx\",\n                                                lineNumber: 243,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ai-dev-ecosystem\\\\app\\\\page.tsx\",\n                                        lineNumber: 241,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: \"w-6 h-6 hover:bg-[#1a1a1a] rounded-md flex items-center justify-center transition-colors\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowUp_Code_MessageSquare_Settings_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"w-4 h-4 text-[#666]\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ai-dev-ecosystem\\\\app\\\\page.tsx\",\n                                                lineNumber: 247,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ai-dev-ecosystem\\\\app\\\\page.tsx\",\n                                            lineNumber: 246,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ai-dev-ecosystem\\\\app\\\\page.tsx\",\n                                        lineNumber: 245,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ai-dev-ecosystem\\\\app\\\\page.tsx\",\n                                lineNumber: 240,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 min-h-0 overflow-hidden\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_7__.ScrollArea, {\n                                    className: \"h-full\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"px-6 py-4 space-y-4\",\n                                        children: messages.map((message)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"group\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex gap-3 \".concat(message.type === \"user\" ? \"flex-row-reverse\" : \"\"),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex-shrink-0\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-8 h-8 rounded-full flex items-center justify-center text-xs font-medium \".concat(message.type === \"user\" ? \"bg-blue-600 text-white\" : \"bg-[#1a1a1a] text-[#888] border border-[#2a2a2a]\"),\n                                                                    children: message.type === \"user\" ? \"U\" : \"AI\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ai-dev-ecosystem\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 260,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ai-dev-ecosystem\\\\app\\\\page.tsx\",\n                                                                lineNumber: 259,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex-1 max-w-[85%] \".concat(message.type === \"user\" ? \"text-right\" : \"\"),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"inline-block px-4 py-3 rounded-lg text-sm leading-relaxed \".concat(message.type === \"user\" ? \"bg-blue-600 text-white shadow-lg\" : \"bg-[#111111] text-[#e5e5e5] shadow-md\"),\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"whitespace-pre-wrap\",\n                                                                            children: message.content\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ai-dev-ecosystem\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 278,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ai-dev-ecosystem\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 271,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-xs text-[#666] mt-1 \".concat(message.type === \"user\" ? \"text-right\" : \"\"),\n                                                                        children: formatTime(message.timestamp)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ai-dev-ecosystem\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 280,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ai-dev-ecosystem\\\\app\\\\page.tsx\",\n                                                                lineNumber: 270,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ai-dev-ecosystem\\\\app\\\\page.tsx\",\n                                                        lineNumber: 258,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    message.type === \"ai\" && message.tasks && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"ml-10\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_task_list__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                            tasks: message.tasks,\n                                                            title: \"Current Tasks\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ai-dev-ecosystem\\\\app\\\\page.tsx\",\n                                                            lineNumber: 289,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ai-dev-ecosystem\\\\app\\\\page.tsx\",\n                                                        lineNumber: 288,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, message.id, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ai-dev-ecosystem\\\\app\\\\page.tsx\",\n                                                lineNumber: 257,\n                                                columnNumber: 17\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ai-dev-ecosystem\\\\app\\\\page.tsx\",\n                                        lineNumber: 255,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ai-dev-ecosystem\\\\app\\\\page.tsx\",\n                                    lineNumber: 254,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ai-dev-ecosystem\\\\app\\\\page.tsx\",\n                                lineNumber: 253,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"shrink-0 p-3 bg-[#0a0a0a] shadow-inner\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative bg-[#111111] rounded-xl p-1 focus-within:shadow-blue-500/20 focus-within:shadow-lg transition-all shadow-md\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                            value: chatInput,\n                                            onChange: (e)=>{\n                                                setChatInput(e.target.value);\n                                                // Auto-resize textarea\n                                                const textarea = e.target;\n                                                textarea.style.height = 'auto';\n                                                const scrollHeight = textarea.scrollHeight;\n                                                const maxHeight = 240; // 10 lines * 24px line height\n                                                textarea.style.height = Math.min(scrollHeight, maxHeight) + 'px';\n                                            },\n                                            onKeyDown: (e)=>{\n                                                if (e.key === 'Enter' && (e.metaKey || e.ctrlKey)) {\n                                                    e.preventDefault();\n                                                    handleSendMessage();\n                                                }\n                                            },\n                                            placeholder: \"Message AI...\",\n                                            className: \"w-full bg-transparent px-4 py-3 pr-12 text-sm text-white placeholder:text-[#666] resize-none focus:outline-none leading-6 scrollbar-none overflow-y-auto\",\n                                            rows: 3,\n                                            style: {\n                                                minHeight: '84px',\n                                                maxHeight: '240px'\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ai-dev-ecosystem\\\\app\\\\page.tsx\",\n                                            lineNumber: 304,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: handleSendMessage,\n                                            disabled: !chatInput.trim(),\n                                            className: \"absolute right-2 top-3 w-8 h-8 bg-blue-600 hover:bg-blue-700 disabled:bg-[#333] disabled:text-[#666] rounded-lg flex items-center justify-center transition-colors shadow-md\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowUp_Code_MessageSquare_Settings_User_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ai-dev-ecosystem\\\\app\\\\page.tsx\",\n                                                lineNumber: 334,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ai-dev-ecosystem\\\\app\\\\page.tsx\",\n                                            lineNumber: 329,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ai-dev-ecosystem\\\\app\\\\page.tsx\",\n                                    lineNumber: 303,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ai-dev-ecosystem\\\\app\\\\page.tsx\",\n                                lineNumber: 302,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                ref: resizeRef,\n                                className: \"absolute top-0 right-0 w-1 h-full cursor-col-resize hover:bg-blue-500 transition-colors rounded-r-xl\",\n                                onMouseDown: handleMouseDown\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ai-dev-ecosystem\\\\app\\\\page.tsx\",\n                                lineNumber: 340,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ai-dev-ecosystem\\\\app\\\\page.tsx\",\n                        lineNumber: 235,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: \"flex-1 flex flex-col bg-[#0a0a0a] rounded-xl overflow-hidden shadow-xl\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"h-12 bg-[#0a0a0a] border-b border-[#1a1a1a] flex items-center px-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: \"flex items-center gap-2 px-3 py-1.5 text-xs rounded-md transition-colors \".concat(activeTab === \"preview\" ? \"bg-[#1a1a1a] text-white\" : \"text-[#666] hover:text-white hover:bg-[#111111]\"),\n                                            onClick: ()=>setActiveTab(\"preview\"),\n                                            children: \"Preview\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ai-dev-ecosystem\\\\app\\\\page.tsx\",\n                                            lineNumber: 352,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: \"flex items-center gap-2 px-3 py-1.5 text-xs rounded-md transition-colors \".concat(activeTab === \"code\" ? \"bg-[#1a1a1a] text-white\" : \"text-[#666] hover:text-white hover:bg-[#111111]\"),\n                                            onClick: ()=>setActiveTab(\"code\"),\n                                            children: \"Code\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ai-dev-ecosystem\\\\app\\\\page.tsx\",\n                                            lineNumber: 362,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ai-dev-ecosystem\\\\app\\\\page.tsx\",\n                                    lineNumber: 351,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ai-dev-ecosystem\\\\app\\\\page.tsx\",\n                                lineNumber: 350,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 bg-[#0a0a0a]\",\n                                children: activeTab === \"preview\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_streaming_preview__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    isStreaming: false,\n                                    streamContent: [],\n                                    onToggleStream: ()=>{}\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ai-dev-ecosystem\\\\app\\\\page.tsx\",\n                                    lineNumber: 378,\n                                    columnNumber: 17\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-full flex\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-64 bg-[#0a0a0a] border-r border-[#1a1a1a] flex flex-col\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"px-4 py-3 border-b border-[#1a1a1a] flex items-center justify-between\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-sm font-medium text-white\",\n                                                        children: \"Explorer\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ai-dev-ecosystem\\\\app\\\\page.tsx\",\n                                                        lineNumber: 384,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ai-dev-ecosystem\\\\app\\\\page.tsx\",\n                                                    lineNumber: 383,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-1 p-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs text-[#666] space-y-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"p-2 hover:bg-[#1a1a1a] rounded-lg cursor-pointer transition-colors\",\n                                                                children: \"\\uD83D\\uDCC1 src\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ai-dev-ecosystem\\\\app\\\\page.tsx\",\n                                                                lineNumber: 388,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"p-2 hover:bg-[#1a1a1a] rounded-lg cursor-pointer ml-4 transition-colors\",\n                                                                children: \"\\uD83D\\uDCC4 app.tsx\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ai-dev-ecosystem\\\\app\\\\page.tsx\",\n                                                                lineNumber: 389,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"p-2 hover:bg-[#1a1a1a] rounded-lg cursor-pointer ml-4 transition-colors\",\n                                                                children: \"\\uD83D\\uDCC4 index.css\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ai-dev-ecosystem\\\\app\\\\page.tsx\",\n                                                                lineNumber: 390,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ai-dev-ecosystem\\\\app\\\\page.tsx\",\n                                                        lineNumber: 387,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ai-dev-ecosystem\\\\app\\\\page.tsx\",\n                                                    lineNumber: 386,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ai-dev-ecosystem\\\\app\\\\page.tsx\",\n                                            lineNumber: 382,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-1 bg-[#0a0a0a] p-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"h-full flex items-center justify-center bg-[#111111] rounded-lg border border-[#1a1a1a]\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center text-[#666] p-8\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowUp_Code_MessageSquare_Settings_User_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                            className: \"w-12 h-12 mx-auto mb-4 opacity-50\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ai-dev-ecosystem\\\\app\\\\page.tsx\",\n                                                            lineNumber: 399,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm\",\n                                                            children: \"Code editor will appear here\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ai-dev-ecosystem\\\\app\\\\page.tsx\",\n                                                            lineNumber: 400,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs mt-2 opacity-70\",\n                                                            children: \"Select a file to start editing\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ai-dev-ecosystem\\\\app\\\\page.tsx\",\n                                                            lineNumber: 401,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ai-dev-ecosystem\\\\app\\\\page.tsx\",\n                                                    lineNumber: 398,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ai-dev-ecosystem\\\\app\\\\page.tsx\",\n                                                lineNumber: 397,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ai-dev-ecosystem\\\\app\\\\page.tsx\",\n                                            lineNumber: 396,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ai-dev-ecosystem\\\\app\\\\page.tsx\",\n                                    lineNumber: 380,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ai-dev-ecosystem\\\\app\\\\page.tsx\",\n                                lineNumber: 376,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ai-dev-ecosystem\\\\app\\\\page.tsx\",\n                        lineNumber: 348,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ai-dev-ecosystem\\\\app\\\\page.tsx\",\n                lineNumber: 233,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ai-dev-ecosystem\\\\app\\\\page.tsx\",\n        lineNumber: 187,\n        columnNumber: 5\n    }, this);\n}\n_s(AIDevInterface, \"a71vGjzytD+jDAoHQFTGwTArfUM=\");\n_c = AIDevInterface;\nvar _c;\n$RefreshReg$(_c, \"AIDevInterface\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/page.tsx\n"));

/***/ })

});