/**
 * AP3X Platform API Client
 * 
 * Connects the frontend to the AP3X backend platform
 */

export interface AP3XAgent {
  id: string
  type: string
  name: string
  status: 'online' | 'offline' | 'busy'
  capabilities: string[]
}

export interface AP3XSession {
  sessionId: string
  userId: string
  projectId: string
  status: 'active' | 'inactive'
  startTime: string
  agentCount: number
}

export interface CodeEditRequest {
  prompt: string
  files?: string[]
  useMultiAgent?: boolean
}

export interface CodeEditResponse {
  success: boolean
  data: {
    sessionId: string
    changes: Array<{
      path: string
      action: string
      description: string
    }>
    agentsUsed: string[]
    executionTime: number
  }
  timestamp: string
}

export interface PlatformInfo {
  name: string
  description: string
  version: string
  features: string[]
  status: string
  uptime: number
  environment: string
}

export interface HealthCheck {
  status: string
  timestamp: string
  version: string
  platform: string
  components: {
    api: string
    websockets: string
    platform: string
  }
}

class AP3XClient {
  private baseUrl: string
  private wsUrl: string
  private socket: any = null

  constructor(baseUrl?: string) {
    // Use proxy route in browser, direct connection in Node.js
    if (typeof window !== 'undefined') {
      this.baseUrl = '/api/ap3x'
      this.wsUrl = 'http://localhost:3000'
    } else {
      this.baseUrl = baseUrl || 'http://localhost:3000/api'
      this.wsUrl = (baseUrl || 'http://localhost:3000').replace('http', 'ws')
    }
  }

  /**
   * Health check endpoint
   */
  async getHealth(): Promise<HealthCheck> {
    const response = await fetch(`${this.baseUrl}/health`)
    if (!response.ok) {
      throw new Error(`Health check failed: ${response.statusText}`)
    }
    return response.json()
  }

  /**
   * Get platform information
   */
  async getPlatformInfo(): Promise<PlatformInfo> {
    const response = await fetch(`${this.baseUrl}/info`)
    if (!response.ok) {
      throw new Error(`Failed to get platform info: ${response.statusText}`)
    }
    return response.json()
  }

  /**
   * Get available agents
   */
  async getAgents(): Promise<{ success: boolean; data: AP3XAgent[]; timestamp: string }> {
    const response = await fetch(`${this.baseUrl}/agents`)
    if (!response.ok) {
      throw new Error(`Failed to get agents: ${response.statusText}`)
    }
    return response.json()
  }

  /**
   * Get active sessions
   */
  async getSessions(): Promise<{ success: boolean; data: AP3XSession[]; timestamp: string }> {
    const response = await fetch(`${this.baseUrl}/sessions`)
    if (!response.ok) {
      throw new Error(`Failed to get sessions: ${response.statusText}`)
    }
    return response.json()
  }

  /**
   * Submit code editing request
   */
  async editCode(request: CodeEditRequest): Promise<CodeEditResponse> {
    const response = await fetch(`${this.baseUrl}/code/edit`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(request),
    })

    if (!response.ok) {
      throw new Error(`Code edit failed: ${response.statusText}`)
    }

    return response.json()
  }

  /**
   * Connect to WebSocket for real-time updates
   */
  connectWebSocket(callbacks: {
    onConnect?: () => void
    onPlatformStatus?: (data: any) => void
    onAgentUpdate?: (data: any) => void
    onSessionCompleted?: (data: any) => void
    onDisconnect?: () => void
  }) {
    if (typeof window === 'undefined') return

    // Dynamically import socket.io-client for client-side only
    import('socket.io-client').then(({ io }) => {
      this.socket = io(this.wsUrl)

      this.socket.on('connect', () => {
        console.log('🔌 Connected to AP3X Platform')
        callbacks.onConnect?.()
      })

      this.socket.on('platform_status', (data: any) => {
        console.log('📊 Platform Status:', data)
        callbacks.onPlatformStatus?.(data)
      })

      this.socket.on('agent_status_update', (data: any) => {
        console.log('🤖 Agent Update:', data)
        callbacks.onAgentUpdate?.(data)
      })

      this.socket.on('session_completed', (data: any) => {
        console.log('✅ Session Completed:', data)
        callbacks.onSessionCompleted?.(data)
      })

      this.socket.on('disconnect', () => {
        console.log('🔌 Disconnected from AP3X Platform')
        callbacks.onDisconnect?.()
      })
    })
  }

  /**
   * Start a coding session
   */
  startCodingSession(sessionData: { sessionId: string; prompt?: string }) {
    if (this.socket) {
      this.socket.emit('start_coding_session', sessionData)
    }
  }

  /**
   * Disconnect WebSocket
   */
  disconnectWebSocket() {
    if (this.socket) {
      this.socket.disconnect()
      this.socket = null
    }
  }

  /**
   * Check if backend is available
   */
  async isBackendAvailable(): Promise<boolean> {
    try {
      await this.getHealth()
      return true
    } catch (error) {
      console.warn('AP3X Backend not available:', error)
      return false
    }
  }
}

// Export singleton instance
export const ap3xClient = new AP3XClient()

// Export class for custom instances
export default AP3XClient
